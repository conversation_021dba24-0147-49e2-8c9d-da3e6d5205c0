package dashboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Unified FinancialMap operations
func (m *mongoDB) FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	var financialMap dashboard.FinancialMap
	err := m.financialMapCollection.FindOne(ctx, bson.M{"userID": userID}).Decode(&financialMap)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "financial map not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find financial map", errors.Internal, err)
	}

	financialMap.ID = financialMap.ObjectID.Hex()
	return &financialMap, nil
}

func (m *mongoDB) SaveFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error {
	result, err := m.financialMapCollection.InsertOne(ctx, financialMap)
	if err != nil {
		return errors.New(errors.Repository, "failed to save financial map", errors.Internal, err)
	}

	financialMap.ObjectID = result.InsertedID.(primitive.ObjectID)
	financialMap.ID = financialMap.ObjectID.Hex()
	return nil
}

func (m *mongoDB) UpdateFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error {
	filter := bson.M{"userID": financialMap.UserID}
	update := bson.M{"$set": financialMap}

	opts := options.Update().SetUpsert(true)
	result, err := m.financialMapCollection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return errors.New(errors.Repository, "failed to update financial map", errors.Internal, err)
	}

	// If it was an upsert and a new document was created
	if result.UpsertedID != nil {
		financialMap.ObjectID = result.UpsertedID.(primitive.ObjectID)
		financialMap.ID = financialMap.ObjectID.Hex()
	}

	return nil
}

// IncomeSource operations
func (m *mongoDB) FindIncomeSource(ctx context.Context, id primitive.ObjectID) (*dashboard.IncomeSource, error) {
	// Find the financial map that contains this income source
	var financialMap dashboard.FinancialMap
	err := m.financialMapCollection.FindOne(ctx, bson.M{
		"incomeSources._id": id,
	}).Decode(&financialMap)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "income source not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find income source", errors.Internal, err)
	}

	// Find the specific income source within the financial map
	for _, source := range financialMap.IncomeSources {
		if source.ObjectID == id {
			source.ID = source.ObjectID.Hex()
			return source, nil
		}
	}

	return nil, errors.New(errors.Repository, "income source not found in financial map", errors.NotFound, nil)
}

func (m *mongoDB) FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	financialMap, err := m.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			return []*dashboard.IncomeSource{}, nil // Return empty slice if no financial map exists
		}
		return nil, err
	}

	// Convert slice to pointer slice
	result := make([]*dashboard.IncomeSource, len(financialMap.IncomeSources))
	for i := range financialMap.IncomeSources {
		financialMap.IncomeSources[i].ID = financialMap.IncomeSources[i].ObjectID.Hex()
		result[i] = financialMap.IncomeSources[i]
	}

	return result, nil
}

func (m *mongoDB) CreateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error {
	// This method is deprecated in favor of unified collection approach
	// Income sources should be created through UpdateFinancialMap
	return errors.New(errors.Repository, "CreateIncomeSource is deprecated - use UpdateFinancialMap instead", errors.NotImplemented, nil)
}

func (m *mongoDB) UpdateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error {
	// This method is deprecated in favor of unified collection approach
	// Income sources should be updated through UpdateFinancialMap
	return errors.New(errors.Repository, "UpdateIncomeSource is deprecated - use UpdateFinancialMap instead", errors.NotImplemented, nil)
}

func (m *mongoDB) DeleteIncomeSource(ctx context.Context, id primitive.ObjectID) error {
	// This method is deprecated in favor of unified collection approach
	// Income sources should be deleted through UpdateFinancialMap
	return errors.New(errors.Repository, "DeleteIncomeSource is deprecated - use UpdateFinancialMap instead", errors.NotImplemented, nil)
}

// StrategicFund operations
func (m *mongoDB) FindStrategicFund(ctx context.Context, userID string) (*dashboard.StrategicFund, error) {
	financialMap, err := m.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.New(errors.Repository, "emergency fund not found", errors.NotFound, err)
		}
		return nil, err
	}

	if financialMap.StrategicFund == nil {
		return nil, errors.New(errors.Repository, "emergency fund not found", errors.NotFound, nil)
	}

	financialMap.StrategicFund.ID = financialMap.StrategicFund.ObjectID.Hex()
	return financialMap.StrategicFund, nil
}

func (m *mongoDB) CreateStrategicFund(ctx context.Context, strategicFund *dashboard.StrategicFund) error {
	// This method is deprecated in favor of unified collection approach
	// Emergency funds should be created through UpdateFinancialMap
	return errors.New(errors.Repository, "CreateStrategicFund is deprecated - use UpdateFinancialMap instead", errors.NotImplemented, nil)
}

func (m *mongoDB) UpdateStrategicFund(ctx context.Context, strategicFund *dashboard.StrategicFund) error {
	// This method is deprecated in favor of unified collection approach
	// Emergency funds should be updated through UpdateFinancialMap
	return errors.New(errors.Repository, "UpdateStrategicFund is deprecated - use UpdateFinancialMap instead", errors.NotImplemented, nil)
}

// Investment operations
func (m *mongoDB) FindInvestment(ctx context.Context, id primitive.ObjectID) (*dashboard.Investment, error) {
	// Find the financial map that contains this investment
	var financialMap dashboard.FinancialMap
	err := m.financialMapCollection.FindOne(ctx, bson.M{
		"investments._id": id,
	}).Decode(&financialMap)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "investment not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find investment", errors.Internal, err)
	}

	// Find the specific investment within the financial map
	for _, investment := range financialMap.Investments {
		if investment.ObjectID == id {
			investment.ID = investment.ObjectID.Hex()
			return investment, nil
		}
	}

	return nil, errors.New(errors.Repository, "investment not found in financial map", errors.NotFound, nil)
}

func (m *mongoDB) FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error) {
	financialMap, err := m.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			return []*dashboard.Investment{}, nil // Return empty slice if no financial map exists
		}
		return nil, err
	}

	// Convert slice to pointer slice
	result := make([]*dashboard.Investment, len(financialMap.Investments))
	for i := range financialMap.Investments {
		financialMap.Investments[i].ID = financialMap.Investments[i].ObjectID.Hex()
		result[i] = financialMap.Investments[i]
	}

	return result, nil
}

func (m *mongoDB) CreateInvestment(ctx context.Context, investment *dashboard.Investment) error {
	// This method is deprecated in favor of unified collection approach
	// Investments should be created through UpdateFinancialMap
	return errors.New(errors.Repository, "CreateInvestment is deprecated - use UpdateFinancialMap instead", errors.NotImplemented, nil)
}

func (m *mongoDB) UpdateInvestment(ctx context.Context, investment *dashboard.Investment) error {
	// This method is deprecated in favor of unified collection approach
	// Investments should be updated through UpdateFinancialMap
	return errors.New(errors.Repository, "UpdateInvestment is deprecated - use UpdateFinancialMap instead", errors.NotImplemented, nil)
}

func (m *mongoDB) DeleteInvestment(ctx context.Context, id primitive.ObjectID) error {
	// This method is deprecated in favor of unified collection approach
	// Investments should be deleted through UpdateFinancialMap
	return errors.New(errors.Repository, "DeleteInvestment is deprecated - use UpdateFinancialMap instead", errors.NotImplemented, nil)
}

// Asset operations
func (m *mongoDB) FindAsset(ctx context.Context, id primitive.ObjectID) (*dashboard.Asset, error) {
	// Find the financial map that contains this asset
	var financialMap dashboard.FinancialMap
	err := m.financialMapCollection.FindOne(ctx, bson.M{
		"assets._id": id,
	}).Decode(&financialMap)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "asset not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find asset", errors.Internal, err)
	}

	// Find the specific asset within the financial map
	for _, asset := range financialMap.Assets {
		if asset.ObjectID == id {
			asset.ID = asset.ObjectID.Hex()
			return asset, nil
		}
	}

	return nil, errors.New(errors.Repository, "asset not found in financial map", errors.NotFound, nil)
}

func (m *mongoDB) FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error) {
	financialMap, err := m.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			return []*dashboard.Asset{}, nil // Return empty slice if no financial map exists
		}
		return nil, err
	}

	// Convert slice to pointer slice
	result := make([]*dashboard.Asset, len(financialMap.Assets))
	for i := range financialMap.Assets {
		financialMap.Assets[i].ID = financialMap.Assets[i].ObjectID.Hex()
		result[i] = financialMap.Assets[i]
	}

	return result, nil
}

func (m *mongoDB) CreateAsset(ctx context.Context, asset *dashboard.Asset) error {
	// This method is deprecated in favor of unified collection approach
	// Assets should be created through UpdateFinancialMap
	return errors.New(errors.Repository, "CreateAsset is deprecated - use UpdateFinancialMap instead", errors.NotImplemented, nil)
}

func (m *mongoDB) UpdateAsset(ctx context.Context, asset *dashboard.Asset) error {
	// This method is deprecated in favor of unified collection approach
	// Assets should be updated through UpdateFinancialMap
	return errors.New(errors.Repository, "UpdateAsset is deprecated - use UpdateFinancialMap instead", errors.NotImplemented, nil)
}

func (m *mongoDB) DeleteAsset(ctx context.Context, id primitive.ObjectID) error {
	// This method is deprecated in favor of unified collection approach
	// Assets should be deleted through UpdateFinancialMap
	return errors.New(errors.Repository, "DeleteAsset is deprecated - use UpdateFinancialMap instead", errors.NotImplemented, nil)
}

// NetWorthSnapshot operations are now handled by transaction-based calculation in service layer

// Specialized query methods for unified collection
// These methods allow querying specific components within the unified FinancialMap document

// FindIncomeSourceFromMap finds a specific income source by ID within the unified document
func (m *mongoDB) FindIncomeSourceFromMap(ctx context.Context, userID string, incomeSourceID primitive.ObjectID) (*dashboard.IncomeSource, error) {
	financialMap, err := m.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	for _, source := range financialMap.IncomeSources {
		if source.ObjectID == incomeSourceID {
			return source, nil
		}
	}

	return nil, errors.New(errors.Repository, "income source not found in financial map", errors.NotFound, nil)
}

// FindInvestmentFromMap finds a specific investment by ID within the unified document
func (m *mongoDB) FindInvestmentFromMap(ctx context.Context, userID string, investmentID primitive.ObjectID) (*dashboard.Investment, error) {
	financialMap, err := m.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	for _, investment := range financialMap.Investments {
		if investment.ObjectID == investmentID {
			return investment, nil
		}
	}

	return nil, errors.New(errors.Repository, "investment not found in financial map", errors.NotFound, nil)
}

// FindAssetFromMap finds a specific asset by ID within the unified document
func (m *mongoDB) FindAssetFromMap(ctx context.Context, userID string, assetID primitive.ObjectID) (*dashboard.Asset, error) {
	financialMap, err := m.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	for _, asset := range financialMap.Assets {
		if asset.ObjectID == assetID {
			return asset, nil
		}
	}

	return nil, errors.New(errors.Repository, "asset not found in financial map", errors.NotFound, nil)
}
