package dashboard

import (
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/labstack/echo/v4"
)

// FindFinancialIndependence returns the financial independence data for the authenticated user
func (dc *controller) FindFinancialIndependence() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		financialIndependence, err := dc.Service.FindFinancialIndependence(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, financialIndependence)
	}
}

// UpdateRetirementTargetAmount updates the retirement target amount for the authenticated user
func (dc *controller) UpdateRetirementTargetAmount() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var request UpdateRetirementTargetAmountRequest
		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		if err := dc.Service.UpdateRetirementTargetAmount(ctx, userToken.Uid, request.TargetAmount); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}
