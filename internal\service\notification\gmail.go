package notification

import (
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"golang.org/x/oauth2"
	"google.golang.org/api/gmail/v1"
	"google.golang.org/api/option"
)

// GmailNotifier implements the Notifier interface using Gmail API
type GmailNotifier struct {
	service *gmail.Service
	config  *Config
}

// NewGmailNotifier creates a new Gmail notifier
func NewGmailNotifier(config *Config) (*GmailNotifier, error) {
	if config.GmailClientID == "" || config.GmailClientSecret == "" || config.GmailRefreshToken == "" {
		return nil, errors.New(errors.Service, "Gmail OAuth credentials are required", errors.BadRequest, nil)
	}

	// Create OAuth2 config
	oauthConfig := &oauth2.Config{
		ClientID:     config.GmailClientID,
		ClientSecret: config.GmailClientSecret,
		Scopes:       []string{gmail.GmailSendScope},
		Endpoint: oauth2.Endpoint{
			AuthURL:  "https://accounts.google.com/o/oauth2/auth",
			TokenURL: "https://oauth2.googleapis.com/token",
		},
	}

	// Create token from refresh token
	token := &oauth2.Token{
		RefreshToken: config.GmailRefreshToken,
	}

	// Create HTTP client with OAuth2
	client := oauthConfig.Client(context.Background(), token)

	// Create Gmail service
	service, err := gmail.NewService(context.Background(), option.WithHTTPClient(client))
	if err != nil {
		return nil, errors.New(errors.Service, "failed to create Gmail service", errors.Internal, err)
	}

	return &GmailNotifier{
		service: service,
		config:  config,
	}, nil
}

// SendPasswordReset sends a password reset email using Gmail API
func (g *GmailNotifier) SendPasswordReset(ctx context.Context, recipientEmail, recipientName, resetLink string) error {
	subject := "Redefinir Senha - Dinbora"
	htmlContent := g.generatePasswordResetHTML(recipientName, resetLink)

	return g.sendEmail(ctx, recipientEmail, recipientName, subject, htmlContent)
}

// SendSubscriptionConfirmation sends a subscription confirmation email
func (g *GmailNotifier) SendSubscriptionConfirmation(ctx context.Context, recipientEmail, recipientName, planType string) error {
	subject := "Confirmação de Assinatura - Dinbora"
	htmlContent := g.generateSubscriptionConfirmationHTML(recipientName, planType)

	return g.sendEmail(ctx, recipientEmail, recipientName, subject, htmlContent)
}

// SendSubscriptionCancellation sends a subscription cancellation email
func (g *GmailNotifier) SendSubscriptionCancellation(ctx context.Context, recipientEmail, recipientName, planType string) error {
	subject := "Cancelamento de Assinatura - Dinbora"
	htmlContent := g.generateSubscriptionCancellationHTML(recipientName, planType)

	return g.sendEmail(ctx, recipientEmail, recipientName, subject, htmlContent)
}

// SendNewUserWelcome sends a welcome email to new users with password setup link
func (g *GmailNotifier) SendNewUserWelcome(ctx context.Context, recipientEmail, recipientName, resetLink, planType string) error {
	subject := "Bem-vindo ao Dinbora!"
	htmlContent := g.generateNewUserWelcomeHTML(recipientName, resetLink, planType)

	return g.sendEmail(ctx, recipientEmail, recipientName, subject, htmlContent)
}

// SendGenericEmail sends a generic email with custom content
func (g *GmailNotifier) SendGenericEmail(ctx context.Context, recipientEmail, recipientName, subject, content string) error {
	return g.sendEmail(ctx, recipientEmail, recipientName, subject, content)
}

// sendEmail is a helper method to send emails using Gmail API
func (g *GmailNotifier) sendEmail(ctx context.Context, recipientEmail, recipientName, subject, htmlContent string) error {
	// Create the email message
	message := g.createMessage(recipientEmail, recipientName, subject, htmlContent)

	// Send the email
	_, err := g.service.Users.Messages.Send("me", message).Context(ctx).Do()
	if err != nil {
		return errors.New(errors.Service, "failed to send email via Gmail", errors.Internal, err)
	}

	return nil
}

// createMessage creates a Gmail message
func (g *GmailNotifier) createMessage(recipientEmail, recipientName, subject, htmlContent string) *gmail.Message {
	// Create the email headers and body
	emailBody := fmt.Sprintf(
		"From: %s <%s>\r\n"+
			"To: %s <%s>\r\n"+
			"Subject: %s\r\n"+
			"MIME-Version: 1.0\r\n"+
			"Content-Type: text/html; charset=utf-8\r\n\r\n"+
			"%s",
		g.config.GmailSenderName, g.config.GmailSenderEmail,
		recipientName, recipientEmail,
		subject,
		htmlContent,
	)

	// Encode the message in base64
	encodedMessage := base64.URLEncoding.EncodeToString([]byte(emailBody))

	return &gmail.Message{
		Raw: encodedMessage,
	}
}

// HTML template generators
func (g *GmailNotifier) generatePasswordResetHTML(recipientName, resetLink string) string {
	// Get the current working directory
	wd, err := os.Getwd()
	if err != nil {
		// Fallback to hardcoded HTML if we can't read the template
		return g.generatePasswordResetHTMLFallback(recipientName, resetLink)
	}

	// Construct the path to the template file
	templatePath := filepath.Join(wd, "internal", "service", "notification", "html", "password_reset.html")

	// Read the template file
	templateContent, err := os.ReadFile(templatePath)
	if err != nil {
		// Fallback to hardcoded HTML if we can't read the template
		return g.generatePasswordResetHTMLFallback(recipientName, resetLink)
	}

	// Replace placeholders in the template
	htmlContent := string(templateContent)
	htmlContent = strings.ReplaceAll(htmlContent, "{{recipientName}}", recipientName)
	log.Println("Reset link: " + resetLink)
	htmlContent = strings.ReplaceAll(htmlContent, "{{{resetLink}}}", resetLink)

	// Debug: Check if replacement worked
	if strings.Contains(htmlContent, "{{{resetLink}}}") {
		log.Println("Warning: resetLink placeholder was not replaced in template")
	}

	return htmlContent
}

// Fallback function with hardcoded HTML
func (g *GmailNotifier) generatePasswordResetHTMLFallback(recipientName, resetLink string) string {
	return fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Redefinir Senha</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">Redefinir Senha - Dinbora</h2>
        <p>Olá %s,</p>
        <p>Você solicitou a redefinição de sua senha. Clique no link abaixo para criar uma nova senha:</p>
        <p style="margin: 20px 0;">
            <a href="%s" style="background-color: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Redefinir Senha</a>
        </p>
        <p>Se você não solicitou esta redefinição, pode ignorar este email.</p>
        <p>Atenciosamente,<br>Equipe Dinbora</p>
    </div>
</body>
</html>`, recipientName, resetLink)
}

func (g *GmailNotifier) generateSubscriptionConfirmationHTML(recipientName, planType string) string {
	return fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Confirmação de Assinatura</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #27ae60;">Assinatura Confirmada!</h2>
        <p>Olá %s,</p>
        <p>Sua assinatura do plano <strong>%s</strong> foi confirmada com sucesso!</p>
        <p>Agora você tem acesso a todos os recursos do seu plano.</p>
        <p>Atenciosamente,<br>Equipe Dinbora</p>
    </div>
</body>
</html>`, recipientName, planType)
}

func (g *GmailNotifier) generateSubscriptionCancellationHTML(recipientName, planType string) string {
	return fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Cancelamento de Assinatura</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #e74c3c;">Assinatura Cancelada</h2>
        <p>Olá %s,</p>
        <p>Sua assinatura do plano <strong>%s</strong> foi cancelada.</p>
        <p>Sentimos muito em vê-lo partir. Se precisar de ajuda, entre em contato conosco.</p>
        <p>Atenciosamente,<br>Equipe Dinbora</p>
    </div>
</body>
</html>`, recipientName, planType)
}

func (g *GmailNotifier) generateNewUserWelcomeHTML(recipientName, resetLink, planType string) string {
	return fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Bem-vindo ao Dinbora</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #3498db;">Bem-vindo ao Dinbora!</h2>
        <p>Olá %s,</p>
        <p>Bem-vindo ao Dinbora! Sua conta foi criada com sucesso para o plano <strong>%s</strong>.</p>
        <p>Para começar, você precisa definir sua senha. Clique no link abaixo:</p>
        <p style="margin: 20px 0;">
            <a href="%s" style="background-color: #27ae60; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Definir Senha</a>
        </p>
        <p>Estamos animados para tê-lo conosco!</p>
        <p>Atenciosamente,<br>Equipe Dinbora</p>
    </div>
</body>
</html>`, recipientName, planType, resetLink)
}
