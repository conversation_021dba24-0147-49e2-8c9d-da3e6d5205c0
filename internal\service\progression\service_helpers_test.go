package progression

import (
	"regexp"
	"strings"
)

// findContentMetadata parses a JSON string and returns maps of content identifiers to line numbers and next field presence
func findContentMetadata(jsonStr string) (map[string]int, map[string]bool) {
	lineInfo := make(map[string]int)
	contentWithNextField := make(map[string]bool)

	// Split the JSON string into lines, handling different line endings
	lines := strings.Split(strings.ReplaceAll(jsonStr, "\r\n", "\n"), "\n")

	// Regular expression to find "identifier": "some-value" pattern
	identifierRegex := regexp.MustCompile(`"identifier"\s*:\s*"([^"]+)"`)

	// Scan each line for identifiers
	for i, line := range lines {
		matches := identifierRegex.FindStringSubmatch(line)
		if len(matches) > 1 {
			identifier := matches[1]

			// Store the line number (1-based)
			lineInfo[identifier] = i + 1

			// Check if this content has both choices and next fields
			// We need to look ahead and behind to find the content object boundaries
			hasChoices := false
			hasNext := false
			contentStartLine := 0
			contentEndLine := 0

			// Look backward to find the start of the content object
			for j := i; j >= 0; j-- {
				if strings.Contains(lines[j], "{") {
					contentStartLine = j
					break
				}
			}

			// Look forward to find the end of the content object
			for j := i; j < len(lines); j++ {
				if strings.Contains(lines[j], "}") {
					contentEndLine = j
					break
				}
			}

			// Check if the content has choices and next fields
			if contentStartLine > 0 && contentEndLine > 0 {
				for j := contentStartLine; j <= contentEndLine; j++ {
					if strings.Contains(lines[j], "\"choices\":") {
						hasChoices = true
					}

					// Check for next field
					if strings.Contains(lines[j], "\"next\":") {
						// Check if we're inside a choices array
						insideChoices := false
						for k := contentStartLine; k < j; k++ {
							if strings.Contains(lines[k], "\"choices\":") {
								insideChoices = true
							}
							if insideChoices && strings.Contains(lines[k], "]") {
								insideChoices = false
							}
						}

						// Only count next fields that are not inside choices
						if !insideChoices {
							hasNext = true
						}
					}
				}
			}

			// If this content has both choices and next, store it in the map
			if hasChoices && hasNext {
				contentWithNextField[identifier] = true
				lineInfo[identifier] = contentStartLine + 1 // Line numbers are 1-based
			}
		}
	}

	return lineInfo, contentWithNextField
}
