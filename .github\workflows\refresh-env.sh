#!/bin/bash
# Script to refresh environment variables from AWS SSM Parameter Store
# Expected to be executed with sudo by the main deployment script.

# Print commands and exit on error
set -ex

# --- Configuration ---
# Define the target region explicitly - CRUCIAL FOR SSM EXECUTION
# Ensure this matches the region of your parameters and instance role policy
AWS_TARGET_REGION="sa-east-1"
# Target environment file read by systemd
ENV_FILE="/etc/dinbora.env"
# SSM Path for parameters
SSM_PATH="/dinbora/"
# Log file for this script's execution (useful for debugging SSM runs)
LOG_FILE="/tmp/refresh_env_$(date +%Y%m%d_%H%M%S).log"

# Redirect all output (stdout & stderr) to the log file
# Use 'exec > >(tee -a "$LOG_FILE") 2>&1' to also see output in SSM console if possible
exec > "$LOG_FILE" 2>&1

echo "--- Starting Environment Variable Refresh ---"
echo "Timestamp: $(date)"
echo "Script execution path: $0"
echo "Current User (effective): $(whoami)" # Should be root if called with sudo
echo "Target Env File: $ENV_FILE"
echo "Target Region: $AWS_TARGET_REGION"
echo "Target SSM Path: $SSM_PATH"

# --- Pre-checks ---
echo "[INFO] Checking PATH: $PATH"
echo "[INFO] Checking AWS CLI Path: $(which aws || echo 'aws not found!')"
echo "[INFO] Checking AWS CLI Version: $(aws --version || echo 'AWS CLI check failed!')"
echo "[INFO] Checking AWS Caller Identity (requires IAM Role permission)..."
# Explicitly set region for identity check as well
aws sts get-caller-identity --region "$AWS_TARGET_REGION" || echo "[WARN] Failed to get caller identity. Check IAM Role/Permissions/Region."

# --- Core Logic ---
# Remove the old environment file to ensure a clean state
echo "[INFO] Removing old environment file: $ENV_FILE"
# Use sudo explicitly here just in case the parent script didn't use it (though it should)
# Using 'rm -f' ignores error if file doesn't exist
rm -f "$ENV_FILE"

# Fetch latest environment variables from SSM Parameter Store
echo "[INFO] Fetching parameters from SSM path $SSM_PATH in region $AWS_TARGET_REGION"
TEMP_SSM_OUTPUT="/tmp/ssm_output.$$.txt" # Temporary file for SSM output

# Run AWS command, check exit code. Explicit region is critical.
# Exit code check ensures we don't process partial/failed output.
if ! aws ssm get-parameters-by-path \
  --region "$AWS_TARGET_REGION" \
  --path "$SSM_PATH" \
  --with-decryption \
  --query "Parameters[*].[Name,Value]" \
  --output text > "$TEMP_SSM_OUTPUT"; then
  AWS_CMD_EXIT_CODE=$?
  echo "[ERROR] AWS SSM get-parameters-by-path command failed with exit code $AWS_CMD_EXIT_CODE. Check log above and IAM permissions."
  rm -f "$TEMP_SSM_OUTPUT" # Clean up temp file on failure
  exit 1 # Exit script with error
fi

echo "[INFO] AWS SSM command successful. Processing parameters..."
PARAM_COUNT=0
# Process the downloaded parameters
# Using < "$TEMP_SSM_OUTPUT" redirection is slightly safer than pipe for loops
while IFS=$'\t' read -r name value || [ -n "$name" ]; do # Handle potential missing newline at EOF
  if [ -z "$name" ]; then
    echo "[WARN] Read empty parameter name, skipping line."
    continue
  fi
  # Extract the last part of the SSM path as the variable name
  var_name=$(basename "$name")
  echo "[DEBUG] Processing: Name='$name', BaseName='$var_name'"
  # Use printf for robust value quoting (handles special chars like ', ", $, etc.)
  printf "%s='%s'\n" "$var_name" "$value" >> "$ENV_FILE"
  echo "[DEBUG] Appended $var_name to $ENV_FILE"
  PARAM_COUNT=$((PARAM_COUNT + 1))
done < "$TEMP_SSM_OUTPUT"

# Clean up temporary file
rm -f "$TEMP_SSM_OUTPUT"
echo "[INFO] Processed $PARAM_COUNT parameters."

# --- Post-checks and Permissions ---
echo "[INFO] Verifying environment file: $ENV_FILE"
if [ ! -f "$ENV_FILE" ]; then
    echo "[ERROR] Environment file $ENV_FILE was NOT created!"
    exit 1
elif ! [ -s "$ENV_FILE" ]; then
    # File exists but is empty - could happen if no parameters found or write failed
    if [ $PARAM_COUNT -eq 0 ]; then
        echo "[WARN] Environment file $ENV_FILE is empty. No parameters found at path $SSM_PATH or processing failed."
        # Decide if this is an error or acceptable state
        # exit 1 # Uncomment to make this a fatal error
    else
        echo "[ERROR] Environment file $ENV_FILE exists but is empty despite parameters being processed. Check write permissions or script errors."
        exit 1
    fi
else
    echo "[INFO] $ENV_FILE created/updated and is not empty. Final contents:"
    # Use sudo to cat as the file is owned by root
    cat "$ENV_FILE"
fi

# Set final permissions and ownership (read by root for systemd, group readable by app user?)
echo "[INFO] Setting permissions (644) on $ENV_FILE"
chmod 644 "$ENV_FILE"
echo "[INFO] Setting ownership (root:ec2-user) on $ENV_FILE"
chown root:ec2-user "$ENV_FILE" # Adjust group if needed

echo "--- Environment Variable Refresh Script Finished Successfully ---"
# Log file is at $LOG_FILE on the instance

exit 0