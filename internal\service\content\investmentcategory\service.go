package investmentcategory

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/investmentcategory"
)

type Service interface {
	// Investment Category CRUD
	Create(ctx context.Context, investmentCategory *content.InvestmentCategory) error
	Find(ctx context.Context, id string) (*content.InvestmentCategory, error)
	FindAll(ctx context.Context) ([]*content.InvestmentCategory, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.InvestmentCategory, error)
	Update(ctx context.Context, investmentCategory *content.InvestmentCategory) error
	Delete(ctx context.Context, id string) error
}

type service struct {
	Repository investmentcategory.Repository
}

func New(repository investmentcategory.Repository) Service {
	return &service{
		Repository: repository,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, investmentCategory *content.InvestmentCategory) error {
	foundInvestmentCategory, err := s.Repository.FindByIdentifier(ctx, investmentCategory.Identifier)
	if err == nil && foundInvestmentCategory != nil {
		return errors.New(errors.Service, "investment category already exists", errors.Conflict, err)
	}

	if err = s.Repository.Create(ctx, investmentCategory); err != nil {
		return err
	}

	return nil
}

func (s *service) Find(ctx context.Context, id string) (*content.InvestmentCategory, error) {
	foundInvestmentCategory, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}
	foundInvestmentCategory.ID = foundInvestmentCategory.ObjectID.Hex()
	return foundInvestmentCategory, nil
}

func (s *service) FindAll(ctx context.Context) ([]*content.InvestmentCategory, error) {
	foundInvestmentCategories, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, foundInvestmentCategory := range foundInvestmentCategories {
		foundInvestmentCategory.ID = foundInvestmentCategory.ObjectID.Hex()
	}

	return foundInvestmentCategories, nil
}

func (s *service) FindByIdentifier(ctx context.Context, identifier string) (*content.InvestmentCategory, error) {
	foundInvestmentCategory, err := s.Repository.FindByIdentifier(ctx, identifier)
	if err != nil {
		return nil, err
	}
	foundInvestmentCategory.ID = foundInvestmentCategory.ObjectID.Hex()
	return foundInvestmentCategory, nil
}

func (s *service) Update(ctx context.Context, investmentCategory *content.InvestmentCategory) error {
	return s.Repository.Update(ctx, investmentCategory)
}

func (s *service) Delete(ctx context.Context, id string) error {
	return s.Repository.Delete(ctx, id)
}
