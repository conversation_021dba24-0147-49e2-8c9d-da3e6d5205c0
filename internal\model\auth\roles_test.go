package auth

import (
	"testing"
)

func TestIsValidRole(t *testing.T) {
	tests := []struct {
		name     string
		role     string
		expected bool
	}{
		{"Valid admin role", "admin", true},
		{"Valid director role", "director", true},
		{"Valid teacher role", "teacher", true},
		{"Valid student role", "student", true},
		{"Valid HR role", "human_resources", true},
		{"Valid user role", "user", true},
		{"Valid company HR role", "hr_company_123", true},
		{"Invalid role", "invalid_role", false},
		{"Empty role", "", false},
		{"Invalid company HR role", "hr_company_", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidRole(tt.role)
			if result != tt.expected {
				t.<PERSON>("IsValidRole(%s) = %v, want %v", tt.role, result, tt.expected)
			}
		})
	}
}

func TestGetRoleLevel(t *testing.T) {
	tests := []struct {
		name     string
		role     Role
		expected int
	}{
		{"Admin role", RoleAdmin, 5},
		{"Director role", RoleDirector, 4},
		{"HR role", <PERSON><PERSON><PERSON>Resources, 3},
		{"Teacher role", RoleTeacher, 2},
		{"Student role", RoleStudent, 1},
		{"User role", RoleUser, 1},
		{"Company HR role", "hr_company_123", 3},
		{"Unknown role", "unknown", 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetRoleLevel(tt.role)
			if result != tt.expected {
				t.Errorf("GetRoleLevel(%s) = %d, want %d", tt.role, result, tt.expected)
			}
		})
	}
}

func TestHasRole(t *testing.T) {
	userRoles := []string{"teacher", "hr_company_123"}

	tests := []struct {
		name       string
		userRoles  []string
		targetRole Role
		expected   bool
	}{
		{"Has teacher role", userRoles, RoleTeacher, true},
		{"Has company HR role", userRoles, "hr_company_123", true},
		{"Does not have admin role", userRoles, RoleAdmin, false},
		{"Does not have student role", userRoles, RoleStudent, false},
		{"Empty roles", []string{}, RoleAdmin, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := HasRole(tt.userRoles, tt.targetRole)
			if result != tt.expected {
				t.Errorf("HasRole(%v, %s) = %v, want %v", tt.userRoles, tt.targetRole, result, tt.expected)
			}
		})
	}
}

func TestHasAnyRole(t *testing.T) {
	userRoles := []string{"teacher", "hr_company_123"}

	tests := []struct {
		name        string
		userRoles   []string
		targetRoles []Role
		expected    bool
	}{
		{"Has one of multiple roles", userRoles, []Role{RoleAdmin, RoleTeacher}, true},
		{"Has none of multiple roles", userRoles, []Role{RoleAdmin, RoleStudent}, false},
		{"Has company HR role", userRoles, []Role{"hr_company_123", RoleAdmin}, true},
		{"Empty target roles", userRoles, []Role{}, false},
		{"Empty user roles", []string{}, []Role{RoleAdmin, RoleTeacher}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := HasAnyRole(tt.userRoles, tt.targetRoles...)
			if result != tt.expected {
				t.Errorf("HasAnyRole(%v, %v) = %v, want %v", tt.userRoles, tt.targetRoles, result, tt.expected)
			}
		})
	}
}

func TestHasMinimumRole(t *testing.T) {
	tests := []struct {
		name        string
		userRoles   []string
		minimumRole Role
		expected    bool
	}{
		{"Admin has minimum director", []string{"admin"}, RoleDirector, true},
		{"Director has minimum teacher", []string{"director"}, RoleTeacher, true},
		{"Teacher has minimum student", []string{"teacher"}, RoleStudent, true},
		{"Student does not have minimum teacher", []string{"student"}, RoleTeacher, false},
		{"HR has minimum teacher", []string{"human_resources"}, RoleTeacher, true},
		{"Company HR has minimum teacher", []string{"hr_company_123"}, RoleTeacher, true},
		{"Multiple roles with minimum", []string{"student", "teacher"}, RoleTeacher, true},
		{"Empty roles", []string{}, RoleStudent, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := HasMinimumRole(tt.userRoles, tt.minimumRole)
			if result != tt.expected {
				t.Errorf("HasMinimumRole(%v, %s) = %v, want %v", tt.userRoles, tt.minimumRole, result, tt.expected)
			}
		})
	}
}

func TestRoleCheckers(t *testing.T) {
	tests := []struct {
		name      string
		userRoles []string
		checker   func([]string) bool
		expected  bool
	}{
		{"IsAdmin with admin role", []string{"admin"}, IsAdmin, true},
		{"IsAdmin without admin role", []string{"teacher"}, IsAdmin, false},
		{"IsDirector with director role", []string{"director"}, IsDirector, true},
		{"IsTeacher with teacher role", []string{"teacher"}, IsTeacher, true},
		{"IsStudent with student role", []string{"student"}, IsStudent, true},
		{"IsUser with user role", []string{"user"}, IsUser, true},
		{"IsHumanResources with HR role", []string{"human_resources"}, IsHumanResources, true},
		{"IsHumanResources with company HR role", []string{"hr_company_123"}, IsHumanResources, true},
		{"IsHumanResources without HR role", []string{"teacher"}, IsHumanResources, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.checker(tt.userRoles)
			if result != tt.expected {
				t.Errorf("%s(%v) = %v, want %v", tt.name, tt.userRoles, result, tt.expected)
			}
		})
	}
}

func TestGetHRCompanyIDs(t *testing.T) {
	tests := []struct {
		name      string
		userRoles []string
		expected  []string
	}{
		{"Single company HR role", []string{"hr_company_123"}, []string{"123"}},
		{"Multiple company HR roles", []string{"hr_company_123", "hr_company_456"}, []string{"123", "456"}},
		{"Mixed roles with company HR", []string{"teacher", "hr_company_123", "student"}, []string{"123"}},
		{"No company HR roles", []string{"admin", "teacher"}, []string{}},
		{"Base HR role only", []string{"human_resources"}, []string{}},
		{"Invalid company HR role", []string{"hr_company_"}, []string{}},
		{"Empty roles", []string{}, []string{}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetHRCompanyIDs(tt.userRoles)
			if len(result) != len(tt.expected) {
				t.Errorf("GetHRCompanyIDs(%v) = %v, want %v", tt.userRoles, result, tt.expected)
				return
			}
			for i, id := range result {
				if id != tt.expected[i] {
					t.Errorf("GetHRCompanyIDs(%v) = %v, want %v", tt.userRoles, result, tt.expected)
					break
				}
			}
		})
	}
}

func TestCreateHRCompanyRole(t *testing.T) {
	tests := []struct {
		name      string
		companyID string
		expected  Role
	}{
		{"Valid company ID", "123", "hr_company_123"},
		{"Another company ID", "abc", "hr_company_abc"},
		{"Empty company ID", "", "hr_company_"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CreateHRCompanyRole(tt.companyID)
			if result != tt.expected {
				t.Errorf("CreateHRCompanyRole(%s) = %s, want %s", tt.companyID, result, tt.expected)
			}
		})
	}
}

func TestGetPermissions(t *testing.T) {
	tests := []struct {
		name      string
		userRoles []string
		expected  RolePermissions
	}{
		{
			"Admin permissions",
			[]string{"admin"},
			RolePermissions{
				CanAccessAllData:     true,
				CanAccessDepartment:  true,
				CanAccessOwnStudents: true,
				CanAccessOwnData:     true,
				CanAccessCompanyData: true,
			},
		},
		{
			"Director permissions",
			[]string{"director"},
			RolePermissions{
				CanAccessAllData:     false,
				CanAccessDepartment:  true,
				CanAccessOwnStudents: true,
				CanAccessOwnData:     true,
				CanAccessCompanyData: false,
			},
		},
		{
			"Teacher permissions",
			[]string{"teacher"},
			RolePermissions{
				CanAccessAllData:     false,
				CanAccessDepartment:  false,
				CanAccessOwnStudents: true,
				CanAccessOwnData:     true,
				CanAccessCompanyData: false,
			},
		},
		{
			"Student permissions",
			[]string{"student"},
			RolePermissions{
				CanAccessAllData:     false,
				CanAccessDepartment:  false,
				CanAccessOwnStudents: false,
				CanAccessOwnData:     true,
				CanAccessCompanyData: false,
			},
		},
		{
			"HR permissions",
			[]string{"human_resources"},
			RolePermissions{
				CanAccessAllData:     false,
				CanAccessDepartment:  false,
				CanAccessOwnStudents: false,
				CanAccessOwnData:     true,
				CanAccessCompanyData: true,
				CompanyIDs:           []string{},
			},
		},
		{
			"Company HR permissions",
			[]string{"hr_company_123"},
			RolePermissions{
				CanAccessAllData:     false,
				CanAccessDepartment:  false,
				CanAccessOwnStudents: false,
				CanAccessOwnData:     true,
				CanAccessCompanyData: true,
				CompanyIDs:           []string{"123"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetPermissions(tt.userRoles)

			if result.CanAccessAllData != tt.expected.CanAccessAllData {
				t.Errorf("CanAccessAllData: got %v, want %v", result.CanAccessAllData, tt.expected.CanAccessAllData)
			}
			if result.CanAccessDepartment != tt.expected.CanAccessDepartment {
				t.Errorf("CanAccessDepartment: got %v, want %v", result.CanAccessDepartment, tt.expected.CanAccessDepartment)
			}
			if result.CanAccessOwnStudents != tt.expected.CanAccessOwnStudents {
				t.Errorf("CanAccessOwnStudents: got %v, want %v", result.CanAccessOwnStudents, tt.expected.CanAccessOwnStudents)
			}
			if result.CanAccessOwnData != tt.expected.CanAccessOwnData {
				t.Errorf("CanAccessOwnData: got %v, want %v", result.CanAccessOwnData, tt.expected.CanAccessOwnData)
			}
			if result.CanAccessCompanyData != tt.expected.CanAccessCompanyData {
				t.Errorf("CanAccessCompanyData: got %v, want %v", result.CanAccessCompanyData, tt.expected.CanAccessCompanyData)
			}

			if len(result.CompanyIDs) != len(tt.expected.CompanyIDs) {
				t.Errorf("CompanyIDs length: got %d, want %d", len(result.CompanyIDs), len(tt.expected.CompanyIDs))
			}
		})
	}
}
