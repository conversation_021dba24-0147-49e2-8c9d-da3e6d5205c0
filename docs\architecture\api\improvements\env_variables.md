# Environment Variables Improvements

Caption: This document outlines architectural suggestions for improving the handling of environment variables in the codebase.
⏳ - Pending
✅ - Completed
🚧 - In Progress

## Environment Variables - ⏳
Issue: Environment variables are being retrieved directly from the os.Getenv function.
- This leads to a lot of repeated code and makes it hard to change the way environment variables are retrieved.
- Solution: Create a configuration package that handles environment variable retrieval.
- This will allow for a single place to change the way environment variables are retrieved and thus reduce code duplication and enhance maintainability.
