package league

import (
	"context"
	"math"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
)

// FindLeagueCard returns a simplified card representation of a league
func (s *service) FindLeagueCard(ctx context.Context, leagueID string, userID string) (*league.LeagueCard, error) {
	if leagueID == "" {
		return nil, errors.New(errors.Service, "league_id_required", errors.Validation, nil)
	}
	if userID == "" {
		return nil, errors.New(errors.Service, "user_id_required", errors.Validation, nil)
	}

	// Get the league with access control
	l, err := s.Repository.FindByIDLoggedUser(ctx, leagueID, userID)
	if err != nil {
		return nil, err
	}

	// Convert to card
	return s.leagueToCard(l, userID), nil
}

// FindAllLeaguesCards returns simplified card representations of all leagues a user is a member of
func (s *service) FindAllLeaguesCards(ctx context.Context, userID string) ([]*league.LeagueCard, error) {
	if userID == "" {
		return nil, errors.New(errors.Service, "user_id_required", errors.Validation, nil)
	}

	// Get all leagues the user is a member of
	leagues, err := s.Repository.FindAllLeagues(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Convert each league to a card
	cards := make([]*league.LeagueCard, 0, len(leagues))
	for _, l := range leagues {
		cards = append(cards, s.leagueToCard(l, userID))
	}

	return cards, nil
}

// Helper
// leagueToCard converts a League to a LeagueCard
func (s *service) leagueToCard(l *league.League, userID string) *league.LeagueCard {
	// Find the leader (member with highest transaction streak)
	var leader *league.LeagueMember
	var userMember *league.LeagueMember

	// Find the user and leader
	for i, member := range l.Members {
		// Keep a reference to the user's member data
		if member.UserID == userID {
			userMember = &l.Members[i]
		}

		// Find the leader (member with highest transaction streak)
		if leader == nil || member.TransactionStreak > leader.TransactionStreak {
			leader = &l.Members[i]
		}
	}

	// Create the card
	card := &league.LeagueCard{
		ObjectID:              l.ObjectID,
		ID:                    l.ID,
		Name:                  l.Name,
		BackgroundColor:       l.BackgroundColor,
		RemainingDaysInSeason: calculateRemainingDaysInSeason(l.CurrentSeason.EndDate),
	}

	// Set leader information if found
	if leader != nil {
		card.LeaderName = leader.UserName
		card.LeaderPhotoURL = leader.PhotoURL
		card.LeaderTransactionStreak = leader.TransactionStreak
	}

	// Set user information if found
	if userMember != nil {
		card.UserName = userMember.UserName
		card.UserPhotoURL = userMember.PhotoURL
		card.UserTransactionStreak = userMember.TransactionStreak
	}

	return card
}

// calculateRemainingDaysInSeason calculates the number of days remaining in a season
func calculateRemainingDaysInSeason(endDate time.Time) int {
	now := time.Now()
	if now.After(endDate) {
		return 0
	}

	// Calculate days between now and end date
	duration := endDate.Sub(now)
	days := int(math.Ceil(duration.Hours() / 24))
	return days
}
