package gamification

import (
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
	"github.com/stretchr/testify/assert"
)

func TestUserAchievement_CreateAchievement(t *testing.T) {
	// Test the model's CreateAchievement method
	userAchievement := &gamification.UserAchievement{
		UserID:       "test-user",
		Achievements: []*gamification.Achievement{},
	}

	// Test creating first achievement
	now := time.Now()
	userAchievement.CreateAchievement("DNA", now)

	assert.Len(t, userAchievement.Achievements, 1)
	assert.Equal(t, "DNA", userAchievement.Achievements[0].Identifier)
	assert.Equal(t, now, userAchievement.Achievements[0].EarnedAt)

	// Test creating duplicate achievement (should not add)
	userAchievement.CreateAchievement("DNA", time.Now())
	assert.Len(t, userAchievement.Achievements, 1) // Should still be 1

	// Test creating second achievement
	later := time.Now().Add(time.Hour)
	userAchievement.CreateAchievement("explorador", later)
	assert.Len(t, userAchievement.Achievements, 2)
}

func TestUserAchievement_HasAchievement(t *testing.T) {
	userAchievement := &gamification.UserAchievement{
		UserID: "test-user",
		Achievements: []*gamification.Achievement{
			{Identifier: "DNA", EarnedAt: time.Now()},
		},
	}

	assert.True(t, userAchievement.HasAchievement("DNA"))
	assert.False(t, userAchievement.HasAchievement("explorador"))
}

func TestUserAchievement_FindAchievement(t *testing.T) {
	now := time.Now()
	userAchievement := &gamification.UserAchievement{
		UserID: "test-user",
		Achievements: []*gamification.Achievement{
			{Identifier: "DNA", EarnedAt: now},
		},
	}

	// Test finding existing achievement
	found := userAchievement.FindAchievement("DNA")
	assert.NotNil(t, found)
	assert.Equal(t, "DNA", found.Identifier)
	assert.Equal(t, now, found.EarnedAt)

	// Test finding non-existing achievement
	notFound := userAchievement.FindAchievement("explorador")
	assert.Nil(t, notFound)
}
