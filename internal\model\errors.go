package model

import (
	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

// User errors
var (
	ErrUserRequiredPassword  = errors.OldError("user password is a required field", errors.Validation, nil)
	ErrUserRequiredEmail     = errors.OldError("user email is a required field", errors.Validation, nil)
	ErrUserRequiredName      = errors.OldError("user name is a required field", errors.Validation, nil)
	ErrUserInvalidPassword   = errors.OldError("password must have at least 6 characters, being a lower and upper letter, a number and a special character", errors.Validation, nil)
	ErrUserInvalidEmail      = errors.OldError("user provided email is invalid", errors.Conflict, nil)
	ErrUserInvalidId         = errors.OldError("user id is invalid", errors.Conflict, nil)
	ErrInvalidUserOrPassword = errors.OldError("invalid user or password", errors.Validation, nil)
	ErrCannotAddAdminGroup   = errors.OldError("cannot add admin user group", errors.Unauthorized, nil)
)

var (
	ErrVaultInvalidId   = errors.OldError("invalid vault id", errors.Validation, nil)
	ErrVaultInvalidUser = errors.OldError("user is a required field", errors.Validation, nil)
)

var (
	ErrInvalidDeleteReason = errors.OldError("invalid delete reason type", errors.Validation, nil)
)
