package trail

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (m mongoDB) CreateRegularTrail(ctx context.Context, content *content.Trail) error {
	_, err := m.collection.InsertOne(ctx, content)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "trail already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create trail", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) FindRegularTrail(ctx context.Context, id string) (*content.Trail, error) {
	trailObjectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid trail ID format", errors.Validation, err)
	}

	// Filter for specific trail ID and no access control
	filter := bson.D{
		{Key: "_id", Value: trailObjectID},
		{Key: "$or", Value: []bson.D{
			{{Key: "accessControl", Value: bson.D{{Key: "$exists", Value: false}}}},
			{{Key: "accessControl", Value: nil}},
		}},
	}

	var trailContent content.Trail
	if err = m.collection.FindOne(ctx, filter).Decode(&trailContent); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "regular trail not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find regular trail", errors.Internal, err)
	}

	return &trailContent, nil
}

func (m mongoDB) FindAllRegularTrails(ctx context.Context) ([]*content.Trail, error) {
	// Filter where accessControl is null, missing, or empty
	filter := bson.D{
		{Key: "$or", Value: []bson.D{
			{{Key: "accessControl", Value: bson.D{{Key: "$exists", Value: false}}}},
			{{Key: "accessControl", Value: nil}},
		}},
	}

	cursor, err := m.collection.Find(ctx, filter)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query regular trails", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var trails []*content.Trail
	for cursor.Next(ctx) {
		var trail content.Trail
		if err = cursor.Decode(&trail); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode regular trail", errors.Internal, err)
		}
		trails = append(trails, &trail)
	}

	return trails, nil
}

func (m mongoDB) FindRegularTrailByIdentifier(ctx context.Context, identifier string) (*content.Trail, error) {
	// Filter for identifier and no access control
	filter := bson.D{
		{Key: "identifier", Value: identifier},
		{Key: "$or", Value: []bson.D{
			{{Key: "accessControl", Value: bson.D{{Key: "$exists", Value: false}}}},
			{{Key: "accessControl", Value: nil}},
		}},
	}

	var trailContent content.Trail
	if err := m.collection.FindOne(ctx, filter).Decode(&trailContent); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "regular trail not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find regular trail by identifier", errors.Internal, err)
	}

	return &trailContent, nil
}

func (m mongoDB) UpdateRegularTrail(ctx context.Context, content *content.Trail) error {
	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: content.ObjectID}},
		primitive.M{"$set": content})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "trail already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update trail", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "trail not found", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) DeleteRegularTrail(ctx context.Context, id string) error {
	trailObjectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid trail ID format", errors.Validation, err)
	}

	result, err := m.collection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: trailObjectID}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete trail", errors.Internal, err)
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "trail not found", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) FindRegularTrailCardData(ctx context.Context, id string) (*content.TrailCard, error) {
	trailObjectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid trail ID format", errors.Validation, err)
	}

	// Filter for specific trail ID and no access control
	filter := bson.D{
		{Key: "_id", Value: trailObjectID},
		{Key: "$or", Value: []bson.D{
			{{Key: "accessControl", Value: bson.D{{Key: "$exists", Value: false}}}},
			{{Key: "accessControl", Value: nil}},
		}},
	}

	// Set options to optimize query performance
	findOptions := options.FindOne()

	// Add projection to include only necessary fields for card data
	projection := bson.D{
		{Key: "name", Value: 1},
		{Key: "identifier", Value: 1},
		{Key: "level", Value: 1},
		{Key: "logo", Value: 1},
		{Key: "color", Value: 1},
		{Key: "requirements", Value: 1},
		{Key: "description", Value: 1},
		{Key: "logoUrl", Value: 1},
		{Key: "themeColor", Value: 1},
	}
	findOptions.SetProjection(projection)

	var cardData content.TrailCard
	if err = m.collection.FindOne(ctx, filter, findOptions).Decode(&cardData); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "regular trail not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find regular trail card data", errors.Internal, err)
	}

	cardData.ID = cardData.ObjectID.Hex()

	return &cardData, nil
}

func (m mongoDB) FindAllRegularTrailsCardData(ctx context.Context) ([]*content.TrailCard, error) {
	// Filter where accessControl is null, missing, or empty
	filter := bson.D{
		{Key: "$or", Value: []bson.D{
			{{Key: "accessControl", Value: bson.D{{Key: "$exists", Value: false}}}},
			{{Key: "accessControl", Value: nil}},
		}},
	}

	// Set options to optimize query performance
	findOptions := options.Find()

	// Add projection to include only necessary fields for card data
	projection := bson.D{
		{Key: "name", Value: 1},
		{Key: "identifier", Value: 1},
		{Key: "level", Value: 1},
		{Key: "logo", Value: 1},
		{Key: "color", Value: 1},
		{Key: "requirements", Value: 1},
		{Key: "description", Value: 1},
		{Key: "logoUrl", Value: 1},
		{Key: "themeColor", Value: 1},
	}
	findOptions.SetProjection(projection)

	cursor, err := m.collection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query regular trail cards", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var cardDataList []*content.TrailCard
	for cursor.Next(ctx) {
		var cardData content.TrailCard
		if err = cursor.Decode(&cardData); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode regular trail card data", errors.Internal, err)
		}

		cardData.ID = cardData.ObjectID.Hex()
		cardDataList = append(cardDataList, &cardData)
	}

	return cardDataList, nil
}
