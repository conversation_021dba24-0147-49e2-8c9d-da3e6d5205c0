# Recurring Transactions Feature

## Overview

The recurring transactions feature allows users to create a transaction that automatically replicates across multiple months. This is useful for recurring income (like salary) or recurring expenses (like rent, subscriptions, etc.).

## API Endpoint

**POST** `/v2/financialsheets/transactions/recurring`

### Request Body

```json
{
  "category": "compensation",
  "moneySource": 1,
  "value": 500000,
  "date": "2024-03-15T10:00:00Z",
  "paymentMethod": 1,
  "type": "income",
  "attachedDreamID": "",
  "recurrenceMonths": [4, 5, 6]
}
```

### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `category` | string | Yes | Category identifier for the transaction |
| `moneySource` | integer | Yes | Money source identifier (1-6) |
| `value` | integer | Yes | Transaction amount in cents (minimum 1) |
| `date` | string | Yes | ISO 8601 date for the original transaction |
| `paymentMethod` | integer | Yes | Payment method identifier (1-6) |
| `type` | string | Yes | Transaction type: "income", "costs_of_living", or "expense" |
| `attachedDreamID` | string | No | Optional dream ID if transaction is related to a dream |
| `recurrenceMonths` | array | Yes | Array of month numbers (1-12) for recurring transactions |

### Validation Rules

1. **recurrenceMonths** must contain at least one month
2. Each month must be between 1 and 12
3. No duplicate months allowed
4. Recurrence months cannot include the same month as the original transaction
5. All standard transaction validation rules apply

### Response

Returns the updated financial record with all created transactions (original + recurring).

```json
{
  "id": "user_record_id",
  "userID": "user_id",
  "userName": "User Name",
  "points": { ... },
  "yearData": {
    "2024": {
      "03": {
        "categories": [...],
        "transactions": [
          {
            "id": "transaction_id_1",
            "category": "compensation",
            "value": 500000,
            "date": "2024-03-15T10:00:00Z",
            ...
          }
        ]
      },
      "04": {
        "categories": [...],
        "transactions": [
          {
            "id": "transaction_id_2",
            "category": "compensation",
            "value": 500000,
            "date": "2024-04-15T10:00:00Z",
            ...
          }
        ]
      },
      ...
    }
  },
  ...
}
```

## Date Calculation Logic

The system calculates recurring transaction dates based on the original transaction date:

- **Original Date**: March 15, 2024
- **Recurrence Months**: [4, 5, 6] (April, May, June)
- **Result**:
  - April 15, 2024
  - May 15, 2024
  - June 15, 2024

### Year Handling

- If recurrence month > original month: Use same year
- If recurrence month ≤ original month: Use next year

**Example**: Original date is March 15, 2024, recurrence months [1, 2, 6]:
- January 15, 2025 (next year)
- February 15, 2025 (next year)
- June 15, 2024 (same year)

### Edge Cases

The system handles invalid dates automatically using Go's time package:
- January 31 → February 31 becomes March 2/3 (depending on leap year)
- This ensures all recurring transactions have valid dates

## Use Cases

### Monthly Salary
```json
{
  "category": "compensation",
  "value": 500000,
  "date": "2024-03-15T00:00:00Z",
  "recurrenceMonths": [4, 5, 6, 7, 8, 9, 10, 11, 12]
}
```

### Quarterly Rent
```json
{
  "category": "housing",
  "value": 150000,
  "date": "2024-01-01T00:00:00Z",
  "recurrenceMonths": [4, 7, 10]
}
```

### Annual Subscription
```json
{
  "category": "subscriptions",
  "value": 12000,
  "date": "2024-01-15T00:00:00Z",
  "recurrenceMonths": [1]
}
```

## Error Responses

### Validation Errors

```json
{
  "error": "validation failed",
  "details": "invalid month in recurrence array: must be between 1 and 12"
}
```

### Business Logic Errors

```json
{
  "error": "recurrence month cannot be the same as original transaction month"
}
```

## Implementation Notes

- Each recurring transaction is created as a separate transaction with its own ID
- All transactions update the financial record's totals and category summaries
- The feature integrates with the existing league system for streak tracking
- Transactions are created in the order specified in the recurrenceMonths array
- If any recurring transaction fails, the entire operation is rolled back
