package progression

// ProgressionType represents the type of progression (lesson or challenge)
type ProgressionType string

const (
	// ProgressionTypeLesson represents a lesson progression
	ProgressionTypeLesson ProgressionType = "LESSON"

	// ProgressionTypeChallenge represents a challenge progression
	ProgressionTypeChallenge ProgressionType = "CHALLENGE"
)

// RewardType represents the type of reward coin
type RewardType string

const (
	// RewardTypeCoin represents a coin reward
	RewardTypeCoin RewardType = "coin"
)
