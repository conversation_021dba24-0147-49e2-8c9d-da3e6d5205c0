# Role-Based Access Control (RBAC) Integration Guide

This guide explains how to integrate the new role-based access control system with existing endpoints in the dinbora-backend project.

## Overview

The RBAC system provides:
- **Role Definition**: Centralized role constants with hierarchical permissions
- **JWT Enhancement**: Tokens include complete roles list while maintaining backward compatibility
- **Middleware Chain**: AuthGuard → UserContextMiddleware → RoleGuard
- **Service Layer**: Role-based filtering and business logic
- **Clean Architecture**: Separation of concerns across all layers

## Quick Start

### 1. Update Service Dependencies

Add the RBAC service to your service constructors:

```go
// In internal/api/service/service.go
func NewServiceRegistry() *ServiceRegistry {
    rbacService := rbac.NewService()
    
    userService := user.New(
        userRepo,
        dreamboardService,
        financialDNAService,
        financialSheetService,
        progressionService,
        vaultService,
        contractService,
        walletService,
        customerService,
        rbacService, // Add RBAC service
    )
    
    return &ServiceRegistry{
        User: userService,
        RBAC: rbacService,
        // ... other services
    }
}
```

### 2. Apply Middleware Chain

Update your route registration to use the new middleware:

```go
// Example route registration
func RegisterUserRoutes(group *echo.Group) {
    // Middleware chain
    authChain := middlewares.AuthGuard()
    userContextChain := middlewares.UserContextMiddleware()
    
    // Self-service endpoints
    group.GET("/me", userController.Find(), authChain, userContextChain)
    
    // Role-based endpoints
    selfOrAdmin := middlewares.SelfOrAdmin()
    group.GET("/users/:id", userController.FindWithRoleFilter(), authChain, userContextChain, selfOrAdmin)
    
    // Admin-only endpoints
    adminOnly := middlewares.AdminOnly()
    group.GET("/users", userController.FindAllWithRoleFilter(), authChain, userContextChain, adminOnly)
    
    // Teacher endpoints
    teacherOrAbove := middlewares.TeacherOrAbove()
    group.GET("/my-students", userController.GetMyStudents(), authChain, userContextChain, teacherOrAbove)
    
    // HR endpoints
    hrOnly := middlewares.HROnly()
    group.GET("/company-users", userController.GetCompanyUsers(), authChain, userContextChain, hrOnly)
}
```

### 3. Update Controller Methods

Modify your controller methods to use the new context utilities:

```go
func (uc *controller) FindUser() echo.HandlerFunc {
    return func(c echo.Context) error {
        ctx := c.Request().Context()
        
        // Get user context from middleware
        userCtx, err := middlewares.GetUserContext(c)
        if err != nil {
            return err
        }
        
        targetUserID := c.Param("id")
        
        // Use role-based filtering
        user, err := uc.Service.FindWithRoleFilter(ctx, targetUserID, userCtx.UserID, userCtx.Roles)
        if err != nil {
            return err
        }
        
        return c.JSON(http.StatusOK, user.Sanitize())
    }
}
```

## Role Definitions

### Role Hierarchy

The system implements a hierarchical role structure with 5 privilege levels (ordered from highest to lowest):

```go
const (
    // Level 5 - Highest privilege
    RoleAdmin          = "admin"           // Full system access with no restrictions

    // Level 4
    RoleDirector       = "director"        // Department/organization-wide access

    // Level 3
    RoleHumanResources = "human_resources" // HR-related data within company scope

    // Level 2
    RoleTeacher        = "teacher"         // Own records and assigned students

    // Level 1 - Equal privilege, different contexts
    RoleStudent        = "student"         // Own data only (educational context)
    RoleUser           = "user"            // Own data only (general platform user)
)
```

### Role Hierarchy Map

```go
var RoleHierarchy = map[Role]int{
    RoleAdmin:          5, // Highest privilege
    RoleDirector:       4,
    RoleHumanResources: 3,
    RoleTeacher:        2,
    RoleStudent:        1, // Educational context users
    RoleUser:           1, // General platform users (same level as student)
}
```

### Student vs User Roles

Both `student` and `user` roles have the same permission level (level 1) but serve different contexts:

- **`student`**: Users in educational contexts (courses, trails, learning content)
- **`user`**: General platform users (financial tools, personal features)

```go
// Both have same access level but different contexts
auth.GetRoleLevel(auth.RoleStudent) // 1
auth.GetRoleLevel(auth.RoleUser)    // 1

// Teachers can access both students and general users
teacherPermissions := auth.GetPermissions([]string{"teacher"})
// teacherPermissions.CanAccessOwnStudents = true (includes both student and user roles)
```

### Company-Specific HR Roles

The system supports granular HR access control through company-specific roles:

```go
// Create company-specific HR role
hrRole := auth.CreateHRCompanyRole("123") // "hr_company_123"

// Check if user has HR access to specific company
companyIDs := auth.GetHRCompanyIDs(userRoles) // ["123"]

// Validate HR role (base or company-specific)
isHR := auth.IsHumanResources(userRoles) // true for both "human_resources" and "hr_company_123"
```

## Middleware Usage

### Basic Middleware Chain

```go
// Required chain for all protected endpoints
authGuard := middlewares.AuthGuard()           // Validates JWT
userContext := middlewares.UserContextMiddleware() // Extracts user info
```

### Role-Based Guards

```go
// Convenience middlewares (hierarchical)
adminOnly := middlewares.AdminOnly()                    // Level 5 only
directorOrAbove := middlewares.DirectorOrAbove()        // Level 4+
teacherOrAbove := middlewares.TeacherOrAbove()          // Level 2+

// Special access patterns
selfOrAdmin := middlewares.SelfOrAdmin()                // Self-access or admin
hrOnly := middlewares.HROnly()                          // HR roles only (excludes admin)
hrOrAdmin := middlewares.HROrAdmin()                    // HR roles or admin

// Business logic specific
contentManagers := middlewares.ContentManagers()       // Admin + Teacher (excludes Director)

// Custom role guard with flexible configuration
customGuard := middlewares.RoleGuard(middlewares.RoleGuardConfig{
    RequiredRoles: []auth.Role{auth.RoleTeacher, auth.RoleDirector}, // OR logic
    MinimumRole:   auth.RoleTeacher,                                 // Hierarchy check
    AllowSelf:     true,                                             // Self-access
    SelfParamName: "userId",                                         // Parameter name for self-check
})
```

### Role Guard Configuration Options

```go
type RoleGuardConfig struct {
    // RequiredRoles: User needs ANY of these roles (OR logic)
    RequiredRoles []auth.Role

    // MinimumRole: User needs this role level or higher (hierarchy)
    MinimumRole auth.Role

    // AllowSelf: Users can access their own resources
    AllowSelf bool

    // SelfParamName: Parameter name to check for self-access (default: "id")
    SelfParamName string
}
```

## Service Layer Integration

### Using Role-Based Filtering

```go
// In your service methods
func (s *service) GetUsers(ctx context.Context, requestingUserID string, requestingUserRoles []string) ([]*model.User, error) {
    // Use RBAC service to build filters
    filter := s.RBACService.BuildUserFilter(ctx, requestingUserID, requestingUserRoles)
    
    // Apply filter in repository query
    return s.Repository.FindWithFilter(ctx, filter)
}

// Check access permissions
func (s *service) CanUserAccessResource(ctx context.Context, userID string, userRoles []string, resourceID string) bool {
    return s.RBACService.CanAccessResource(ctx, userID, userRoles, resourceID)
}
```

### Permission Checking

```go
// Get user permissions based on role hierarchy
permissions := auth.GetPermissions(userRoles)

// Permission structure
type RolePermissions struct {
    CanAccessAllData     bool     // Admin only (Level 5)
    CanAccessDepartment  bool     // Director and above (Level 4+)
    CanAccessOwnStudents bool     // Teacher and above (Level 2+)
    CanAccessOwnData     bool     // All roles (Level 1+)
    CanAccessCompanyData bool     // HR roles (Level 3)
    CompanyIDs           []string // For HR company-specific roles
}

// Usage example
if permissions.CanAccessAllData {
    // Admin access - no restrictions
} else if permissions.CanAccessDepartment {
    // Director access - department scope
} else if permissions.CanAccessCompanyData {
    // HR access - company scope
} else if permissions.CanAccessOwnStudents {
    // Teacher access - assigned students
} else {
    // Student/User access - own data only
}
```

### Individual Role Checkers

```go
// Hierarchical role checking (ordered from highest to lowest)
isAdmin := auth.IsAdmin(userRoles)                    // Level 5
isDirector := auth.IsDirector(userRoles)              // Level 4
isHR := auth.IsHumanResources(userRoles)              // Level 3 (includes company-specific)
isTeacher := auth.IsTeacher(userRoles)                // Level 2
isStudent := auth.IsStudent(userRoles)                // Level 1 (educational)
isUser := auth.IsUser(userRoles)                      // Level 1 (general platform)

// Utility functions
hasMinimumRole := auth.HasMinimumRole(userRoles, auth.RoleTeacher) // Level 2+
hasAnyRole := auth.HasAnyRole(userRoles, auth.RoleTeacher, auth.RoleDirector)
```

## Context Utilities

### Extracting User Information

```go
// In controller methods
userCtx, err := middlewares.GetUserContext(c)
userID, err := middlewares.GetUserID(c)
roles, err := middlewares.GetUserRoles(c)

// Using convenience functions
isAdmin, err := utils.IsAdmin(c)
canAccess, err := utils.CanAccessUser(c, targetUserID)
permissions, err := utils.ContextUtils.GetPermissions(c)
```

### Permission Validation

```go
// Require specific permissions
err := utils.ContextUtils.RequireCanAccessAllData(c)        // Admin only
err := utils.ContextUtils.RequireCanAccessDepartment(c)     // Director+
err := utils.ContextUtils.RequireCanAccessOwnStudents(c)    // Teacher+
err := utils.ContextUtils.RequireCanAccessCompanyData(c)    // HR only
```

## RBAC Service Integration

### Service Methods

The RBAC service provides centralized filtering and permission checking:

```go
type Service interface {
    // BuildUserFilter creates MongoDB filter based on user roles and permissions
    BuildUserFilter(ctx context.Context, userID string, userRoles []string) bson.M

    // BuildStudentFilter creates filter for teacher-student relationships
    BuildStudentFilter(ctx context.Context, userID string, userRoles []string, teacherField, studentField string) bson.M

    // BuildHREmployeeFilter creates filter for HR-employee relationships (referral system)
    BuildHREmployeeFilter(ctx context.Context, userID string, userRoles []string) bson.M

    // CanAccessResource checks if user can access specific resource
    CanAccessResource(ctx context.Context, userID string, userRoles []string, resourceOwnerID string) bool

    // GetAccessibleUserIDs returns list of user IDs that current user can access
    GetAccessibleUserIDs(ctx context.Context, userID string, userRoles []string) ([]string, error)
}
```

### Using RBAC Service in Your Services

```go
// In your service constructor
func NewYourService(repo Repository, rbacService rbac.Service) Service {
    return &service{
        Repository:  repo,
        RBACService: rbacService,
    }
}

// In your service methods
func (s *service) FindUsers(ctx context.Context, requestingUserID string, requestingUserRoles []string) ([]*model.User, error) {
    // Use RBAC service to build appropriate filter
    filter := s.RBACService.BuildUserFilter(ctx, requestingUserID, requestingUserRoles)

    // Apply filter in repository query
    return s.Repository.FindWithFilter(ctx, filter)
}
```

## Database Query Filtering

### MongoDB Filter Examples

```go
// Admin user (Level 5) - no filter
if permissions.CanAccessAllData {
    filter = bson.M{} // Access all data
}

// Student/User (Level 1) - own data only
if permissions.CanAccessOwnData && !permissions.CanAccessOwnStudents {
    filter = bson.M{
        "$or": []bson.M{
            {"_id": userID},
            {"_id": objID}, // Handle ObjectID conversion
        },
    }
}

// Teacher (Level 2+) - own data + assigned students
if permissions.CanAccessOwnStudents {
    filter = bson.M{
        "$or": []bson.M{
            {"_id": userID},
            {"teacherId": userID},
            {"assignedTeacher": userID},
        },
    }
}

// HR (Level 3) - employee referral relationships
if permissions.CanAccessCompanyData {
    filter = bson.M{"referringUserId": userID}

    // For company-specific HR roles
    if len(permissions.CompanyIDs) > 0 {
        filter = bson.M{
            "$and": []bson.M{
                {"referringUserId": userID},
                {"companyId": bson.M{"$in": permissions.CompanyIDs}},
            },
        }
    }
}

// Director (Level 4) - department scope
if permissions.CanAccessDepartment {
    filter = bson.M{
        "$or": []bson.M{
            {"_id": userID},
            {"departmentId": userDepartmentID},
            {"managerId": userID},
        },
    }
}
```

## Testing

### Unit Tests

```go
func TestRoleBasedAccess(t *testing.T) {
    // Test individual role checking
    assert.True(t, auth.HasRole([]string{"teacher"}, auth.RoleTeacher))
    assert.False(t, auth.HasRole([]string{"student"}, auth.RoleAdmin))

    // Test hierarchical role checking
    assert.True(t, auth.HasMinimumRole([]string{"admin"}, auth.RoleTeacher))
    assert.False(t, auth.HasMinimumRole([]string{"student"}, auth.RoleTeacher))

    // Test permissions
    permissions := auth.GetPermissions([]string{"director"})
    assert.True(t, permissions.CanAccessDepartment)
    assert.True(t, permissions.CanAccessOwnStudents)
    assert.False(t, permissions.CanAccessAllData)

    // Test HR role checking
    assert.True(t, auth.IsHumanResources([]string{"human_resources"}))
    assert.True(t, auth.IsHumanResources([]string{"hr_company_123"}))
    assert.False(t, auth.IsHumanResources([]string{"teacher"}))

    // Test company ID extraction
    companyIDs := auth.GetHRCompanyIDs([]string{"hr_company_123", "hr_company_456"})
    assert.Equal(t, []string{"123", "456"}, companyIDs)
}
```

### Integration Tests

```go
func TestEndpointAccess(t *testing.T) {
    // Create test tokens with different roles
    adminToken := createTestToken("admin-user", []string{"admin"})
    teacherToken := createTestToken("teacher-user", []string{"teacher"})
    studentToken := createTestToken("student-user", []string{"student"})
    
    // Test admin access
    req := httptest.NewRequest(http.MethodGet, "/api/v2/users", nil)
    req.Header.Set("Authorization", "Bearer "+adminToken)
    // Should succeed
    
    // Test student access to admin endpoint
    req.Header.Set("Authorization", "Bearer "+studentToken)
    // Should fail with 403
}
```

## Migration Strategy

### Phase 1: Backward Compatibility
- Deploy new JWT structure with both `role` and `roles` fields
- Existing endpoints continue to work with legacy `role` field
- New endpoints use `roles` array

### Phase 2: Gradual Migration
- Update endpoints one by one to use new middleware chain
- Add role-based filtering to service methods
- Test thoroughly with different user roles

### Phase 3: Legacy Cleanup
- Remove legacy `role` field from JWT (optional)
- Update all endpoints to use new RBAC system
- Remove old middleware functions

## Best Practices

1. **Always use middleware chain**: AuthGuard → UserContext → RoleGuard
2. **Validate at multiple layers**: Middleware, controller, and service
3. **Use service layer for business logic**: Keep controllers thin
4. **Test with all role combinations**: Ensure proper access control
5. **Log access attempts**: For security auditing
6. **Use convenience functions**: Leverage utils package for common checks
7. **Handle edge cases**: Empty roles, invalid tokens, etc.

## Common Patterns

### Self-Access Pattern
```go
// Allow users to access their own data or admins to access any data
selfOrAdmin := middlewares.SelfOrAdmin()
group.GET("/users/:id", handler, authChain, userContextChain, selfOrAdmin)
```

### Hierarchical Access Pattern
```go
// Allow teacher level and above
teacherOrAbove := middlewares.TeacherOrAbove()
group.GET("/students", handler, authChain, userContextChain, teacherOrAbove)
```

### Company-Scoped Access Pattern
```go
// HR users can only access their company data (excludes admin)
hrOnly := middlewares.HROnly()
group.GET("/hr/employees", handler, authChain, userContextChain, hrOnly)

// HR users or admin can access (includes admin)
hrOrAdmin := middlewares.HROrAdmin()
group.GET("/company-reports", handler, authChain, userContextChain, hrOrAdmin)
```

### Business Logic Specific Pattern
```go
// Content managers (admin + teacher, excludes director)
contentManagers := middlewares.ContentManagers()
group.POST("/content", handler, authChain, userContextChain, contentManagers)
```

## Key Features and Benefits

### 1. Hierarchical Role System
- **Clear privilege levels**: 5-level hierarchy from Admin (5) to Student/User (1)
- **Consistent ordering**: All functions and documentation follow Admin → Director → HR → Teacher → Student/User
- **Flexible access control**: MinimumRole for hierarchy, RequiredRoles for specific business logic

### 2. Company-Specific HR Roles
- **Granular access**: `hr_company_{companyId}` for multi-tenant HR access
- **Automatic detection**: System recognizes company-specific roles automatically
- **Referral-based access**: HR users access employees through referral relationships

### 3. Clean Architecture Integration
- **Separation of concerns**: Middleware → Controller → Service → Repository
- **Centralized RBAC service**: Consistent filtering and permission logic
- **MongoDB integration**: Dynamic query building based on roles

### 4. Backward Compatibility
- **Legacy support**: Maintains both `role` and `roles` fields in JWT
- **Gradual migration**: Existing endpoints continue to work during transition
- **Deprecation strategy**: Clear path for removing legacy code

This RBAC system provides a robust, scalable foundation for access control while maintaining clean architecture principles and backward compatibility.
