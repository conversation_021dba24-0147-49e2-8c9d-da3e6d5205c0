# Logging Configuration

## Application Logging

The application uses a structured logging approach with Go's standard `log/slog` package and Echo's RequestLogger middleware. The logging system is configured to:

- Log successful health check requests (GET /v2/health with status 200) at DEBUG level
- Log successful requests at INFO level
- Log failed requests at ERROR level

This helps reduce log noise from frequent health check requests while still maintaining visibility into the application's health.

## Structured Logging with slog

The application uses Go's standard `log/slog` package for structured logging, which provides:

- JSON-formatted logs for better machine readability
- Consistent log structure across the application
- Proper log levels for different types of events
- Contextual information with each log entry

## Log Levels

The application uses the following log levels:

- **DEBUG**: Detailed information, typically of interest only when diagnosing problems
  - Successful health check requests (GET /v2/health with status 200)

- **INFO**: Confirmation that things are working as expected
  - Successful HTTP requests
  - Application startup and shutdown
  - Important system events

- **WARN**: Indication that something unexpected happened, or indicative of some problem in the near future
  - Deprecated API usage
  - Poor use of an API
  - Other runtime situations that are undesirable but not necessarily wrong

- **ERROR**: Due to a more serious problem, the software has not been able to perform some function
  - Failed requests
  - API errors
  - Database connection issues

## Log Structure

Each log entry includes the following information:

- **time**: Timestamp of the log entry
- **level**: Log level (DEBUG, INFO, WARN, ERROR)
- **msg**: Message describing the event (e.g., "REQUEST", "HEALTH_CHECK", "REQUEST_ERROR")
- **uri**: The request URI
- **method**: The HTTP method (GET, POST, etc.)
- **status**: The HTTP status code
- **remote_ip**: The client's IP address
- **user_agent**: The client's user agent
- **latency**: The time taken to process the request
- **error**: Error message (only for failed requests)

## Configuration

The custom logger is configured in `internal/api/middlewares/logger.go` and is used in `internal/api/server.go`.

### Changing Log Level

The log level can be configured using the `LOG_LEVEL` environment variable:

- `DEBUG`: Logs everything including debug information
- `INFO`: Logs info, warnings, and errors (default)
- `WARN`: Logs only warnings and errors
- `ERROR`: Logs only errors

For example, to set the log level to DEBUG:

```bash
# In Linux/macOS
export LOG_LEVEL=DEBUG

# In Windows PowerShell
$env:LOG_LEVEL="DEBUG"

# In Windows Command Prompt
set LOG_LEVEL=DEBUG
```

You can also set this in your `.env` file:

```
LOG_LEVEL=DEBUG
```

### Advanced Configuration

To modify other aspects of the logging behavior:

1. Edit the `CustomLogger` middleware in `internal/api/middlewares/logger.go`
2. Adjust the format or conditions as needed

For application-wide logging (outside of HTTP requests), use the `GetSlogger()` function to get a configured slog.Logger instance:

```go
import "github.com/dsoplabs/dinbora-backend/internal/api/middlewares"

func someFunction() {
    logger := middlewares.GetSlogger()

    // Log at different levels
    logger.Debug("Debug information")
    logger.Info("Operation completed", "key", "value")
    logger.Warn("Warning message", "reason", "disk space low")
    logger.Error("Operation failed", "error", err.Error())
}
```

## Log Output

In development mode, logs are output to the console in JSON format.

In production mode, logs are written to:
- Console output (JSON format)
- System journal via systemd
- Log file at `/var/log/dinbora.log` (rotated weekly or when reaching 50MB)