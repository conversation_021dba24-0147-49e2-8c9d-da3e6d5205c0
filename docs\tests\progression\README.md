# Progression System Testing

This document describes the testing approach for the Progression System, which tracks user progress through lessons, challenges, and trails.

## Test Organization

The Progression System follows the standard Go project layout for tests:

- **Model Tests**: `internal/model/progression/*_test.go`
- **Service Tests**: `internal/service/progression/*_test.go`
- **Repository Tests**: `internal/repository/progression/*_test.go`
- **Controller Tests**: `internal/controller/progression/*_test.go`

Each test file is located in the same directory as the code it tests, following Go best practices.

## Running Tests

### All Progression Tests

To run all progression system tests:

```bash
go test -v ./internal/model/progression/... ./internal/service/progression/... ./internal/repository/progression/... ./internal/controller/progression/...
```

### Layer-Specific Tests

To run tests for a specific layer:

```bash
# Model tests
go test -v ./internal/model/progression/...

# Service tests
go test -v ./internal/service/progression/...

# Repository tests
go test -v ./internal/repository/progression/...

# Controller tests
go test -v ./internal/controller/progression/...
```

### Coverage Reports

To run tests with coverage:

```bash
go test -v -coverprofile=coverage.out ./internal/model/progression/... ./internal/service/progression/... ./internal/repository/progression/... ./internal/controller/progression/...
go tool cover -html=coverage.out
```

## Trail File Validation Tests

The progression service includes comprehensive validation tests for trail files. These tests check for various issues that could cause problems in the user experience:

```bash
go test -v -run TestTrailMigrationFiles ./internal/service/progression/...
```

### What the Trail Validation Tests Check

The validation tests check for the following issues:

#### 1. Content Structure Issues

- **Choices and Next Field Conflict**: Content items with both `choices` and a `next` field (even if empty)
  - Content items with choices should not have a next field at the top level
  - The next field should only exist inside each choice
  
- **Missing Navigation**: Content items missing both `choices` and a `next` field
  - Every content item should have either choices or a next field to allow progression

#### 2. Navigation Issues

- **Unreachable Content**: Content items that cannot be reached from the starting point
  - All content items should be reachable through some path from the first content item
  - Example: If content item A points to B and C, but D is never referenced, D is unreachable

- **Cycles**: Circular paths that could trap users in an infinite loop
  - The test detects if following the "next" fields creates a cycle
  - Example: If A → B → C → A, this creates a cycle

- **Dead Ends**: Paths that don't lead to a reward or completion
  - Every path should eventually lead to a reward or a valid end point
  - Valid end points include "coin", "diamond", and "free"

#### 3. Reference Issues

- **Invalid References**: References to non-existent content items
  - If a content item's "next" field points to an identifier that doesn't exist
  - If a choice's "next" field points to an identifier that doesn't exist

- **Missing Required Fields**: Content items missing required fields
  - Every content item should have an identifier
  - Content items should have either a description or an image

#### 4. Reward Path Issues

- **Missing Reward Paths**: Lessons or challenges that don't have paths leading to rewards
  - At least one path in each lesson should lead to a reward
  - Rewards include "coin", "diamond", or "free"

### Error Reporting

Each issue is reported with:
- The file path
- The line number in the file
- A description of the problem
- The trail ID, lesson/phase identifier, and content identifier

This makes it easy to locate and fix issues in the trail files.

## CI/CD Pipeline

We have a CI/CD pipeline set up using GitHub Actions that runs automatically when code is pushed to the `test` branch. The pipeline:

1. **Lints** the code using golangci-lint
2. **Tests** the code and generates a coverage report
3. **Scans** for security vulnerabilities using gosec
4. **Builds** the application to ensure it compiles correctly
5. **Notifies** the team of the results

The pipeline only runs when changes are made to the progression system files.

## Test Development Guidelines

When writing tests for the progression system, please follow these guidelines:

1. **Test Coverage**: Aim for high test coverage, especially for critical paths
2. **Test Edge Cases**: Include tests for boundary conditions and error scenarios
3. **Test Readability**: Use descriptive test names and clear assertions
4. **Test Independence**: Each test should be independent and not rely on the state from other tests
5. **Test Performance**: Tests should run quickly to encourage frequent testing

### Example Test Structure

```go
func TestSomeFunctionality(t *testing.T) {
    // Setup
    // ...
    
    // Test cases
    testCases := []struct {
        name     string
        input    SomeType
        expected SomeResult
        wantErr  bool
    }{
        {
            name:     "valid input",
            input:    SomeType{...},
            expected: SomeResult{...},
            wantErr:  false,
        },
        {
            name:     "invalid input",
            input:    SomeType{...},
            expected: SomeResult{},
            wantErr:  true,
        },
    }
    
    // Run test cases
    for _, tc := range testCases {
        t.Run(tc.name, func(t *testing.T) {
            result, err := FunctionUnderTest(tc.input)
            
            if tc.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
                assert.Equal(t, tc.expected, result)
            }
        })
    }
}
```
