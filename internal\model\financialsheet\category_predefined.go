package financialsheet

// Predefined categories
var (
	UndefinedCategory = Category{
		Identifier: CategoryIdentifierUndefined,
	}
	Compensation = Category{
		Identifier: CategoryIdentifierCompensation,
		Name:       "Remuneração",
		Type:       CategoryTypeIncome,
		Icon:       CategoryIconCompensationCard,
		Background: CategoryBackgroundIncome,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	Benefits = Category{
		Identifier: CategoryIdentifierBenefits,
		Name:       "Benefícios",
		Type:       CategoryTypeIncome,
		Icon:       CategoryIconBenefitsCard,
		Background: CategoryBackgroundIncome,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	InvestmentIncome = Category{
		Identifier: CategoryIdentifierInvestmentIncome,
		Name:       "Renda de Investimentos",
		Type:       CategoryTypeIncome,
		Icon:       CategoryIconInvestmentIncomeCard,
		Background: CategoryBackgroundIncome,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	AdditionalEarnings = Category{
		Identifier: CategoryIdentifierAdditionalEarnings,
		Name:       "Ganhos Adicionais",
		Type:       CategoryTypeIncome,
		Icon:       CategoryIconAdditionalEarningsCard,
		Background: CategoryBackgroundIncome,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	PersonalNeeds = Category{
		Identifier: CategoryIdentifierPersonalNeeds,
		Name:       "Necessidades",
		Type:       CategoryTypeCostsOfLiving,
		Icon:       CategoryIconPersonalNeedsCard,
		Background: CategoryBackgroundCostsOfLiving,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	Dreams = Category{
		Identifier: CategoryIdentifierDreams,
		Name:       "Sonhos",
		Type:       CategoryTypeCostsOfLiving,
		Icon:       CategoryIconDreamsCard,
		Background: CategoryBackgroundCostsOfLiving,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	Debts = Category{
		Identifier: CategoryIdentifierDebts,
		Name:       "Dívidas",
		Type:       CategoryTypeCostsOfLiving,
		Icon:       CategoryIconDebtsCard,
		Background: CategoryBackgroundCostsOfLiving,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	PersonalReserves = Category{
		Identifier: CategoryIdentifierPersonalReserves,
		Name:       "Reservas",
		Type:       CategoryTypeCostsOfLiving,
		Icon:       CategoryIconPersonalReservesCard,
		Background: CategoryBackgroundCostsOfLiving,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	Housing = Category{
		Identifier: CategoryIdentifierHousing,
		Name:       "Moradia",
		Type:       CategoryTypeExpense,
		Icon:       CategoryIconHousingCard,
		Background: CategoryBackgroundExpense,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	HouseBills = Category{
		Identifier: CategoryIdentifierHouseBills,
		Name:       "Contas de Casa",
		Type:       CategoryTypeExpense,
		Icon:       CategoryIconHouseBillsCard,
		Background: CategoryBackgroundExpense,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	Education = Category{
		Identifier: CategoryIdentifierEducation,
		Name:       "Educação",
		Type:       CategoryTypeExpense,
		Icon:       CategoryIconEducationCard,
		Background: CategoryBackgroundExpense,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	Family = Category{
		Identifier: CategoryIdentifierFamily,
		Name:       "Família",
		Type:       CategoryTypeExpense,
		Icon:       CategoryIconFamilyCard,
		Background: CategoryBackgroundExpense,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	Communication = Category{
		Identifier: CategoryIdentifierCommunication,
		Name:       "Comunicação",
		Type:       CategoryTypeExpense,
		Icon:       CategoryIconCommunicationCard,
		Background: CategoryBackgroundExpense,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	Transportation = Category{
		Identifier: CategoryIdentifierTransportation,
		Name:       "Transporte",
		Type:       CategoryTypeExpense,
		Icon:       CategoryIconTransportationCard,
		Background: CategoryBackgroundExpense,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	Health = Category{
		Identifier: CategoryIdentifierHealth,
		Name:       "Saúde",
		Type:       CategoryTypeExpense,
		Icon:       CategoryIconHealthCard,
		Background: CategoryBackgroundExpense,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	PersonalCare = Category{
		Identifier: CategoryIdentifierPersonalCare,
		Name:       "Cuidado Pessoal",
		Type:       CategoryTypeExpense,
		Icon:       CategoryIconPersonalCareCard,
		Background: CategoryBackgroundExpense,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	Food = Category{
		Identifier: CategoryIdentifierFood,
		Name:       "Alimentação",
		Type:       CategoryTypeExpense,
		Icon:       CategoryIconFoodCard,
		Background: CategoryBackgroundExpense,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	Leisure = Category{
		Identifier: CategoryIdentifierLeisure,
		Name:       "Lazer",
		Type:       CategoryTypeExpense,
		Icon:       CategoryIconLeisureCard,
		Background: CategoryBackgroundExpense,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	CasualShopping = Category{
		Identifier: CategoryIdentifierCasualShopping,
		Name:       "Compras Casuais",
		Type:       CategoryTypeExpense,
		Icon:       CategoryIconCasualShoppingCard,
		Background: CategoryBackgroundExpense,
		MoneySource: []MoneySource{
			MoneySourceOpt1,
			MoneySourceOpt2,
			MoneySourceOpt3,
			MoneySourceOpt4,
			MoneySourceOpt5,
			MoneySourceOther,
		},
		PaymentMethod: []PaymentMethod{
			PaymentMethodOpt1,
			PaymentMethodOpt2,
			PaymentMethodOpt3,
			PaymentMethodOpt4,
			PaymentMethodOpt5,
			PaymentMethodOther,
		},
	}
	NetworthStrategicFund = Category{
		Identifier: CategoryIdentifierNetworthStrategicFund,
		Name:       "Reserva Estratégica",
		Type:       CategoryTypeNetworthTracking,
		Icon:       "", // Not visible to users
		Background: "", // Not visible to users
	}
	NetworthInvestments = Category{
		Identifier: CategoryIdentifierNetworthInvestments,
		Name:       "Investimentos",
		Type:       CategoryTypeNetworthTracking,
		Icon:       "", // Not visible to users
		Background: "", // Not visible to users
	}
	NetworthAssets = Category{
		Identifier: CategoryIdentifierNetworthAssets,
		Name:       "Ativos",
		Type:       CategoryTypeNetworthTracking,
		Icon:       "", // Not visible to users
		Background: "", // Not visible to users
	}
)

// GetAllCategories returns all predefined categories
func GetAllCategories() []Category {
	return []Category{
		Compensation,
		Benefits,
		InvestmentIncome,
		AdditionalEarnings,
		PersonalNeeds,
		Dreams,
		Debts,
		PersonalReserves,
		Housing,
		HouseBills,
		Education,
		Family,
		Communication,
		Transportation,
		Health,
		PersonalCare,
		Food,
		Leisure,
		CasualShopping,
		NetworthStrategicFund,
		NetworthInvestments,
		NetworthAssets,
	}
}
