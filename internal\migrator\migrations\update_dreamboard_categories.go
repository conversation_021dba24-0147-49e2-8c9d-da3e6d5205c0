package migrations

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// UpdateDreamboardCategories implements the Migration interface for updating category properties (name, icon, color) in Dreamboards
type UpdateDreamboardCategories struct {
	db *mongo.Database
}

func NewUpdateDreamboardCategories(db *mongo.Database) *UpdateDreamboardCategories {
	return &UpdateDreamboardCategories{
		db: db,
	}
}

func (m *UpdateDreamboardCategories) Name() string {
	return "update_dreamboard_categories_20250428_fix000"
}

// getCorrectName helper function to get the correct name based on category identifier
func (m *UpdateDreamboardCategories) getCorrectName(identifier string) string {
	switch identifier {
	case dreamboard.Professional.Identifier:
		return dreamboard.Professional.Name
	case dreamboard.Financial.Identifier:
		return dreamboard.Financial.Name
	case dreamboard.Leisure.Identifier:
		return dreamboard.Leisure.Name
	case dreamboard.Emotional.Identifier:
		return dreamboard.Emotional.Name
	case dreamboard.Intellectual.Identifier:
		return dreamboard.Intellectual.Name
	case dreamboard.Spiritual.Identifier:
		return dreamboard.Spiritual.Name
	case dreamboard.Physical.Identifier:
		return dreamboard.Physical.Name
	case dreamboard.Intimate.Identifier:
		return dreamboard.Intimate.Name
	case dreamboard.Social.Identifier:
		return dreamboard.Social.Name
	case dreamboard.Familial.Identifier:
		return dreamboard.Familial.Name
	default:
		log.Printf("Warning: Undefined category identifier '%s' encountered. Using default name.", identifier)
		return dreamboard.UndefinedCategory.Name
	}
}

// getCorrectIcon helper function to get the correct icon URL based on category identifier
func (m *UpdateDreamboardCategories) getCorrectIcon(identifier string) string {
	switch identifier {
	case dreamboard.Professional.Identifier:
		return dreamboard.Professional.Icon
	case dreamboard.Financial.Identifier:
		return dreamboard.Financial.Icon
	case dreamboard.Leisure.Identifier:
		return dreamboard.Leisure.Icon
	case dreamboard.Emotional.Identifier:
		return dreamboard.Emotional.Icon
	case dreamboard.Intellectual.Identifier:
		return dreamboard.Intellectual.Icon
	case dreamboard.Spiritual.Identifier:
		return dreamboard.Spiritual.Icon
	case dreamboard.Physical.Identifier:
		return dreamboard.Physical.Icon
	case dreamboard.Intimate.Identifier:
		return dreamboard.Intimate.Icon
	case dreamboard.Social.Identifier:
		return dreamboard.Social.Icon
	case dreamboard.Familial.Identifier:
		return dreamboard.Familial.Icon
	default:
		log.Printf("Warning: Undefined category identifier '%s' encountered. Using default icon.", identifier)
		return dreamboard.UndefinedCategory.Icon
	}
}

// getCorrectColor helper function to get the correct color based on category identifier
func (m *UpdateDreamboardCategories) getCorrectColor(identifier string) string {
	switch identifier {
	case dreamboard.Professional.Identifier:
		return dreamboard.Professional.Color
	case dreamboard.Financial.Identifier:
		return dreamboard.Financial.Color
	case dreamboard.Leisure.Identifier:
		return dreamboard.Leisure.Color
	case dreamboard.Emotional.Identifier:
		return dreamboard.Emotional.Color
	case dreamboard.Intellectual.Identifier:
		return dreamboard.Intellectual.Color
	case dreamboard.Spiritual.Identifier:
		return dreamboard.Spiritual.Color
	case dreamboard.Physical.Identifier:
		return dreamboard.Physical.Color
	case dreamboard.Intimate.Identifier:
		return dreamboard.Intimate.Color
	case dreamboard.Social.Identifier:
		return dreamboard.Social.Color
	case dreamboard.Familial.Identifier:
		return dreamboard.Familial.Color
	default:
		log.Printf("Warning: Undefined category identifier '%s' encountered. Using default color.", identifier)
		return dreamboard.UndefinedCategory.Color
	}
}

func (m *UpdateDreamboardCategories) Up(ctx context.Context) error {
	dreamboardCollection := m.db.Collection(repository.DREAMBOARDS_COLLECTION)

	cursor, err := dreamboardCollection.Find(ctx, bson.D{})
	if err != nil {
		return errors.New(errors.Migrator, "failed to find dreamboards", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	log.Println("Starting migration to update category properties (name, icon, color) in dreamboards...")

	var updatedCount int64
	var matchedCount int64

	for cursor.Next(ctx) {
		var dreamboard dreamboard.Dreamboard
		if err := cursor.Decode(&dreamboard); err != nil {
			log.Printf("Error decoding dreamboard with ID %s: %v. Skipping.", dreamboard.ID, err)
			continue // Skip this dreamboard and continue with the next
		}

		// Check if categories array exists and is not empty
		if len(dreamboard.Categories) == 0 {
			log.Printf("Dreamboard %s has no categories. Skipping.", dreamboard.ObjectID.Hex())
			continue
		}

		session, err := dreamboardCollection.Database().Client().StartSession()
		if err != nil {
			log.Printf("Failed to start session for dreamboard %s: %v. Skipping.", dreamboard.ObjectID.Hex(), err)
			continue // Skip this dreamboard
		}

		// First update the categories
		categoryCallback := func(sessCtx mongo.SessionContext) (interface{}, error) {
			var transactionUpdatedCount int64
			var transactionMatchedCount int64

			for _, category := range dreamboard.Categories {
				correctName := m.getCorrectName(category.Identifier)
				correctIcon := m.getCorrectIcon(category.Identifier)
				correctColor := m.getCorrectColor(category.Identifier)

				// Only update if any property is different from the correct one
				if category.Name != correctName || category.Icon != correctIcon || category.Color != correctColor {
					log.Printf("Updating properties for category '%s' in dreamboard %s", category.Identifier, dreamboard.ObjectID.Hex())

					// Use arrayFilters to target the specific category element by its _id
					filter := bson.M{"_id": dreamboard.ObjectID}
					arrayFilter := options.ArrayFilters{
						Filters: []interface{}{bson.M{"elem._id": category.ObjectID}},
					}
					update := bson.M{
						"$set": bson.M{
							"categories.$[elem].name":  correctName,
							"categories.$[elem].icon":  correctIcon,
							"categories.$[elem].color": correctColor,
							"updatedAt":                time.Now(),
						},
					}

					result, err := dreamboardCollection.UpdateOne(
						sessCtx,
						filter,
						update,
						options.Update().SetArrayFilters(arrayFilter),
					)
					if err != nil {
						// Abort transaction on error
						log.Printf("Error updating category properties for dreamboard %s, category %s: %v", dreamboard.ObjectID.Hex(), category.Identifier, err)
						return nil, errors.New(errors.Repository, "failed to update category properties", errors.Internal, err)
					}

					transactionMatchedCount += result.MatchedCount
					transactionUpdatedCount += result.ModifiedCount
					if result.ModifiedCount > 0 {
						log.Printf("Successfully updated properties for category '%s' in dreamboard %s", category.Identifier, dreamboard.ObjectID.Hex())
					} else if result.MatchedCount > 0 {
						log.Printf("Category '%s' in dreamboard %s matched but properties were already correct.", category.Identifier, dreamboard.ObjectID.Hex())
					} else {
						log.Printf("Warning: No match found for category '%s' (_id: %s) in dreamboard %s during update.", category.Identifier, category.ObjectID.Hex(), dreamboard.ObjectID.Hex())
					}
				} else {
					log.Printf("Properties for category '%s' in dreamboard %s are already up-to-date.", category.Identifier, dreamboard.ObjectID.Hex())
					// Increment matched count even if not updated, as we found the document/category
					transactionMatchedCount++
				}
			}
			// Return counts for logging outside the transaction
			return bson.M{"matched": transactionMatchedCount, "updated": transactionUpdatedCount}, nil
		}

		categoryResultData, err := session.WithTransaction(ctx, categoryCallback)
		if err != nil {
			log.Printf("Category update transaction failed for dreamboard %s: %v", dreamboard.ObjectID.Hex(), err)
			session.EndSession(ctx)
			continue // Continue with the next dreamboard
		}

		// Log results from the category update transaction
		if categoryResultData != nil {
			if data, ok := categoryResultData.(bson.M); ok {
				matched := data["matched"].(int64)
				updated := data["updated"].(int64)
				matchedCount += matched
				updatedCount += updated
				log.Printf("Category update transaction successful for dreamboard %s. Matched: %d, Updated: %d", dreamboard.ObjectID.Hex(), matched, updated)
			}
		}

		// Now update the dreams
		dreamCallback := func(sessCtx mongo.SessionContext) (interface{}, error) {
			var transactionUpdatedCount int64
			var transactionMatchedCount int64

			// Update each dream in the dreamboard
			for _, dream := range dreamboard.Dreams {
				correctName := m.getCorrectName(dream.Category.String())
				correctColor := m.getCorrectColor(dream.Category.String())

				// Update dream properties
				update := bson.D{
					{Key: "$set", Value: bson.D{
						{Key: "dreams.$.name", Value: correctName},
						{Key: "dreams.$.color", Value: correctColor},
						{Key: "updatedAt", Value: time.Now()},
					}},
				}

				result, err := dreamboardCollection.UpdateOne(
					sessCtx,
					bson.M{
						"_id":        dreamboard.ObjectID,
						"dreams._id": dream.ObjectID,
					},
					update,
				)
				if err != nil {
					return nil, errors.New(errors.Repository, "failed to update dream properties", errors.Internal, err)
				}

				transactionMatchedCount += result.MatchedCount
				transactionUpdatedCount += result.ModifiedCount
				if result.MatchedCount > 0 {
					log.Printf("Updated properties for dream %s in dreamboard %s", dream.ID, dreamboard.ID)
				}
			}

			return bson.M{"matched": transactionMatchedCount, "updated": transactionUpdatedCount}, nil
		}

		dreamResultData, err := session.WithTransaction(ctx, dreamCallback)
		if err != nil {
			log.Printf("Dream update transaction failed for dreamboard %s: %v", dreamboard.ObjectID.Hex(), err)
			session.EndSession(ctx)
			continue // Continue with the next dreamboard
		}

		// Log results from the dream update transaction
		if dreamResultData != nil {
			if data, ok := dreamResultData.(bson.M); ok {
				matched := data["matched"].(int64)
				updated := data["updated"].(int64)
				matchedCount += matched
				updatedCount += updated
				log.Printf("Dream update transaction successful for dreamboard %s. Matched: %d, Updated: %d", dreamboard.ObjectID.Hex(), matched, updated)
			}
		}

		session.EndSession(ctx) // End session successfully
	}

	if err := cursor.Err(); err != nil {
		log.Printf("Cursor error after iteration: %v", err)
		return errors.New(errors.Migrator, "cursor error after iterating dreamboards", errors.Internal, err)
	}

	log.Printf("Dreamboard category properties update migration completed. Total matched items: %d, Total updated items: %d", matchedCount, updatedCount)
	return nil // Migration completed successfully
}
