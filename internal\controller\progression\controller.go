package progression

import (
	"context"
	"net/http"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	_progression "github.com/dsoplabs/dinbora-backend/internal/service/progression"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD

	// Trail CRUD
	FindTrail() echo.HandlerFunc

	// Lesson and Challenge
	Create() echo.HandlerFunc

	// Lesson CRUD
	FindLesson() echo.HandlerFunc
	// Challenge CRUD
	FindChallenge() echo.HandlerFunc
	FindChallengePhase() echo.HandlerFunc
	FindChallengePhaseCurrentPoints() echo.HandlerFunc

	// Card CRUD
	FindTrailCard() echo.HandlerFunc
	FindAllTrailsCards() echo.HandlerFunc
	FindAllRegularTrailsCards() echo.HandlerFunc
	FindAllExtraTrailsCards() echo.HandlerFunc
	FindAllModulesCards() echo.HandlerFunc
	FindAllChallengePhasesCards() echo.HandlerFunc
	FindChallengePhaseCard() echo.HandlerFunc

	// Legacy
	LegacyCreate() echo.HandlerFunc
	LegacyFindTrailCards() echo.HandlerFunc
	LegacyFindLessonCards() echo.HandlerFunc
	LegacyFindChallengePhaseCards() echo.HandlerFunc
}

type controller struct {
	Service     _progression.Service
	UserService user.Service
}

func New(service _progression.Service, userService user.Service) Controller {
	return &controller{
		Service:     service,
		UserService: userService,
	}
}

// Routes
func (tc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	// V2
	// Group for self-service endpoints under "/progressions/me"
	progressionsGroup := currentGroup.Group("/progressions", middlewares.AuthGuard())
	meGroup := progressionsGroup.Group("/me")

	// CRUD

	// Trail CRUD
	trailsGroup := meGroup.Group("/trails")
	trailsGroup.GET("/:trailId", tc.FindTrail())

	// Lesson CRUD
	lessonsGroup := trailsGroup.Group("/:trailId/lessons")
	lessonsGroup.POST("/:lessonIdentifier", tc.Create()) // Create lesson progression
	lessonsGroup.GET("/:lessonIdentifier", tc.FindLesson())

	// Challenge CRUD
	challengesGroup := trailsGroup.Group("/:trailId/challenges")
	challengesGroup.POST("/:challengeIdentifier", tc.Create()) // Create challenge progression
	challengesGroup.GET("/:challengeIdentifier", tc.FindChallenge())
	challengesGroup.GET("/:challengeIdentifier/phases/:phaseIdentifier", tc.FindChallengePhase())
	challengesGroup.GET("/:challengeIdentifier/phases/:phaseIdentifier/points", tc.FindChallengePhaseCurrentPoints())

	// Card CRUD
	cardsGroup := meGroup.Group("/cards")
	cardsGroup.GET("/trails/:trailId", tc.FindTrailCard())
	cardsGroup.GET("/trails", tc.FindAllTrailsCards())
	cardsGroup.GET("/trails/regular", tc.FindAllRegularTrailsCards())
	cardsGroup.GET("/trails/extra", tc.FindAllExtraTrailsCards())
	cardsGroup.GET("/trails/:trailId/modules", tc.FindAllModulesCards())
	cardsGroup.GET("/trails/:trailId/challenges/phases/:phaseIdentifier", tc.FindChallengePhaseCard())
	cardsGroup.GET("/trails/:trailId/challenges/phases", tc.FindAllChallengePhasesCards())

	// Legacy V1 Routes
	progressGroup := legacyGroup.Group("progress/", middlewares.AuthGuard())
	progressGroup.POST(":trailId/register/:lessonIdentifier/", tc.LegacyCreate())
	progressGroup.GET("trail/:trailId/", tc.FindTrail())
	progressGroup.GET("trail/:trailId/:lessonIdentifier/", tc.FindLesson())
	progressGroup.GET("challenge/:trailId/:challengeIdentifier/", tc.FindChallenge())
	progressGroup.GET("challenge/:trailId/:challengeIdentifier/:phaseIdentifier/", tc.FindChallengePhase())
	progressGroup.POST("getLessonCards/", tc.LegacyFindLessonCards())
	progressGroup.POST("getTrailCards/", tc.LegacyFindTrailCards())
	progressGroup.POST("getChallengePhaseCards/", tc.LegacyFindChallengePhaseCards())
}

// CRUD

// Trail CRUD
// Create handles registering progress for lessons or challenges.
// It determines which specific service function to call based on the request body's Type.
func (tc *controller) Create() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var progressionBody progression.ProgressionBody // Use the existing body struct which contains the Type field

		if err := c.Bind(&progressionBody); err != nil {
			return errors.New(errors.Controller, "invalid input binding", errors.Validation, err)
		}

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		progressionBody.Trail = c.Param("trailId")

		// Call the appropriate service function based on the Type
		switch progressionBody.Type {
		case "LESSON":
			progressionBody.Module = c.Param("lessonIdentifier")
			err = tc.Service.CreateLesson(ctx, userToken.Uid, userClassification, &progressionBody)
		case "CHALLENGE":
			progressionBody.Module = c.Param("challengeIdentifier")
			err = tc.Service.CreateChallenge(ctx, userToken.Uid, userClassification, &progressionBody)

			// Set the user financial profile based on the points in the financial profile challenge phase
			if progressionBody.Content == "dsop_final_options_page" { // This means is the last page of the financial profile challenge
				err = tc.UserService.UpdateFinancialProfile(ctx, userToken.Uid)
			}

		default:
			// Handle unknown type
			return errors.New(errors.Controller, "invalid progression type in request body", errors.Validation, nil)
		}

		// Check for errors from the service call
		if err != nil {
			return err // Return the error passed up from the service
		}

		// Success
		return c.JSON(http.StatusNoContent, nil)
	}
}

func (tc *controller) FindTrail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trailProgress, err := tc.Service.FindTrail(ctx, userToken.Uid, userClassification, c.Param("trailId"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, trailProgress)
	}
}

// Lesson CRUD
func (tc *controller) FindLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		lessonProgress, err := tc.Service.FindLesson(ctx, userToken.Uid, userClassification, c.Param("trailId"), c.Param("lessonIdentifier"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lessonProgress)
	}
}

// Challenge CRUD
func (tc *controller) FindChallenge() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		lessonProgress, err := tc.Service.FindChallenge(ctx, userToken.Uid, userClassification, c.Param("trailId"), c.Param("challengeIdentifier"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lessonProgress)
	}
}

func (tc *controller) FindChallengePhase() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		lessonProgress, err := tc.Service.FindChallengePhase(ctx, userToken.Uid, userClassification, c.Param("trailId"), c.Param("challengeIdentifier"), c.Param("phaseIdentifier"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lessonProgress)
	}
}

func (tc *controller) FindChallengePhaseCurrentPoints() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		points, err := tc.Service.FindChallengePhaseCurrentPoints(ctx, userToken.Uid, userClassification, c.Param("trailId"), c.Param("challengeIdentifier"), c.Param("phaseIdentifier"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, echo.Map{"currentPoints": points})
	}
}

// Card CRUD
func (tc *controller) FindTrailCard() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trailId := c.Param("trailId")
		if trailId == "" {
			return errors.New(errors.Controller, "invalid trail identifier", errors.Validation, nil)
		}

		cards, err := tc.Service.FindTrailCards(ctx, userToken.Uid, userClassification, trailId)
		if err != nil {
			return err
		}

		if len(cards) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		return c.JSON(http.StatusOK, cards)
	}
}

func (tc *controller) FindAllTrailsCards() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		cards, err := tc.Service.FindTrailCards(ctx, userToken.Uid, userClassification, "")
		if err != nil {
			return err
		}

		if len(cards) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		return c.JSON(http.StatusOK, cards)
	}
}

func (tc *controller) FindAllRegularTrailsCards() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		cards, err := tc.Service.FindRegularTrailCards(ctx, userToken.Uid, "")
		if err != nil {
			return err
		}

		if len(cards) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		return c.JSON(http.StatusOK, cards)
	}
}

func (tc *controller) FindAllExtraTrailsCards() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		cards, err := tc.Service.FindExtraTrailCards(ctx, userToken.Uid, userClassification, "")
		if err != nil {
			return err
		}

		if len(cards) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		return c.JSON(http.StatusOK, cards)
	}
}

func (tc *controller) FindAllModulesCards() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trailId := c.Param("trailId")
		if trailId == "" {
			return errors.New(errors.Controller, "invalid trail identifier", errors.Validation, nil)
		}

		lessonCards, challengeCard, err := tc.Service.FindModuleCards(ctx, userToken.Uid, userClassification, trailId, "")
		if err != nil {
			return err
		}

		if len(lessonCards) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		var cards []interface{}
		for _, card := range lessonCards {
			cards = append(cards, card)
		}

		if challengeCard != nil {
			cards = append(cards, challengeCard)
		}

		return c.JSON(http.StatusOK, cards)
	}
}

func (tc *controller) FindChallengePhaseCard() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trailId := c.Param("trailId")
		if trailId == "" {
			return errors.New(errors.Controller, "invalid trail identifier", errors.Validation, nil)
		}

		phaseIdentifier := c.Param("phaseIdentifier")
		if phaseIdentifier == "" {
			return errors.New(errors.Controller, "invalid phase identifier", errors.Validation, nil)
		}

		var cards []*progression.ChallengePhaseCard

		phaseCards, err := tc.Service.FindChallengePhaseCards(ctx, userToken.Uid, userClassification, trailId, phaseIdentifier)
		if err != nil {
			return err
		}

		if len(phaseCards) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		cards = append(cards, phaseCards...)

		return c.JSON(http.StatusOK, cards)
	}
}

func (tc *controller) FindAllChallengePhasesCards() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trailId := c.Param("trailId")
		if trailId == "" {
			return errors.New(errors.Controller, "invalid trail identifier", errors.Validation, nil)
		}

		var cards []*progression.ChallengePhaseCard

		phaseCards, err := tc.Service.FindChallengePhaseCards(ctx, userToken.Uid, userClassification, trailId, "")
		if err != nil {
			return err
		}

		if len(phaseCards) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		cards = append(cards, phaseCards...)

		return c.JSON(http.StatusOK, cards)
	}
}

// Legacy
func (tc *controller) LegacyCreate() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var tmpBody struct {
			Trail     string                           `json:"trail"`
			Module    string                           `json:"lesson"`
			Content   string                           `json:"content"`
			Type      string                           `json:"type"`
			Choice    *progression.ModuleContentChoice `json:"choice"`
			Timestamp time.Time                        `json:"timestamp"`
		}

		if err := c.Bind(&tmpBody); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		body := progression.ProgressionBody{
			Trail:     tmpBody.Trail,
			Module:    tmpBody.Module,
			Content:   tmpBody.Content,
			Type:      tmpBody.Type,
			Choice:    tmpBody.Choice,
			Timestamp: tmpBody.Timestamp,
		}

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		err = tc.Service.LegacyCreateLessonChallenge(ctx, userToken.Uid, userClassification, &body)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

func (tc *controller) LegacyFindTrailCards() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var input map[string]interface{}

		if err := c.Bind(&input); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		trailId := ""
		if val, ok := input["trailId"]; ok {
			trailId = val.(string)
		}

		cards, err := tc.Service.FindRegularTrailCards(ctx, userToken.Uid, trailId)
		if err != nil {
			return err
		}

		if len(cards) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		return c.JSON(http.StatusOK, cards)
	}
}

func (tc *controller) LegacyFindLessonCards() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var input map[string]interface{}

		if err := c.Bind(&input); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trailId := ""
		lessonId := ""

		if val, ok := input["trail"]; ok {
			trailId = val.(string)
		} else {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if val, ok := input["lesson"]; ok {
			lessonId = val.(string)
		}

		var cards []interface{}

		lessonCards, challengeCard, err := tc.Service.FindModuleCards(ctx, userToken.Uid, userClassification, trailId, lessonId)
		if err != nil {
			return err
		}

		if len(lessonCards) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		for _, card := range lessonCards {
			cards = append(cards, card)
		}

		if challengeCard != nil {
			cards = append(cards, challengeCard)
		}

		return c.JSON(http.StatusOK, cards)
	}
}

func (tc *controller) LegacyFindChallengePhaseCards() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var input map[string]interface{}

		if err := c.Bind(&input); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trailId := ""
		phaseId := ""

		if val, ok := input["trail"]; ok {
			trailId = val.(string)
		} else {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if val, ok := input["phase"]; ok {
			phaseId = val.(string)
		}

		var cards []*progression.ChallengePhaseCard

		phaseCards, err := tc.Service.FindChallengePhaseCards(ctx, userToken.Uid, userClassification, trailId, phaseId)
		if err != nil {
			return err
		}

		if len(phaseCards) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		cards = append(cards, phaseCards...)

		return c.JSON(http.StatusOK, cards)
	}
}
