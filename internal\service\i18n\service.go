package i18n

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/dsoplabs/dinbora-backend/locales"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"
)

const (
	// Default language
	DefaultLanguage = "pt"

	// Supported languages
	LanguagePortuguese = "pt"
	LanguageEnglish    = "en"
	LanguageSpanish    = "es"

	// Context key for storing language in Echo context
	LanguageContextKey = "user_language"
)

// Service provides internationalization functionality
type Service struct {
	bundle     *i18n.Bundle
	localizers map[string]*i18n.Localizer
}

// New creates a new i18n service instance
func New() (*Service, error) {
	bundle := i18n.NewBundle(language.Portuguese)
	bundle.RegisterUnmarshalFunc("json", json.Unmarshal)

	service := &Service{
		bundle:     bundle,
		localizers: make(map[string]*i18n.Localizer),
	}

	// Load translation files
	if err := service.loadTranslations(); err != nil {
		return nil, fmt.Errorf("failed to load translations: %w", err)
	}

	return service, nil
}

// loadTranslations loads all translation files from the EMBEDDED locales directory
func (s *Service) loadTranslations() error {
	languages := []string{LanguagePortuguese, LanguageEnglish, LanguageSpanish}

	for _, lang := range languages {
		// The path inside the embedded FS is relative to the path in the `//go:embed` directive.
		// Since we embedded `*.json`, the files are available at `en.json`, etc.
		filename := fmt.Sprintf("%s.json", lang)

		// Use `LoadMessageFileFS` which is designed to work with an fs.FS interface like embed.FS
		if _, err := s.bundle.LoadMessageFileFS(locales.EmbedFS, filename); err != nil {
			return fmt.Errorf("failed to load embedded translation file %s: %w", filename, err)
		}

		// Create localizer for this language (this part remains the same)
		s.localizers[lang] = i18n.NewLocalizer(s.bundle, lang)
	}

	return nil
}

// GetSupportedLanguages returns a list of supported language codes
func (s *Service) GetSupportedLanguages() []string {
	return []string{LanguagePortuguese, LanguageEnglish, LanguageSpanish}
}

// IsLanguageSupported checks if a language is supported
func (s *Service) IsLanguageSupported(lang string) bool {
	for _, supported := range s.GetSupportedLanguages() {
		if supported == lang {
			return true
		}
	}
	return false
}

// DetectLanguageFromHeader detects language from Accept-Language header
func (s *Service) DetectLanguageFromHeader(acceptLanguage string) string {
	if acceptLanguage == "" {
		return DefaultLanguage
	}

	// Parse Accept-Language header
	tags, _, err := language.ParseAcceptLanguage(acceptLanguage)
	if err != nil {
		return DefaultLanguage
	}

	// Find the best matching supported language
	for _, tag := range tags {
		lang := tag.String()

		// Check exact match first
		if s.IsLanguageSupported(lang) {
			return lang
		}

		// Check base language (e.g., "pt-BR" -> "pt")
		if base, _ := tag.Base(); s.IsLanguageSupported(base.String()) {
			return base.String()
		}
	}

	return DefaultLanguage
}

// Translate translates a message key to the specified language
func (s *Service) Translate(lang, key string) string {
	// Use default language if not supported
	if !s.IsLanguageSupported(lang) {
		lang = DefaultLanguage
	}

	localizer, exists := s.localizers[lang]
	if !exists {
		// Fallback to default language
		localizer = s.localizers[DefaultLanguage]
	}

	// Try to translate the message
	message, err := localizer.Localize(&i18n.LocalizeConfig{
		MessageID: key,
	})

	if err != nil {
		// If translation fails, try with default language
		if lang != DefaultLanguage {
			if defaultLocalizer, exists := s.localizers[DefaultLanguage]; exists {
				if defaultMessage, err := defaultLocalizer.Localize(&i18n.LocalizeConfig{
					MessageID: key,
				}); err == nil {
					return defaultMessage
				}
			}
		}

		// If all else fails, return the key itself
		return key
	}

	return message
}

// TranslateWithFallback translates a message key with a fallback message
func (s *Service) TranslateWithFallback(lang, key, fallback string) string {
	translated := s.Translate(lang, key)

	// If translation returned the key itself (meaning no translation found), use fallback
	if translated == key && fallback != "" {
		return fallback
	}

	return translated
}

// GetLanguageFromContext extracts language from context
func GetLanguageFromContext(ctx context.Context) string {
	if lang, ok := ctx.Value(LanguageContextKey).(string); ok {
		return lang
	}
	return DefaultLanguage
}

// SetLanguageInContext sets language in context
func SetLanguageInContext(ctx context.Context, lang string) context.Context {
	return context.WithValue(ctx, LanguageContextKey, lang)
}

// NormalizeLanguageCode normalizes language codes to supported format
func (s *Service) NormalizeLanguageCode(lang string) string {
	lang = strings.ToLower(strings.TrimSpace(lang))

	// Handle common variations
	switch {
	case strings.HasPrefix(lang, "pt"):
		return LanguagePortuguese
	case strings.HasPrefix(lang, "en"):
		return LanguageEnglish
	case strings.HasPrefix(lang, "es"):
		return LanguageSpanish
	default:
		return DefaultLanguage
	}
}
