import os
import re
import argparse
import shutil
from collections import defaultdict

def standardize_base_name(filename):
    """
    Standardizes the base name of a GIF file according to the rules:
    - Removes the .gif extension.
    - Removes leading digits followed by a hyphen (e.g., '1044-name' -> 'name').
    - Removes trailing number suffixes like (1), (2).
    - Converts to lowercase.
    - Replaces spaces and underscores with hyphens.
    - Cleans up multiple consecutive hyphens.
    - Removes leading/trailing hyphens.
    """
    if not filename.lower().endswith(".gif"):
        return None # Not a gif file

    base_name, ext = os.path.splitext(filename) # Remove .gif extension

    # --- Step 1: Remove unwanted prefixes/suffixes ---
    # Remove trailing (number) suffixes, handling potential spaces
    base_name = re.sub(r'\s*\(\d+\)$', '', base_name).strip()
    # Remove leading digits followed by a hyphen (e.g., 123-)
    base_name = re.sub(r'^\d+-', '', base_name)

    # --- Step 2: Standardize characters and case ---
    # Convert to lowercase and replace spaces/underscores with hyphens
    standardized = base_name.lower().replace(' ', '-').replace('_', '-')

    # --- Step 3: Clean up hyphens ---
    # Clean up multiple consecutive hyphens
    standardized = re.sub(r'-+', '-', standardized)
    # Remove leading/trailing hyphens that might result from replacements
    standardized = standardized.strip('-')

    # Return None if standardization results in empty name (edge case)
    if not standardized:
        return None

    return standardized


def process_gifs(source_dir, dest_dir, dry_run=False, force=False):
    """
    Finds GIF files in the source directory, standardizes names (including removing
    leading numbers), plans copying actions to the destination directory, asks for
    confirmation, and executes the plan.
    (Function body remains the same as the previous 'copy_rename_gifs.py' version)
    """
    if not os.path.isdir(source_dir):
        print(f"Error: Source directory not found: {source_dir}")
        return

    try:
        os.makedirs(dest_dir, exist_ok=True)
        print(f"Ensured destination directory exists: {dest_dir}")
    except OSError as e:
        print(f"Error: Could not create destination directory '{dest_dir}': {e}")
        return

    target_map = defaultdict(list)
    actions = []

    print(f"\nScanning source directory: {source_dir}")

    # --- Step 1: Scan Source and Group Files ---
    for filename in os.listdir(source_dir):
        original_full_path = os.path.join(source_dir, filename)
        if not os.path.isfile(original_full_path):
            continue

        standardized_base = standardize_base_name(filename)

        if standardized_base:
            # Note: We group by the *base name* before adding .gif
            # This correctly handles cases where different originals map to the same target
            target_map[standardized_base].append(original_full_path)

    found_gifs_count = sum(len(v) for v in target_map.values())
    if found_gifs_count == 0:
        print("No GIF files found in the source directory matching the criteria.")
        return
    print(f"Found {found_gifs_count} GIF files to potentially process.")

    # --- Step 2: Plan Copy Actions Based on Groups ---
    for standardized_base, original_paths in target_map.items():
        target_filename = f"{standardized_base}.gif" # Add .gif extension back here
        target_full_path = os.path.join(dest_dir, target_filename)

        if os.path.exists(target_full_path):
            print(f"  [!] Skipped: Target file '{target_filename}' already exists in destination '{dest_dir}'.")
            continue

        source_path_to_copy = None

        if len(original_paths) == 1:
            source_path_to_copy = original_paths[0]
        elif len(original_paths) > 1:
            print(f"  [*] Found duplicates in source for target '{target_filename}':")
            path_to_keep = None
            potential_keepers = []
            for p in sorted(original_paths):
                original_basename = os.path.basename(p)
                print(f"      - {original_basename}")
                base_name_no_ext = os.path.splitext(original_basename)[0]
                is_clean_version = not (re.search(r'\s*\(\d+\)$', base_name_no_ext) or re.match(r'^\d+-', base_name_no_ext)) # Check for both patterns

                # Double check standardization matches the target base (should always unless base name became empty)
                standardized_check = standardize_base_name(original_basename)

                if standardized_check == standardized_base and is_clean_version:
                     potential_keepers.append(p)

            if potential_keepers:
                path_to_keep = sorted(potential_keepers)[0]
            else:
                path_to_keep = sorted(original_paths)[0] # Fallback

            print(f"      -> Will copy source: {os.path.basename(path_to_keep)}")
            source_path_to_copy = path_to_keep

        if source_path_to_copy:
             actions.append(('copy', source_path_to_copy, target_full_path))

    # --- Step 3: Review and Execute ---
    if not actions:
        print("\nNo files need to be copied (either none found, already exist in destination, or failed checks).")
        return

    print("\n--- Planned Actions ---")
    for action_type, source_path, dest_path in actions:
        print(f"Copy: '{os.path.basename(source_path)}' (from '{os.path.dirname(source_path)}')")
        print(f"  To: '{os.path.basename(dest_path)}' (in '{os.path.dirname(dest_path)}')")
    print("-" * 20)

    if dry_run:
        print("\nDry run requested. No files will be copied.")
        return

    if not force:
        confirm = input("Proceed with copying these files? (yes/no): ").lower()
        if confirm != 'yes':
            print("Aborted by user.")
            return

    print("\n--- Applying Changes (Copying Files) ---")
    copied_count = 0
    error_count = 0
    for action_type, source_path, dest_path in actions:
        try:
            shutil.copy2(source_path, dest_path)
            print(f"Copied: '{os.path.basename(source_path)}' -> '{dest_path}'")
            copied_count += 1
        except OSError as e:
            print(f"Error copying '{os.path.basename(source_path)}' to '{dest_path}': {e}")
            error_count += 1
        except Exception as e:
             print(f"Unexpected error copying '{os.path.basename(source_path)}' to '{dest_path}': {e}")
             error_count += 1

    print("\n--- Summary ---")
    print(f"Files Copied: {copied_count}")
    print(f"Errors:       {error_count}")
    print(f"\nOriginal files in '{source_dir}' remain unchanged.")

# --- Main Execution ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Standardize GIF filenames (removing leading numbers like '123-') from a source directory and copy them to a destination directory." # Updated description
        )
    parser.add_argument("source_directory",
                        help="The source directory containing the original GIF files.")
    parser.add_argument("destination_directory",
                        help="The destination directory where standardized GIFs will be copied.")
    parser.add_argument("--dry-run", action="store_true",
                        help="Show planned copy actions without executing them.")
    parser.add_argument("--force", "-f", action="store_true",
                        help="Execute copy actions without asking for confirmation (use with caution!).")

    args = parser.parse_args()

    process_gifs(args.source_directory, args.destination_directory, args.dry_run, args.force)