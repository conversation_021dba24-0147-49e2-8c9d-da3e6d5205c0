package billing

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Subscription links a User to a Plan and serves as the source of truth for access control
type Subscription struct {
	ObjectID primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID       string             `json:"id,omitempty" bson:"-"`

	// Core relationships
	UserID    primitive.ObjectID `json:"userId" bson:"userId" validate:"required"`
	UserEmail string             `json:"userEmail" bson:"userEmail" validate:"required"`
	PlanID    primitive.ObjectID `json:"planId" bson:"planId" validate:"required"`

	// Subscription status and lifecycle
	Status SubscriptionStatus `json:"status" bson:"status" validate:"required"`

	// Payment provider information
	Provider               PaymentProvider `json:"provider" bson:"provider" validate:"required"`
	ProviderSubscriptionID string          `json:"providerSubscriptionId,omitempty" bson:"providerSubscriptionId,omitempty"`

	// Subscription dates
	StartDate    time.Time  `json:"startDate" bson:"startDate" validate:"required"`
	EndDate      time.Time  `json:"endDate" bson:"endDate" validate:"required"`
	TrialEndDate *time.Time `json:"trialEndDate,omitempty" bson:"trialEndDate,omitempty"`

	// Subscription configuration
	AutoRenew bool `json:"autoRenew" bson:"autoRenew"`

	// Cancellation information
	CancelledAt        *time.Time `json:"cancelledAt,omitempty" bson:"cancelledAt,omitempty"`
	CancellationReason string     `json:"cancellationReason,omitempty" bson:"cancellationReason,omitempty"`

	// Metadata
	CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" bson:"updatedAt"`
}

// SubscriptionStatus represents the status of a subscription
type SubscriptionStatus string

const (
	SubscriptionStatusActive    SubscriptionStatus = "active"
	SubscriptionStatusTrial     SubscriptionStatus = "trial"
	SubscriptionStatusCancelled SubscriptionStatus = "cancelled"
	SubscriptionStatusExpired   SubscriptionStatus = "expired"
	SubscriptionStatusSuspended SubscriptionStatus = "suspended"
)

// PaymentProvider represents the payment provider
type PaymentProvider string

const (
	PaymentProviderHotmart  PaymentProvider = "hotmart"
	PaymentProviderApplePay PaymentProvider = "applepay"
)

// IsValid validates the subscription status
func (ss SubscriptionStatus) IsValid() bool {
	switch ss {
	case SubscriptionStatusActive, SubscriptionStatusTrial, SubscriptionStatusCancelled,
		SubscriptionStatusExpired, SubscriptionStatusSuspended:
		return true
	default:
		return false
	}
}

// IsValid validates the payment provider
func (pp PaymentProvider) IsValid() bool {
	switch pp {
	case PaymentProviderHotmart, PaymentProviderApplePay:
		return true
	default:
		return false
	}
}

// Validate validates all fields in the subscription
func (s *Subscription) Validate() error {
	// Either UserID or UserEmail must be provided (or both)
	if s.UserID.IsZero() && s.UserEmail == "" {
		return errors.New(errors.Model, "subscription requires either userID or userEmail", errors.Validation, nil)
	}

	if s.UserEmail == "" {
		return errors.New(errors.Model, "subscription userEmail is required", errors.Validation, nil)
	}

	if s.PlanID.IsZero() {
		return errors.New(errors.Model, "subscription planID is required", errors.Validation, nil)
	}

	if !s.Status.IsValid() {
		return errors.New(errors.Model, "invalid subscription status", errors.Validation, nil)
	}

	if !s.Provider.IsValid() {
		return errors.New(errors.Model, "invalid payment provider", errors.Validation, nil)
	}

	if s.StartDate.IsZero() {
		return errors.New(errors.Model, "subscription start date is required", errors.Validation, nil)
	}

	if s.EndDate.IsZero() {
		return errors.New(errors.Model, "subscription end date is required", errors.Validation, nil)
	}

	if s.EndDate.Before(s.StartDate) {
		return errors.New(errors.Model, "subscription end date must be after start date", errors.Validation, nil)
	}

	if s.TrialEndDate != nil && s.TrialEndDate.Before(s.StartDate) {
		return errors.New(errors.Model, "trial end date must be after start date", errors.Validation, nil)
	}

	if s.TrialEndDate != nil && s.TrialEndDate.After(s.EndDate) {
		return errors.New(errors.Model, "trial end date must be before subscription end date", errors.Validation, nil)
	}

	return nil
}

// SetDefaults sets default values for the subscription
func (s *Subscription) SetDefaults() {
	now := time.Now()

	if s.CreatedAt.IsZero() {
		s.CreatedAt = now
	}
	s.UpdatedAt = now

	// Default auto-renew to true
	s.AutoRenew = true

	// Set default status to trial if trial end date is set and in the future
	if s.Status == "" {
		if s.TrialEndDate != nil && s.TrialEndDate.After(now) {
			s.Status = SubscriptionStatusTrial
		} else {
			s.Status = SubscriptionStatusActive
		}
	}
}

// IsActive checks if the subscription is currently active (including trial)
func (s *Subscription) IsActive() bool {
	now := time.Now()

	// Check if subscription is in an active state
	if s.Status != SubscriptionStatusActive && s.Status != SubscriptionStatusTrial {
		return false
	}

	// Check if subscription hasn't expired
	if now.After(s.EndDate) {
		return false
	}

	// For cancelled subscriptions, check if access should be revoked immediately
	if s.Status == SubscriptionStatusCancelled && s.CancelledAt != nil {
		// If should revoke access immediately (trial or within refund period), no access
		if s.ShouldRevokeAccessImmediately() {
			return false
		}
		// Otherwise, access continues until end date (post-7-day cancellation)
		return now.Before(s.EndDate)
	}

	return true
}

// IsInTrial checks if the subscription is currently in trial period
func (s *Subscription) IsInTrial() bool {
	if s.TrialEndDate == nil {
		return false
	}

	now := time.Now()
	return s.Status == SubscriptionStatusTrial && now.Before(*s.TrialEndDate)
}

// HasGracePeriod checks if the subscription has a grace period after cancellation
func (s *Subscription) HasGracePeriod() bool {
	if s.Status != SubscriptionStatusCancelled || s.CancelledAt == nil {
		return false
	}

	// No grace period if cancelled during trial
	if s.TrialEndDate != nil && s.CancelledAt.Before(*s.TrialEndDate) {
		return false
	}

	// Grace period exists if cancelled after trial and before end date
	now := time.Now()
	return now.Before(s.EndDate)
}

// Cancel cancels the subscription with a reason
func (s *Subscription) Cancel(reason string) {
	now := time.Now()
	s.Status = SubscriptionStatusCancelled
	s.CancelledAt = &now
	s.CancellationReason = reason
	s.AutoRenew = false
	s.UpdatedAt = now
}

// ShouldRevokeAccessImmediately determines if access should be revoked immediately
// This happens only when cancelled during trial period
func (s *Subscription) ShouldRevokeAccessImmediately() bool {
	// If cancelled during trial, revoke immediately
	return s.TrialEndDate != nil && s.CancelledAt != nil && s.CancelledAt.Before(*s.TrialEndDate)
}

// Expire marks the subscription as expired
func (s *Subscription) Expire() {
	s.Status = SubscriptionStatusExpired
	s.UpdatedAt = time.Now()
}

// Suspend suspends the subscription
func (s *Subscription) Suspend() {
	s.Status = SubscriptionStatusSuspended
	s.UpdatedAt = time.Now()
}

// Reactivate reactivates a suspended subscription
func (s *Subscription) Reactivate() {
	if s.Status == SubscriptionStatusSuspended {
		now := time.Now()
		if s.TrialEndDate != nil && now.Before(*s.TrialEndDate) {
			s.Status = SubscriptionStatusTrial
		} else {
			s.Status = SubscriptionStatusActive
		}
		s.UpdatedAt = now
	}
}
