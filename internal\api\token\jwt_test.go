package token

import (
	"os"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/golang-jwt/jwt"
)

func TestMain(m *testing.M) {
	// Set up test environment variables
	os.Setenv("API_ACCESS_JWT_KEY", "test-jwt-key")
	os.Setenv("API_REFRESH_JWT_KEY", "test-refresh-jwt-key")

	// Run tests
	code := m.Run()

	// Clean up
	os.Unsetenv("API_ACCESS_JWT_KEY")
	os.Unsetenv("API_REFRESH_JWT_KEY")

	os.Exit(code)
}

func TestCreateTokenWithRoles(t *testing.T) {
	user := &model.User{
		ID:    "test-user-id",
		Roles: []string{"teacher", "hr_company_123"},
	}

	tokens, err := Create(user)
	if err != nil {
		t.Fatalf("Create() error = %v", err)
	}

	if tokens.Access == "" {
		t.Error("Access token should not be empty")
	}
	if tokens.Refresh == "" {
		t.Error("Refresh token should not be empty")
	}

	// Parse and verify access token
	accessToken, err := jwt.Parse(tokens.Access, ParseToken)
	if err != nil {
		t.Fatalf("Failed to parse access token: %v", err)
	}

	claims, ok := accessToken.Claims.(jwt.MapClaims)
	if !ok {
		t.Fatal("Failed to get claims from access token")
	}

	// Check that roles are included in the token
	rolesInterface, exists := claims["roles"]
	if !exists {
		t.Error("Roles should be present in token claims")
	}

	rolesArray, ok := rolesInterface.([]interface{})
	if !ok {
		t.Error("Roles should be an array")
	}

	if len(rolesArray) != 2 {
		t.Errorf("Expected 2 roles, got %d", len(rolesArray))
	}

	// Check individual roles
	expectedRoles := map[string]bool{"teacher": false, "hr_company_123": false}
	for _, roleInterface := range rolesArray {
		if roleStr, ok := roleInterface.(string); ok {
			if _, exists := expectedRoles[roleStr]; exists {
				expectedRoles[roleStr] = true
			}
		}
	}

	for role, found := range expectedRoles {
		if !found {
			t.Errorf("Expected role %s not found in token", role)
		}
	}

	// Check backward compatibility - legacy role field should still exist
	legacyRole, exists := claims["role"]
	if !exists {
		t.Error("Legacy role field should be present for backward compatibility")
	}

	if legacyRole != "user" {
		t.Errorf("Expected legacy role to be 'user', got %v", legacyRole)
	}
}

func TestCreateTokenWithAdminUser(t *testing.T) {
	user := &model.User{
		ID:    "admin-user-id",
		Roles: []string{"admin"},
	}

	tokens, err := Create(user)
	if err != nil {
		t.Fatalf("Create() error = %v", err)
	}

	// Parse and verify access token
	accessToken, err := jwt.Parse(tokens.Access, ParseToken)
	if err != nil {
		t.Fatalf("Failed to parse access token: %v", err)
	}

	claims, ok := accessToken.Claims.(jwt.MapClaims)
	if !ok {
		t.Fatal("Failed to get claims from access token")
	}

	// Check that legacy role is set to admin
	legacyRole, exists := claims["role"]
	if !exists {
		t.Error("Legacy role field should be present")
	}

	if legacyRole != "admin" {
		t.Errorf("Expected legacy role to be 'admin', got %v", legacyRole)
	}

	// Check that roles array contains admin
	rolesInterface, exists := claims["roles"]
	if !exists {
		t.Error("Roles should be present in token claims")
	}

	rolesArray, ok := rolesInterface.([]interface{})
	if !ok {
		t.Error("Roles should be an array")
	}

	found := false
	for _, roleInterface := range rolesArray {
		if roleStr, ok := roleInterface.(string); ok && roleStr == "admin" {
			found = true
			break
		}
	}

	if !found {
		t.Error("Admin role should be present in roles array")
	}
}

func TestCreateTokenWithEmptyRoles(t *testing.T) {
	user := &model.User{
		ID:    "user-no-roles",
		Roles: []string{}, // Empty roles
	}

	tokens, err := Create(user)
	if err != nil {
		t.Fatalf("Create() error = %v", err)
	}

	// Parse and verify access token
	accessToken, err := jwt.Parse(tokens.Access, ParseToken)
	if err != nil {
		t.Fatalf("Failed to parse access token: %v", err)
	}

	claims, ok := accessToken.Claims.(jwt.MapClaims)
	if !ok {
		t.Fatal("Failed to get claims from access token")
	}

	// Check that default role is assigned
	rolesInterface, exists := claims["roles"]
	if !exists {
		t.Error("Roles should be present in token claims")
	}

	rolesArray, ok := rolesInterface.([]interface{})
	if !ok {
		t.Error("Roles should be an array")
	}

	if len(rolesArray) != 1 {
		t.Errorf("Expected 1 default role, got %d", len(rolesArray))
	}

	if rolesArray[0] != "user" {
		t.Errorf("Expected default role to be 'user', got %v", rolesArray[0])
	}
}

func TestGetClaimsFromTokenWithRoles(t *testing.T) {
	user := &model.User{
		ID:    "test-user-id",
		Roles: []string{"teacher", "student"},
	}

	tokens, err := Create(user)
	if err != nil {
		t.Fatalf("Create() error = %v", err)
	}

	// Get claims directly from token string
	details, err := GetClaimsFromToken(tokens.Access)
	if err != nil {
		t.Fatalf("GetClaimsFromToken() error = %v", err)
	}

	// Check user ID
	if details.Uid != "test-user-id" {
		t.Errorf("Expected Uid to be 'test-user-id', got %s", details.Uid)
	}

	// Check legacy role
	if details.Role != "user" {
		t.Errorf("Expected Role to be 'user', got %s", details.Role)
	}

	// Check roles array
	if len(details.Roles) != 2 {
		t.Errorf("Expected 2 roles, got %d", len(details.Roles))
	}

	expectedRoles := map[string]bool{"teacher": false, "student": false}
	for _, role := range details.Roles {
		if _, exists := expectedRoles[role]; exists {
			expectedRoles[role] = true
		}
	}

	for role, found := range expectedRoles {
		if !found {
			t.Errorf("Expected role %s not found in details", role)
		}
	}
}

func TestDetailsHelperMethods(t *testing.T) {
	details := &Details{
		Uid:   "test-user",
		Role:  "user",
		Roles: []string{"teacher", "hr_company_123", "student"},
	}

	// Test HasRole
	if !details.HasRole("teacher") {
		t.Error("HasRole should return true for 'teacher'")
	}
	if details.HasRole("admin") {
		t.Error("HasRole should return false for 'admin'")
	}

	// Test HasAnyRole
	if !details.HasAnyRole("admin", "teacher") {
		t.Error("HasAnyRole should return true when user has 'teacher'")
	}
	if details.HasAnyRole("admin", "director") {
		t.Error("HasAnyRole should return false when user has neither role")
	}

	// Test IsAdmin
	if details.IsAdmin() {
		t.Error("IsAdmin should return false for non-admin user")
	}

	// Test with admin user
	adminDetails := &Details{
		Uid:   "admin-user",
		Role:  "admin",
		Roles: []string{"admin"},
	}

	if !adminDetails.IsAdmin() {
		t.Error("IsAdmin should return true for admin user")
	}

	// Test GetRoles
	roles := details.GetRoles()
	if len(roles) != 3 {
		t.Errorf("GetRoles should return 3 roles, got %d", len(roles))
	}
}

func TestBackwardCompatibilityFallback(t *testing.T) {
	// Create a token manually without roles array to test fallback
	claims := jwt.MapClaims{
		"authorized": true,
		"uid":        "test-user",
		"role":       "admin",
		"exp":        time.Now().Add(time.Hour).Unix(),
		// Note: no "roles" field to test fallback
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(os.Getenv("API_ACCESS_JWT_KEY")))
	if err != nil {
		t.Fatalf("Failed to create test token: %v", err)
	}

	// Get claims directly from token string
	details, err := GetClaimsFromToken(tokenString)
	if err != nil {
		t.Fatalf("GetClaimsFromToken() error = %v", err)
	}

	// Check that fallback works
	if details.Role != "admin" {
		t.Errorf("Expected Role to be 'admin', got %s", details.Role)
	}

	if len(details.Roles) != 1 || details.Roles[0] != "admin" {
		t.Errorf("Expected Roles to fallback to ['admin'], got %v", details.Roles)
	}
}
