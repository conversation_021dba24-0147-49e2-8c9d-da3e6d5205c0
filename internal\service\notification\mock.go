package notification

import (
	"context"
	"fmt"
	"log"
	"sync"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

// MockNotifier implements the Notifier interface for testing purposes
type MockNotifier struct {
	mu           sync.RWMutex
	sentEmails   []MockEmail
	shouldFail   bool
	failureError error
}

// MockEmail represents an email that was sent through the mock notifier
type MockEmail struct {
	Type           string
	RecipientEmail string
	RecipientName  string
	Subject        string
	Content        string
	ResetLink      string
	PlanType       string
}

// NewMockNotifier creates a new mock notifier
func NewMockNotifier() *MockNotifier {
	return &MockNotifier{
		sentEmails: make([]MockEmail, 0),
		shouldFail: false,
	}
}

// SendPasswordReset mocks sending a password reset email
func (m *MockNotifier) SendPasswordReset(ctx context.Context, recipientEmail, recipientName, resetLink string) error {
	if m.shouldFail {
		return m.getFailureError("password_reset")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	email := MockEmail{
		Type:           "password_reset",
		RecipientEmail: recipientEmail,
		RecipientName:  recipientName,
		Subject:        "Password Reset - Dinbora",
		ResetLink:      resetLink,
	}

	m.sentEmails = append(m.sentEmails, email)

	log.Printf("Mock: Password reset email sent to %s (%s)", recipientEmail, recipientName)
	return nil
}

// SendSubscriptionConfirmation mocks sending a subscription confirmation email
func (m *MockNotifier) SendSubscriptionConfirmation(ctx context.Context, recipientEmail, recipientName, planType string) error {
	if m.shouldFail {
		return m.getFailureError("subscription_confirmation")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	email := MockEmail{
		Type:           "subscription_confirmation",
		RecipientEmail: recipientEmail,
		RecipientName:  recipientName,
		Subject:        "Subscription Confirmed - Dinbora",
		PlanType:       planType,
	}

	m.sentEmails = append(m.sentEmails, email)

	log.Printf("Mock: Subscription confirmation email sent to %s (%s) for plan %s", recipientEmail, recipientName, planType)
	return nil
}

// SendSubscriptionCancellation mocks sending a subscription cancellation email
func (m *MockNotifier) SendSubscriptionCancellation(ctx context.Context, recipientEmail, recipientName, planType string) error {
	if m.shouldFail {
		return m.getFailureError("subscription_cancellation")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	email := MockEmail{
		Type:           "subscription_cancellation",
		RecipientEmail: recipientEmail,
		RecipientName:  recipientName,
		Subject:        "Subscription Cancelled - Dinbora",
		PlanType:       planType,
	}

	m.sentEmails = append(m.sentEmails, email)

	log.Printf("Mock: Subscription cancellation email sent to %s (%s) for plan %s", recipientEmail, recipientName, planType)
	return nil
}

// SendNewUserWelcome mocks sending a welcome email to new users
func (m *MockNotifier) SendNewUserWelcome(ctx context.Context, recipientEmail, recipientName, resetLink, planType string) error {
	if m.shouldFail {
		return m.getFailureError("new_user_welcome")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	email := MockEmail{
		Type:           "new_user_welcome",
		RecipientEmail: recipientEmail,
		RecipientName:  recipientName,
		Subject:        "Welcome to Dinbora!",
		ResetLink:      resetLink,
		PlanType:       planType,
	}

	m.sentEmails = append(m.sentEmails, email)

	log.Printf("Mock: New user welcome email sent to %s (%s) for plan %s", recipientEmail, recipientName, planType)
	return nil
}

// SendGenericEmail mocks sending a generic email
func (m *MockNotifier) SendGenericEmail(ctx context.Context, recipientEmail, recipientName, subject, content string) error {
	if m.shouldFail {
		return m.getFailureError("generic_email")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	email := MockEmail{
		Type:           "generic_email",
		RecipientEmail: recipientEmail,
		RecipientName:  recipientName,
		Subject:        subject,
		Content:        content,
	}

	m.sentEmails = append(m.sentEmails, email)

	log.Printf("Mock: Generic email sent to %s (%s) with subject: %s", recipientEmail, recipientName, subject)
	return nil
}

// Test helper methods

// GetSentEmails returns all emails that were sent through the mock
func (m *MockNotifier) GetSentEmails() []MockEmail {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// Return a copy to prevent race conditions
	emails := make([]MockEmail, len(m.sentEmails))
	copy(emails, m.sentEmails)
	return emails
}

// GetSentEmailsByType returns all emails of a specific type
func (m *MockNotifier) GetSentEmailsByType(emailType string) []MockEmail {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var filtered []MockEmail
	for _, email := range m.sentEmails {
		if email.Type == emailType {
			filtered = append(filtered, email)
		}
	}
	return filtered
}

// GetSentEmailsCount returns the total number of emails sent
func (m *MockNotifier) GetSentEmailsCount() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return len(m.sentEmails)
}

// GetLastSentEmail returns the last email that was sent
func (m *MockNotifier) GetLastSentEmail() *MockEmail {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if len(m.sentEmails) == 0 {
		return nil
	}

	email := m.sentEmails[len(m.sentEmails)-1]
	return &email
}

// ClearSentEmails clears all sent emails (useful for test cleanup)
func (m *MockNotifier) ClearSentEmails() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.sentEmails = make([]MockEmail, 0)
}

// SetShouldFail configures the mock to fail on the next operation
func (m *MockNotifier) SetShouldFail(shouldFail bool, err error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.shouldFail = shouldFail
	m.failureError = err
}

// getFailureError returns the configured failure error or a default one
func (m *MockNotifier) getFailureError(operation string) error {
	if m.failureError != nil {
		return m.failureError
	}
	return errors.New(errors.Service, "mock failure", errors.Internal, fmt.Errorf("mock configured to fail"))
}

// HasSentEmailTo checks if an email was sent to a specific recipient
func (m *MockNotifier) HasSentEmailTo(recipientEmail string) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	for _, email := range m.sentEmails {
		if email.RecipientEmail == recipientEmail {
			return true
		}
	}
	return false
}

// HasSentEmailOfType checks if an email of a specific type was sent
func (m *MockNotifier) HasSentEmailOfType(emailType string) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	for _, email := range m.sentEmails {
		if email.Type == emailType {
			return true
		}
	}
	return false
}
