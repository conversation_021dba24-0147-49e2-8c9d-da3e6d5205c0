package content

import (
	"strings"

	"github.com/imdario/mergo"
)

type WalletGroup struct {
	Name            string            `json:"name" bson:"name"`
	Category        string            `json:"category" bson:"category"`
	Balance         float64           `json:"balance" bson:"balance"`
	Lesson          *Lesson           `json:"lesson" bson:"lesson"`
	Recommendations []*Recommendation `json:"recommendations" bson:"recommendations"`
}

type Recommendation struct {
	Name    string  `json:"name" bson:"name"`
	Ticker  string  `json:"ticker" bson:"ticker"`
	Type    string  `json:"type" bson:"type"`
	Balance float64 `json:"balance" bson:"balance"`
}

func (t *WalletGroup) PrepareCreate() error {
	t.Category = strings.TrimSpace(strings.ToLower(t.Category))

	return t.ValidateCreate()
}

func (t *WalletGroup) ValidateCreate() error {
	if t.Name == "" {
		return ErrWalletGroupRequiredName
	}

	if t.Category == "" {
		return ErrWalletGroupRequiredCategory
	}

	return nil
}

func (t *WalletGroup) PrepareUpdate(newWalletGroup *WalletGroup) error {
	if err := mergo.Merge(t, newWalletGroup, mergo.WithOverride); err != nil {
		return err
	}

	t.Category = strings.TrimSpace(strings.ToLower(t.Category))

	return t.ValidateUpdate()
}

func (t *WalletGroup) ValidateUpdate() error {
	return t.PrepareCreate()
}
