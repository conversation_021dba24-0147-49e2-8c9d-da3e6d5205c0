# Notification Service Usage Examples

The notification service provides direct access to individual providers through public fields.

## Direct Provider Access (Simplified)

### In Auth Service (ForgotPassword)

```go
func (s *service) ForgotPassword(ctx context.Context, userEmail string) error {
    foundUser, err := s.UserService.FindByEmail(ctx, userEmail)
    if err != nil {
        return err
    }

    userToken, err := token.Standalone(foundUser)
    if err != nil {
        return err
    }

    resetLink := os.Getenv("APP_URL") + "/nova-senha/" + url.QueryEscape(base64.StdEncoding.WithPadding(base64.NoPadding).EncodeToString([]byte(userToken)))

    // Choose specific provider - Gmail in this case
    if s.NotificationService.GmailNotifier != nil {
        return s.NotificationService.GmailNotifier.SendPasswordReset(ctx, foundUser.Email, foundUser.Name, resetLink)
    }

    // Fallback to Brevo if Gmail is not available
    if s.NotificationService.BrevoNotifier != nil {
        return s.NotificationService.BrevoNotifier.SendPasswordReset(ctx, foundUser.Email, foundUser.Name, resetLink)
    }

    return errors.New(errors.Service, "no notification providers available", errors.Internal, nil)
}
```

### In Subscription Service

```go
func (s *service) SendSubscriptionEmail(ctx context.Context, user *model.User, planType string) error {
    // Use Brevo for subscription emails (better templates)
    if s.NotificationService.BrevoNotifier != nil {
        return s.NotificationService.BrevoNotifier.SendSubscriptionConfirmation(ctx, user.Email, user.Name, planType)
    }

    // Fallback to Gmail
    if s.NotificationService.GmailNotifier != nil {
        return s.NotificationService.GmailNotifier.SendSubscriptionConfirmation(ctx, user.Email, user.Name, planType)
    }

    return errors.New(errors.Service, "no notification providers available", errors.Internal, nil)
}
```

## Provider Availability

- **Gmail**: Available if `GMAIL_CLIENT_ID`, `GMAIL_CLIENT_SECRET`, and `GMAIL_REFRESH_TOKEN` are configured
- **Brevo**: Available if `BREVO_API_KEY` is configured

Both providers can be configured simultaneously, giving you the flexibility to choose which one to use for different types of emails.

## Benefits of Direct Provider Access

1. **No Duplicate Emails**: Choose exactly which provider to use
2. **Simple Access**: Direct field access without method calls
3. **Provider Optimization**: Use Gmail for simple emails, Brevo for templated emails
4. **Fallback Strategy**: Try preferred provider first, fallback to secondary
5. **Cost Control**: Route emails through different providers based on volume/cost
6. **Feature Utilization**: Use provider-specific features (templates, analytics, etc.)

## Simplified Usage Pattern

```go
// Direct access to providers
if s.NotificationService.GmailNotifier != nil {
    err := s.NotificationService.GmailNotifier.SendPasswordReset(ctx, email, name, link)
}

if s.NotificationService.BrevoNotifier != nil {
    err := s.NotificationService.BrevoNotifier.SendPasswordReset(ctx, email, name, link)
}
```
