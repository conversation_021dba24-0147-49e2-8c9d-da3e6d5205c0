package financialdna

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.FINANCIAL_DNA_COLLECTION),
	}

	// Create index on userID field for efficient lookups
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "userID", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		db.Client().Disconnect(context.Background())
		log.Println("warning: failed to create index on userId field")
	}

	return repo
}

// CRUD
func (m *mongoDB) Create(ctx context.Context, tree *financialdna.FinancialDNATree) (string, error) {
	tree.CreatedAt = time.Now()
	tree.UpdatedAt = time.Now()

	insertedResult, err := m.collection.InsertOne(ctx, tree)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, errors.FinancialDNAConflictExists, errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, errors.FinancialDNACreateFailed, errors.Internal, err)
	}
	return insertedResult.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (m *mongoDB) Find(ctx context.Context, id primitive.ObjectID) (*financialdna.FinancialDNATree, error) {
	var tree financialdna.FinancialDNATree
	err := m.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&tree)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.FinancialDNAConflictExists, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.FinancialDNAFindFailed, errors.Internal, err)
	}
	return &tree, nil
}

func (m *mongoDB) FindByUser(ctx context.Context, userID primitive.ObjectID) (*financialdna.FinancialDNATree, error) {
	var tree financialdna.FinancialDNATree
	err := m.collection.FindOne(ctx, bson.M{"userID": userID.Hex()}).Decode(&tree)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.FinancialDNAConflictExists, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.FinancialDNAFindByUserFailed, errors.Internal, err)
	}
	return &tree, nil
}

func (m *mongoDB) Update(ctx context.Context, tree *financialdna.FinancialDNATree) error {
	if tree.ObjectID.IsZero() {
		return errors.New(errors.Repository, errors.FinancialDNAInvalidID, errors.BadRequest, nil)
	}

	tree.UpdatedAt = time.Now()

	opts := options.Update().SetUpsert(false)
	result, err := m.collection.UpdateOne(ctx,
		bson.D{{Key: "_id", Value: tree.ObjectID}},
		bson.D{{Key: "$set", Value: tree}},
		opts,
	)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, errors.FinancialDNAConflictUpdate, errors.Conflict, err)
		}
		return errors.New(errors.Repository, errors.FinancialDNAUpdateFailed, errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, errors.FinancialDNANotFound, errors.NotFound, nil)
	}
	return nil
}

func (m *mongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.collection.DeleteOne(ctx, bson.D{{Key: "_id", Value: id}})
	if err != nil {
		return errors.New(errors.Repository, errors.FinancialDNADeleteFailed, errors.Internal, err)
	}
	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, errors.FinancialDNANotFound, errors.NotFound, nil)
	}
	return nil
}
