package api

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/controller"
	"github.com/labstack/echo/v4"
)

const (
	LegacyAPIVersion  = "v1/"
	CurrentAPIVersion = "/v2"
)

func (s *Server) RegisterRoutes(ctx context.Context, controllers ...controller.RouteHandler) error {

	// Creates a versioned group.
	legacyGroup := s.echo.Group(LegacyAPIVersion)
	currentGroup := s.echo.Group(CurrentAPIVersion)

	// Register health check routes
	s.registerHealthCheckRoutes(currentGroup)

	// Registering routes
	for _, c := range controllers {
		c.RegisterRoutes(ctx, legacyGroup, currentGroup)
	}

	return nil
}

// registerHealthCheckRoutes registers the health check endpoint
func (s *Server) registerHealthCheckRoutes(group *echo.Group) {
	health := group.Group("/health")
	health.GET("", healthCheck)
}

// healthCheck checks the server status.
func healthCheck(c echo.Context) error {
	return c.JSON(http.StatusOK, map[string]string{"status": "ok"})
}
