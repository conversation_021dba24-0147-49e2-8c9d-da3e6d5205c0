package financialsheet

import (
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TestDeleteCategory_RemovesFromRecords tests that deleting a category
// removes it from both the categories collection and the financial sheet records
func TestDeleteCategory_RemovesFromRecords(t *testing.T) {
	// This test requires a real MongoDB connection
	// Skip if running in CI or without MongoDB
	if testing.Short() {
		t.Skip("Skipping integration test")
	}

	// Note: This test would require a real MongoDB connection
	// For demonstration purposes, we'll create a mock scenario
	// In a real integration test, you would:
	// 1. Set up a test MongoDB instance
	// 2. Create a financial sheet record with categories
	// 3. Delete a category
	// 4. Verify it's removed from both collections and records

	t.Log("Integration test for DeleteCategory - removes category from records")

	// Mock data structure to demonstrate the expected behavior
	userID := "test-user-123"
	categoryID := primitive.NewObjectID()
	categoryIdentifier := financialsheet.CategoryIdentifier("test_category")

	// Create a mock financial sheet record with the category in month data
	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   userID,
		UserName: "Test User",
		YearData: map[int]financialsheet.YearData{
			2025: {
				"08": financialsheet.MonthData{
					Categories: []financialsheet.CategoryCard{
						{
							ID:         categoryID.Hex(),
							Identifier: string(categoryIdentifier),
							Name:       "Test Category",
							Type:       financialsheet.CategoryTypeExpense,
							Value:      monetary.Amount(1000),
						},
						{
							ID:         "other-category-id",
							Identifier: "other_category",
							Name:       "Other Category",
							Type:       financialsheet.CategoryTypeIncome,
							Value:      monetary.Amount(2000),
						},
					},
					Transactions: []financialsheet.Transaction{
						{
							ObjectID: primitive.NewObjectID(),
							Category: categoryIdentifier,
							Value:    monetary.Amount(500),
							Date:     time.Now(),
							Type:     financialsheet.CategoryTypeExpense,
						},
					},
				},
			},
		},
	}

	// Create the category to be deleted
	category := &financialsheet.Category{
		ObjectID:   categoryID,
		ID:         categoryID.Hex(),
		User:       userID,
		Identifier: categoryIdentifier,
		Name:       "Test Category",
		Type:       financialsheet.CategoryTypeExpense,
	}

	// Simulate the deletion process
	t.Log("Before deletion:")
	t.Logf("  Record has %d categories in month 08/2025", len(record.YearData[2025]["08"].Categories))

	// Find the category that should be deleted
	monthData := record.YearData[2025]["08"]
	var foundCategory bool
	for _, cat := range monthData.Categories {
		if cat.ID == categoryID.Hex() || cat.Identifier == string(categoryIdentifier) {
			foundCategory = true
			break
		}
	}
	assert.True(t, foundCategory, "Category should exist in record before deletion")

	// Simulate the cleanup logic from our DeleteCategory implementation
	updated := false
	for year, yearData := range record.YearData {
		for month, monthData := range yearData {
			// Filter out the category to be deleted
			filteredCategories := make([]financialsheet.CategoryCard, 0, len(monthData.Categories))
			for _, cat := range monthData.Categories {
				if cat.ID != categoryID.Hex() && cat.Identifier != string(category.Identifier) {
					filteredCategories = append(filteredCategories, cat)
				} else {
					updated = true
				}
			}

			if updated {
				monthData.Categories = filteredCategories
				record.YearData[year][month] = monthData
			}
		}
	}

	// Verify the category was removed
	t.Log("After deletion:")
	t.Logf("  Record has %d categories in month 08/2025", len(record.YearData[2025]["08"].Categories))

	// Check that the category is no longer in the record
	monthDataAfter := record.YearData[2025]["08"]
	var foundCategoryAfter bool
	for _, cat := range monthDataAfter.Categories {
		if cat.ID == categoryID.Hex() || cat.Identifier == string(categoryIdentifier) {
			foundCategoryAfter = true
			break
		}
	}
	assert.False(t, foundCategoryAfter, "Category should be removed from record after deletion")

	// Verify other categories remain
	assert.Len(t, monthDataAfter.Categories, 1, "Other categories should remain")
	assert.Equal(t, "other_category", monthDataAfter.Categories[0].Identifier, "Other category should remain unchanged")

	// Verify transactions are not affected (they should be handled separately)
	assert.Len(t, monthDataAfter.Transactions, 1, "Transactions should not be affected by category deletion")

	t.Log("✅ Category successfully removed from financial sheet record")
}

// TestDeleteCategory_HandlesMultipleMonths tests that category deletion
// removes the category from all months where it appears
func TestDeleteCategory_HandlesMultipleMonths(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test")
	}

	t.Log("Testing category deletion across multiple months")

	userID := "test-user-456"
	categoryID := primitive.NewObjectID()
	categoryIdentifier := financialsheet.CategoryIdentifier("multi_month_category")

	// Create a record with the same category in multiple months
	record := &financialsheet.Record{
		ObjectID: primitive.NewObjectID(),
		UserID:   userID,
		YearData: map[int]financialsheet.YearData{
			2025: {
				"07": financialsheet.MonthData{
					Categories: []financialsheet.CategoryCard{
						{
							ID:         categoryID.Hex(),
							Identifier: string(categoryIdentifier),
							Name:       "Multi Month Category",
							Value:      monetary.Amount(500),
						},
					},
				},
				"08": financialsheet.MonthData{
					Categories: []financialsheet.CategoryCard{
						{
							ID:         categoryID.Hex(),
							Identifier: string(categoryIdentifier),
							Name:       "Multi Month Category",
							Value:      monetary.Amount(750),
						},
					},
				},
			},
		},
	}

	category := &financialsheet.Category{
		ObjectID:   categoryID,
		Identifier: categoryIdentifier,
	}

	// Count categories before deletion
	totalCategoriesBefore := 0
	for _, yearData := range record.YearData {
		for _, monthData := range yearData {
			totalCategoriesBefore += len(monthData.Categories)
		}
	}
	require.Equal(t, 2, totalCategoriesBefore, "Should have 2 categories before deletion")

	// Simulate deletion across all months
	for year, yearData := range record.YearData {
		for month, monthData := range yearData {
			filteredCategories := make([]financialsheet.CategoryCard, 0, len(monthData.Categories))
			for _, cat := range monthData.Categories {
				if cat.ID != categoryID.Hex() && cat.Identifier != string(category.Identifier) {
					filteredCategories = append(filteredCategories, cat)
				}
			}
			monthData.Categories = filteredCategories
			record.YearData[year][month] = monthData
		}
	}

	// Count categories after deletion
	totalCategoriesAfter := 0
	for _, yearData := range record.YearData {
		for _, monthData := range yearData {
			totalCategoriesAfter += len(monthData.Categories)
		}
	}

	assert.Equal(t, 0, totalCategoriesAfter, "All instances of the category should be removed")
	t.Log("✅ Category successfully removed from all months")
}
