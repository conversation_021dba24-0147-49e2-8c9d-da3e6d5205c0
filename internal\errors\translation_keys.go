package errors

// Translation keys for error messages.
//
// These constants map to keys defined in the translation files (locales/*.json).
// Keys are organized by package in alphabetical order (e.g., auth, financialSheet, financialDna, user, etc.).
//
// Key format:
//   Key<Package>Error<SpecificError> = "<package>.error.<specificError>"
//
// Examples:
//   - User not found (from the user package):
//       KeyUserErrorNotFound = "user.error.notFound"
//   - User not found by ID (from the user package):
//       KeyUserErrorNotFoundById = "user.error.notFoundById"

const (
	// AI Assistant
	// AI Context
	// Apple
	// Auth
	KeyAuthErrorInvalidLoginInput                  = "auth.error.invalidLoginInput"
	KeyAuthErrorLoginValidationFailed              = "auth.error.loginValidationFailed"
	KeyAuthErrorInvalidRegisterInput               = "auth.error.invalidRegisterInput"
	KeyAuthErrorFailedToParseFormData              = "auth.error.failedToParseFormData"
	KeyAuthErrorInvalidOboardingAgeRange           = "auth.error.invalidOboardingAgeRange"
	KeyAuthErrorInvalidOboardingFinancialSituation = "auth.error.invalidOboardingFinancialSituation"
	KeyAuthErrorInvalidOboardingFinancialGoal      = "auth.error.invalidOboardingFinancialGoal"
	KeyAuthErrorInvalidOboardingPersonalInterest   = "auth.error.invalidOboardingPersonalInterest"
	KeyAuthErrorFailedToProcessPhoto               = "auth.error.failedToProcessPhoto"
	KeyAuthErrorInvalidFileType                    = "auth.error.invalidFileType"
	KeyAuthErrorFileTooLarge                       = "auth.error.fileTooLarge"
	KeyAuthErrorInvalidRefreshTokenInput           = "auth.error.invalidRefreshTokenInput"
	KeyAuthErrorInvalidForgotPasswordInput         = "auth.error.invalidForgotPasswordInput"
	KeyAuthErrorInvalidResetPasswordInput          = "auth.error.invalidResetPasswordInput"
	KeyAuthErrorInvalidCheckPasswordInput          = "auth.error.invalidCheckPasswordInput"
	KeyAuthErrorUserNotLoggedIn                    = "auth.error.userNotLoggedIn"
	KeyAuthErrorInvalidAdminLoginInput             = "auth.error.invalidAdminLoginInput"
	KeyAuthErrorInvalidHRLoginInput                = "auth.error.invalidHRLoginInput"

	KeyAuthErrorFailedToUploadPhoto               = "auth.error.failedToUploadPhoto"
	KeyAuthErrorFailedToCreateToken               = "auth.error.failedToCreateToken"
	KeyAuthErrorFailedToRetrieveUserAfterCreation = "auth.error.failedToRetrieveUserAfterCreation"
	KeyAuthErrorBrevoNotifierNotAvailable         = "auth.error.brevoNotifierNotAvailable"
	KeyAuthErrorUserNotAdmin                      = "auth.error.userNotAdmin"
	KeyAuthErrorUserNotHR                         = "auth.error.userNotHR"
	// Billing
	// Content
	// Dashboard
	// Dreamboard
	// Financial DNA
	// Financial Sheet
	// Firebase
	// Gamification
	// Google
	// I18n
	// League
	// League Ranking
	// Notification
	// Progression
	KeyProgressionErrorConflict                    = "progression.error.conflict"
	KeyProgressionErrorCreateFailed                = "progression.error.createFailed"
	KeyProgressionErrorInvalidIdFormat             = "progression.error.invalidIdFormat"
	KeyProgressionErrorNotFound                    = "progression.error.notFound"
	KeyProgressionErrorFindFailed                  = "progression.error.findFailed"
	KeyProgressionErrorNotFoundForUser             = "progression.error.notFoundForUser"
	KeyProgressionErrorFindByUserFailed            = "progression.error.findByUserFailed"
	KeyProgressionErrorUpdateConflict              = "progression.error.updateConflict"
	KeyProgressionErrorUpdateFailed                = "progression.error.updateFailed"
	KeyProgressionErrorNotFoundForUpdate           = "progression.error.notFoundForUpdate"
	KeyProgressionErrorDeleteFailed                = "progression.error.deleteFailed"
	KeyProgressionErrorNotFoundForDeletion         = "progression.error.notFoundForDeletion"
	KeyProgressionErrorFindTrailProgressionsFailed = "progression.error.findTrailProgressionsFailed"
	// RBAC
	// S3
	// User
	KeyUserErrorHashPassword         = "user.error.hashPassword"
	KeyUserErrorForbidden            = "user.error.forbidden"
	KeyUserErrorInvalidCredentials   = "user.error.invalidCredentials"
	KeyUserErrorResetPassword        = "user.error.resetPassword"
	KeyUserErrorMergeFailed          = "user.error.mergeFailed"
	KeyUserErrorProcessPassword      = "user.error.processPassword"
	KeyUserErrorEmailRequired        = "user.error.emailRequired"
	KeyUserErrorInvalidEmail         = "user.error.invalidEmail"
	KeyUserErrorEmptyId              = "user.error.emptyId"
	KeyUserErrorNameRequired         = "user.error.nameRequired"
	KeyUserErrorPasswordRequired     = "user.error.passwordRequired"
	KeyUserErrorReferralCodeRequired = "user.error.referralCodeRequired"
	KeyUserErrorSetRoleNotAllowed    = "user.error.setRoleNotAllowed"
	KeyUserErrorPhoneRequired        = "user.error.phoneRequired"
	KeyUserErrorPasswordRequirements = "user.error.passwordRequirements"

	KeyUserErrorConflict                    = "user.error.conflict"
	KeyUserErrorNotFoundById                = "user.error.notFoundById"
	KeyUserErrorNotFoundByEmail             = "user.error.notFoundByEmail"
	KeyUserErrorNotFoundByReferral          = "user.error.notFoundByReferral"
	KeyUserErrorDeletedNotFoundByEmail      = "user.error.deletedNotFoundByEmail"
	KeyUserErrorConflictUpdate              = "user.error.conflictUpdate"
	KeyUserErrorNotFoundForUpdate           = "user.error.notFoundForUpdate"
	KeyUserErrorNotFoundForDeletion         = "user.error.notFoundForDeletion"
	KeyUserErrorCreateFailed                = "user.error.createFailed"
	KeyUserErrorDeletedCreateFailed         = "user.error.deletedCreateFailed"
	KeyUserErrorFindByIdFailed              = "user.error.findByIdFailed"
	KeyUserErrorFindAllFailed               = "user.error.findAllFailed"
	KeyUserErrorDecodeUserFailed            = "user.error.decodeUserFailed"
	KeyUserErrorAdminUsersNotFound          = "user.error.adminUsersNotFound"
	KeyUserErrorDecodeAdminUserFailed       = "user.error.decodeAdminUserFailed"
	KeyUserErrorFindByEmailFailed           = "user.error.findByEmailFailed"
	KeyUserErrorFindByReferralFailed        = "user.error.findByReferralFailed"
	KeyUserErrorFindByReferringUserIdFailed = "user.error.findByReferringUserIdFailed"
	KeyUserErrorCursorError                 = "user.error.cursorError"
	KeyUserErrorFindWithFilterFailed        = "user.error.findWithFilterFailed"
	KeyUserErrorDeletedFindByEmailFailed    = "user.error.deletedFindByEmailFailed"
	KeyUserErrorInvalidId                   = "user.error.invalidId"
	KeyUserErrorUpdateFailed                = "user.error.updateFailed"
	KeyUserErrorDeleteFailed                = "user.error.deleteFailed"
	KeyUserErrorDeletedConflictExists       = "user.error.deletedConflictExists"
	// Vault
)
