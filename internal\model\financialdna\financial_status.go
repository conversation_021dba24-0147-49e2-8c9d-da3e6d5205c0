package financialdna

import (
	"encoding/json"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

// FinancialStatus represents the financial profile category of a family member
type FinancialStatus string

const (
	// FinancialStatusUndefined represents an uninitialized or unknown financial status
	FinancialStatusUndefined FinancialStatus = "undefined"
	// FinancialStatusInvestor represents a person with diversified investments and emergency funds
	FinancialStatusInvestor FinancialStatus = "investor"
	// FinancialStatusBalanced represents a person with good financial health but no significant investments
	FinancialStatusBalanced FinancialStatus = "balanced"
	// FinancialStatusIndebted represents a person with active debts and financial difficulties
	FinancialStatusIndebted FinancialStatus = "indebted"
	// FinancialStatusOverindebted represents a person with overdue debts and significant financial issues
	FinancialStatusOverindebted FinancialStatus = "overindebted"
)

// IsValid checks if the financial status is valid
func (fs FinancialStatus) IsValid() bool {
	switch fs {
	case FinancialStatusUndefined, FinancialStatusInvestor, FinancialStatusBalanced, FinancialStatusIndebted, FinancialStatusOverindebted:
		return true
	default:
		return false
	}
}

// Validate checks if the financial status is valid and returns an error if not
func (fs FinancialStatus) Validate() error {
	if !fs.IsValid() {
		return errors.New(errors.Model, "invalid financial status value", errors.Validation, nil)
	}
	return nil
}

// String implements fmt.Stringer
func (fs FinancialStatus) String() string {
	return fs.StringValue()
}

// StringValue returns the string representation of the financial status
func (fs FinancialStatus) StringValue() string {
	switch fs {
	case FinancialStatusUndefined:
		return ""
	case FinancialStatusInvestor:
		return "Investidor"
	case FinancialStatusBalanced:
		return "Equilibrado"
	case FinancialStatusIndebted:
		return "Endividado"
	case FinancialStatusOverindebted:
		return "Superendividado"
	default:
		return "undefined"
	}
}

// MarshalJSON marshals the financial status to JSON
func (fs FinancialStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(fs.StringValue())
}

// GetEmoji returns the emoji representation of the financial status
func (fs FinancialStatus) GetEmoji() string {
	switch fs {
	case FinancialStatusUndefined:
		return "❓"
	case FinancialStatusInvestor:
		return "✨"
	case FinancialStatusBalanced:
		return "⚖️"
	case FinancialStatusIndebted:
		return "⚠️"
	case FinancialStatusOverindebted:
		return "🚨"
	default:
		return ""
	}
}

// GetColor returns the color code associated with the financial status
func (fs FinancialStatus) GetColor() string {
	switch fs {
	case FinancialStatusUndefined:
		return "#808080" // Gray for none/undefined
	case FinancialStatusInvestor:
		return "#00FF00" // Green
	case FinancialStatusBalanced:
		return "#0000FF" // Blue
	case FinancialStatusIndebted:
		return "#FFFF00" // Yellow
	case FinancialStatusOverindebted:
		return "#FF0000" // Red
	default:
		return "#808080" // Gray for undefined
	}
}
