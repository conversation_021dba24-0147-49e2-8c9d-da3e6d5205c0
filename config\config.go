package config

import (
	"log"
	"os"

	"github.com/joho/godotenv"
)

// Env function to config enviroment variables.
func Env() error {
	if os.Getenv("APP_URL") == "" {
		if err := godotenv.Load(); err != nil {
			return err
		}
		log.Println("Config Mode: Development")
	} else if os.Getenv("APP_URL") != "https://dinbora.com.br" {
		log.Println("Config Mode: Test")
	} else {
		log.Println("Config Mode: Production")
	}
	return nil
}

// EnvFromFile loads environment variables from specified .env file path
func EnvFromFile(path string) error {
	if os.Getenv("APP_URL") == "" {
		if err := godotenv.Load(path); err != nil {
			return err
		}
		log.Println("Config Mode: Development")
	} else if os.Getenv("APP_URL") != "https://dinbora.com.br" {
		log.Println("Config Mode: Test")
	} else {
		log.Println("Config Mode: Production")
	}
	return nil
}
