package billing

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type paymentMongoDB struct {
	collection *mongo.Collection
}

func NewPaymentRepository(db *mongo.Database) PaymentRepository {
	repo := &paymentMongoDB{
		collection: db.Collection(repository.BILLING_PAYMENTS_COLLECTION),
	}

	// Create indexes
	repo.createIndexes()

	return repo
}

func (r *paymentMongoDB) createIndexes() {
	ctx := context.Background()

	// Index on userId for finding user payments
	_, err := r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "userId", Value: 1}},
			Options: options.Index().SetName("paymentUserId"),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on payments.userId field: %v", err)
	}

	// Index on subscriptionId for finding subscription payments
	_, err = r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "subscriptionId", Value: 1}},
			Options: options.Index().SetName("paymentSubscriptionId").SetSparse(true),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on payments.subscriptionId field: %v", err)
	}

	// Unique index on provider and providerTransactionId for webhook deduplication
	_, err = r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "provider", Value: 1}, {Key: "providerTransactionId", Value: 1}},
			Options: options.Index().SetName("paymentProviderTransactionId").SetUnique(true),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on payments.provider+providerTransactionId fields: %v", err)
	}

	// Index on status for finding payments by status
	_, err = r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "status", Value: 1}},
			Options: options.Index().SetName("paymentStatus"),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on payments.status field: %v", err)
	}

	// Index on createdAt for date range queries
	_, err = r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "createdAt", Value: 1}},
			Options: options.Index().SetName("paymentCreatedAt"),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on payments.createdAt field: %v", err)
	}
}

// Read operations
func (r *paymentMongoDB) Find(ctx context.Context, id primitive.ObjectID) (*billing.Payment, error) {
	var payment billing.Payment
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&payment)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "payment not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find payment", errors.Internal, err)
	}

	payment.ID = payment.ObjectID.Hex()
	return &payment, nil
}

func (r *paymentMongoDB) FindByUser(ctx context.Context, userID primitive.ObjectID) ([]*billing.Payment, error) {
	// Sort by createdAt descending to get most recent payments first
	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}})
	
	cursor, err := r.collection.Find(ctx, bson.M{"userId": userID}, opts)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find user payments", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var payments []*billing.Payment
	for cursor.Next(ctx) {
		var payment billing.Payment
		if err := cursor.Decode(&payment); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode payment", errors.Internal, err)
		}
		payment.ID = payment.ObjectID.Hex()
		payments = append(payments, &payment)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding user payments", errors.Internal, err)
	}

	return payments, nil
}

func (r *paymentMongoDB) FindBySubscription(ctx context.Context, subscriptionID primitive.ObjectID) ([]*billing.Payment, error) {
	// Sort by createdAt descending to get most recent payments first
	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}})
	
	cursor, err := r.collection.Find(ctx, bson.M{"subscriptionId": subscriptionID}, opts)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find subscription payments", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var payments []*billing.Payment
	for cursor.Next(ctx) {
		var payment billing.Payment
		if err := cursor.Decode(&payment); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode payment", errors.Internal, err)
		}
		payment.ID = payment.ObjectID.Hex()
		payments = append(payments, &payment)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding subscription payments", errors.Internal, err)
	}

	return payments, nil
}

func (r *paymentMongoDB) FindByProviderTransactionID(ctx context.Context, provider billing.PaymentProvider, providerTransactionID string) (*billing.Payment, error) {
	filter := bson.M{
		"provider":              provider,
		"providerTransactionId": providerTransactionID,
	}

	var payment billing.Payment
	err := r.collection.FindOne(ctx, filter).Decode(&payment)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "payment not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find payment by provider transaction ID", errors.Internal, err)
	}

	payment.ID = payment.ObjectID.Hex()
	return &payment, nil
}

func (r *paymentMongoDB) FindByStatus(ctx context.Context, status billing.PaymentStatus) ([]*billing.Payment, error) {
	// Sort by createdAt descending to get most recent payments first
	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}})
	
	cursor, err := r.collection.Find(ctx, bson.M{"status": status}, opts)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find payments by status", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var payments []*billing.Payment
	for cursor.Next(ctx) {
		var payment billing.Payment
		if err := cursor.Decode(&payment); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode payment", errors.Internal, err)
		}
		payment.ID = payment.ObjectID.Hex()
		payments = append(payments, &payment)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding payments by status", errors.Internal, err)
	}

	return payments, nil
}

func (r *paymentMongoDB) FindByDateRange(ctx context.Context, startDate, endDate string) ([]*billing.Payment, error) {
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid start date format", errors.Validation, err)
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid end date format", errors.Validation, err)
	}

	// Add 24 hours to end date to include the entire day
	end = end.AddDate(0, 0, 1)

	filter := bson.M{
		"createdAt": bson.M{
			"$gte": start,
			"$lt":  end,
		},
	}

	// Sort by createdAt descending to get most recent payments first
	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}})
	
	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find payments by date range", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var payments []*billing.Payment
	for cursor.Next(ctx) {
		var payment billing.Payment
		if err := cursor.Decode(&payment); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode payment", errors.Internal, err)
		}
		payment.ID = payment.ObjectID.Hex()
		payments = append(payments, &payment)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding payments by date range", errors.Internal, err)
	}

	return payments, nil
}

// Write operations
func (r *paymentMongoDB) Create(ctx context.Context, payment *billing.Payment) (string, error) {
	payment.SetDefaults()
	
	if err := payment.Validate(); err != nil {
		return "", err
	}

	result, err := r.collection.InsertOne(ctx, payment)
	if err != nil {
		// Check for duplicate key error (provider + providerTransactionId)
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, "payment with this provider transaction ID already exists", errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, "failed to create payment", errors.Internal, err)
	}

	objectID, ok := result.InsertedID.(primitive.ObjectID)
	if !ok {
		return "", errors.New(errors.Repository, "failed to get inserted payment ID", errors.Internal, nil)
	}

	return objectID.Hex(), nil
}

func (r *paymentMongoDB) Update(ctx context.Context, payment *billing.Payment) error {
	payment.SetDefaults()
	
	if err := payment.Validate(); err != nil {
		return err
	}

	filter := bson.M{"_id": payment.ObjectID}
	update := bson.M{"$set": payment}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update payment", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "payment not found", errors.NotFound, nil)
	}

	return nil
}

func (r *paymentMongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete payment", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "payment not found", errors.NotFound, nil)
	}

	return nil
}
