package migrations

import (
	"context"
	"fmt"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// UpdateFinancialSheetCategories implements the Migration interface for adding compensation category
type UpdateFinancialSheetCategories struct {
	db *mongo.Database
}

func NewUpdateFinancialSheetCategories(db *mongo.Database) *UpdateFinancialSheetCategories {
	return &UpdateFinancialSheetCategories{
		db: db,
	}
}

func (m *UpdateFinancialSheetCategories) Name() string {
	return "update_financial_sheet_categories_20250821_fix000"
}

func (m *UpdateFinancialSheetCategories) Up(ctx context.Context) error {
	collection := m.db.Collection(repository.FINANCIAL_SHEETS_COLLECTION_CATEGORIES)

	// Process all valid categories
	for _, category := range financialsheet.GetAllCategories() {
		// Skip undefined category
		if category.Identifier == financialsheet.CategoryIdentifierUndefined {
			continue
		}

		// Replace if exists, insert if not (upsert)
		filter := bson.M{"identifier": category.Identifier}
		log.Printf("Processing category: %s\n", category)

		opts := options.Replace().SetUpsert(true)
		_, err := collection.ReplaceOne(ctx, filter, category, opts)
		if err != nil {
			return errors.New(errors.Migrator, errors.Message(fmt.Sprintf("failed to upsert %s category", category.Name)), errors.Internal, err)
		}
		log.Printf("Successfully upserted %s category\n", category.Name)
	}

	return nil
}
