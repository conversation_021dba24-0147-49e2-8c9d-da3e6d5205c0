package user

import (
	"net/http"
	"strconv"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/labstack/echo/v4"
)

// FindEmployeesByHR returns all employees that were referred by the HR user
// This endpoint is protected by HR role middleware and returns a list of users
// where ReferringUserID matches the requesting HR user's ID
func (uc *controller) FindEmployeesByHR() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Call service to get employees for this HR user
		employees, err := uc.Service.FindEmployeesByHR(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		// Sanitize user data before returning (remove sensitive information)
		var sanitizedEmployees []*model.User
		for _, employee := range employees {
			sanitizedEmployees = append(sanitizedEmployees, employee.Sanitize())
		}

		return c.JSON(http.StatusOK, sanitizedEmployees)
	}
}

// FindTopEngagers returns the top engagers for the HR user's company based on the number of achievements and dcoins
func (uc *controller) FindTopEngagers() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Query param to limit the number of top engagers returned
		limit, err := strconv.Atoi(c.QueryParam("limit"))
		if err != nil {
			limit = 0 // Unlimited
		}

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Call service to get top engagers for this HR user's company
		topEngagers, err := uc.Service.FindTopEngagers(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		if limit > 0 && limit < len(topEngagers) {
			topEngagers = topEngagers[:limit]
		}

		return c.JSON(http.StatusOK, topEngagers) // Return the top engagers as JSON response
	}
}

// FindJourneyProgression returns the journey progression for the HR user's company based on the user's progression
func (uc *controller) FindJourneyProgression() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Call service to get journey progression for this HR user's company
		journeyProgression, err := uc.Service.FindJourneyProgression(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, journeyProgression)
	}
}

// FindDreamsProgression returns the dreams progress for the HR user's company based on the number of registered dreams and completed dreams
func (uc *controller) FindDreamsProgression() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Call service to get dreams progress for this HR user's company
		dreamsProgress, err := uc.Service.FindDreamsProgression(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, dreamsProgress)
	}
}

// FindFinancialProfileDistribution returns the financial profile distribution for the HR user's company based on the user's pontuation in the "perfil-financeiro" challenge phase points
func (uc *controller) FindFinancialProfileDistribution() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Call service to get financial profile distribution for this HR user's company
		financialProfileDistribution, err := uc.Service.FindFinancialProfileDistribution(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, financialProfileDistribution)
	}
}

// FindAgeRanges returns the age ranges for the HR user's company based on the user's age
func (uc *controller) FindAgeRanges() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Call service to get age ranges for this HR user's company
		ageRanges, err := uc.Service.FindAgeRanges(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, ageRanges)
	}
}

// FindFinancialSituations returns the financial situations for the HR user's company based on the user's financial situation
func (uc *controller) FindFinancialSituations() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Call service to get financial situations for this HR user's company
		financialSituations, err := uc.Service.FindFinancialSituations(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, financialSituations)
	}
}

// FindFinancialProfileEvolution returns the financial profile evolution for the HR user's company based on the user's pontuation in the "perfil-financeiro" challenge phase points
func (uc *controller) FindFinancialProfileEvolution() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Call service to get financial profile evolution for this HR user's company
		financialProfileEvolution, err := uc.Service.FindFinancialProfileEvolution(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, financialProfileEvolution)
	}
}

// FindPersonalInterests returns the personal interests for the HR user's company based on the user's personal interests
func (uc *controller) FindPersonalInterests() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Call service to get personal interests for this HR user's company
		personalInterests, err := uc.Service.FindPersonalInterests(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, personalInterests)
	}
}

// FindFinancialGoals returns the financial goals for the HR user's company based on the user's financial goals
func (uc *controller) FindFinancialGoals() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Call service to get financial goals for this HR user's company
		financialGoals, err := uc.Service.FindFinancialGoals(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, financialGoals)
	}
}
