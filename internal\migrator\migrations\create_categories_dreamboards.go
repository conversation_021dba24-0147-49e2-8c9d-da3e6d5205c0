package migrations

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// CreateCategoriesDreamboards implements the Migration interface for creating default dreamboards
type CreateCategoriesDreamboards struct {
	db *mongo.Database
}

func NewCreateCategoriesDreamboards(db *mongo.Database) *CreateCategoriesDreamboards {
	return &CreateCategoriesDreamboards{
		db: db,
	}
}

func (m *CreateCategoriesDreamboards) Name() string {
	return "create_categories_dreamboards_update_names"
}

func (m *CreateCategoriesDreamboards) Up(ctx context.Context) error {
	dreamboardCollection := m.db.Collection(repository.DREAMBOARDS_COLLECTION)

	cursor, err := dreamboardCollection.Find(ctx, bson.D{})
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var board dreamboard.Dreamboard
		if err := cursor.Decode(&board); err != nil {
			return err
		}

		categories := []dreamboard.Category{
			dreamboard.Professional,
			dreamboard.Financial,
			dreamboard.Leisure,
			dreamboard.Emotional,
			dreamboard.Intellectual,
			dreamboard.Spiritual,
			dreamboard.Physical,
			dreamboard.Intimate,
			dreamboard.Social,
			dreamboard.Familial,
		}

		session, err := dreamboardCollection.Database().Client().StartSession()
		if err != nil {
			return errors.New(errors.Migrator, "failed to start session", errors.Internal, err)
		}
		defer session.EndSession(ctx)

		callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
			// First, remove all existing categories
			_, err := dreamboardCollection.UpdateOne(
				sessCtx,
				bson.M{"_id": cursor.Current.Lookup("_id")},
				bson.D{{Key: "$set", Value: bson.D{
					{Key: "categories", Value: []dreamboard.Category{}},
					{Key: "updatedAt", Value: time.Now()},
				}}},
			)
			if err != nil {
				return nil, errors.New(errors.Repository, "failed to remove existing categories", errors.Internal, err)
			}
			log.Println("Removed existing categories")

			// Then add all categories in the desired order
			for i := range categories {
				categories[i].ObjectID = primitive.NewObjectID()

				update := bson.D{
					{Key: "$push", Value: bson.D{{Key: "categories", Value: categories[i]}}},
					{Key: "$set", Value: bson.D{{Key: "updatedAt", Value: time.Now()}}},
				}

				result, err := dreamboardCollection.UpdateOne(
					sessCtx,
					bson.M{"_id": cursor.Current.Lookup("_id")},
					update,
				)
				if err != nil {
					return nil, errors.New(errors.Repository, "failed to add category", errors.Internal, err)
				}

				if result.MatchedCount > 0 {
					log.Println("Category", categories[i].Identifier, "added successfully.")
				} else {
					return nil, errors.New(errors.Repository, "dreamboard not found", errors.NotFound, nil)
				}
			}

			return nil, nil
		}

		_, err = session.WithTransaction(ctx, callback)
		if err != nil {
			return errors.New(errors.Repository, "failed to create categories in transaction", errors.Internal, err)
		}
	}

	return cursor.Err()
}
