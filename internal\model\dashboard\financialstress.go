package dashboard

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// FinancialStress represents the main financial stress analysis DTO
type FinancialStress struct {
	StressScore        *StressScore            `json:"stressScore"`
	CommitmentAnalysis *CommitmentAnalysis     `json:"commitmentAnalysis"`
	ExpenseAnalysis    *ExpenseAnalysis        `json:"expenseAnalysis"`
	PaymentMethods     []*PaymentMethodSummary `json:"paymentMethods"`
	SpendingVariation  *SpendingVariation      `json:"spendingVariation"`
	Period             string                  `json:"period"`
	AnalysisDate       time.Time               `json:"analysisDate"`
}

// StressScore represents the calculated stress score with interpretation
type StressScore struct {
	Score   float64 `json:"score"`   // 0-100 scale
	Level   string  `json:"level"`   // "verylow", "low", "medium", "high", "veryhigh"
	Label   string  `json:"label"`   // User-friendly description
	Insight string  `json:"insight"` // Actionable insight based on score
}

// CommitmentAnalysis represents the breakdown of financial commitments
type CommitmentAnalysis struct {
	FixedExpense        CommitmentDetail `json:"fixedExpense"`
	VariableExpense     CommitmentDetail `json:"variableExpense"`
	Debt                CommitmentDetail `json:"debt"`
	AvailableForSavings monetary.Amount  `json:"availableForSavings"`
}

// CommitmentDetail represents a specific commitment category
type CommitmentDetail struct {
	Amount     monetary.Amount `json:"amount"`
	Percentage float64         `json:"percentage"`
}

// ExpenseAnalysis represents detailed expense breakdown
type ExpenseAnalysis struct {
	FixedExpenses    ExpenseCategory      `json:"fixedExpenses"`
	VariableExpenses ExpenseCategory      `json:"variableExpenses"`
	Debts            ExpenseCategory      `json:"debts"`
	Categories       []*CategoryBreakdown `json:"categories"`
}

// ExpenseCategory represents a category of expenses
type ExpenseCategory struct {
	Total                monetary.Amount      `json:"total"`
	Percentage           float64              `json:"percentage"`
	Count                int                  `json:"count"`
	MoneySourceBreakdown []*MoneySourceDetail `json:"moneySourceBreakdown"`
}

// CategoryBreakdown represents spending by financial category
type CategoryBreakdown struct {
	CategoryIdentifier   string               `json:"categoryIdentifier"`
	CategoryName         string               `json:"categoryName"`
	Icon                 string               `json:"icon"`
	Amount               monetary.Amount      `json:"amount"`
	Percentage           float64              `json:"percentage"`
	IsFixed              bool                 `json:"isFixed"`
	IsDebt               bool                 `json:"isDebt"`
	Count                int                  `json:"count"`
	MoneySourceBreakdown []*MoneySourceDetail `json:"moneySourceBreakdown"`
	MonthlyVariation     *MonthlyVariation    `json:"monthlyVariation,omitempty"`
	MonthlyEvolution     []*MonthlyData       `json:"monthlyEvolution,omitempty"`
}

// MoneySourceDetail represents spending breakdown by money source within a category
type MoneySourceDetail struct {
	MoneySourceIdentifier string          `json:"moneySourceIdentifier"`
	MoneySourceName       string          `json:"moneySourceName"`
	Icon                  string          `json:"icon"`
	Amount                monetary.Amount `json:"amount"`
	Percentage            float64         `json:"percentage"` // percentage within this category
	Count                 int             `json:"count"`
	Color                 string          `json:"color"`
}

// MonthlyVariation represents month-over-month spending change
type MonthlyVariation struct {
	Percentage float64         `json:"percentage"` // percentage change from previous month
	Direction  string          `json:"direction"`  // "increase", "decrease", "stable"
	Amount     monetary.Amount `json:"amount"`     // absolute change amount
}

// MonthlyData represents spending data for a specific month
type MonthlyData struct {
	Label string          `json:"label"` // e.g., "Jan", "Feb", "Mar"
	Value monetary.Amount `json:"value"` // total spending for that month
}

// PaymentMethodSummary represents spending by payment method
type PaymentMethodSummary struct {
	PaymentMethodId   string          `json:"paymentMethodId"`
	PaymentMethodName string          `json:"paymentMethodName"`
	Icon              string          `json:"icon"`
	Amount            monetary.Amount `json:"amount"`
	Percentage        float64         `json:"percentage"`
	Transactions      int             `json:"transactions"`
}

// SpendingVariation represents changes compared to previous period
type SpendingVariation struct {
	TotalChange    VariationDetail         `json:"totalChange"`
	FixedChange    VariationDetail         `json:"fixedChange"`
	VariableChange VariationDetail         `json:"variableChange"`
	DebtChange     VariationDetail         `json:"debtChange"`
	TopIncreases   []*MoneySourceVariation `json:"topIncreases"`
	TopReductions  []*MoneySourceVariation `json:"topReductions"`
}

// VariationDetail represents a change in spending
type VariationDetail struct {
	Amount     monetary.Amount `json:"amount"`
	Percentage float64         `json:"percentage"`
	Direction  string          `json:"direction"` // "increase", "decrease", "stable"
}

// CategoryVariation represents spending change for a specific category
type CategoryVariation struct {
	CategoryIdentifier string          `json:"categoryIdentifier"`
	CategoryName       string          `json:"categoryName"`
	Amount             monetary.Amount `json:"amount"`
	Percentage         float64         `json:"percentage"`
	Direction          string          `json:"direction"`
}

// MoneySourceVariation represents spending change for a specific money source
type MoneySourceVariation struct {
	MoneySourceIdentifier string          `json:"moneySourceIdentifier"`
	MoneySourceName       string          `json:"moneySourceName"`
	Icon                  string          `json:"icon"`
	Amount                monetary.Amount `json:"amount"`
	Percentage            float64         `json:"percentage"`
	Direction             string          `json:"direction"`
}
