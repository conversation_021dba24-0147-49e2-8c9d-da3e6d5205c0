# Implementation Guide: Event-Driven Progression System

## Overview

This document provides step-by-step implementation guidance for refactoring the progression system from a complex nested architecture to a simple, maintainable event-driven system while maintaining 100% backward compatibility.

## Implementation Phases

### Phase 1: Foundation Setup (Week 1)

#### Step 1.1: Create New Model Files

**Create `internal/model/progression/event.go`**:
```go
package progression

import (
    "time"
    "go.mongodb.org/mongo-driver/bson/primitive"
)

type ProgressEvent struct {
    ID        primitive.ObjectID `json:"-" bson:"_id,omitempty"`
    UserID    string            `json:"userId" bson:"userId"`
    TrailID   string            `json:"trailId" bson:"trailId"`
    ItemID    string            `json:"itemId" bson:"itemId"`        // lesson/challenge identifier
    ItemType  ProgressType      `json:"itemType" bson:"itemType"`    // LESSON | CHALLENGE
    Action    ActionType        `json:"action" bson:"action"`        // STARTED | COMPLETED
    ContentID string            `json:"contentId" bson:"contentId"`  // specific content within item
    Choice    *Choice           `json:"choice,omitempty" bson:"choice,omitempty"`
    Data      map[string]interface{} `json:"data,omitempty" bson:"data,omitempty"`
    Timestamp time.Time         `json:"timestamp" bson:"timestamp"`
    
    // Migration fields
    LegacyID  string            `json:"legacyId,omitempty" bson:"legacyId,omitempty"`
    Migrated  bool              `json:"migrated" bson:"migrated"`
}

type Choice struct {
    Identifier string `json:"identifier" bson:"identifier"`
    Next       string `json:"next" bson:"next"`
}

func (e *ProgressEvent) Validate() error {
    if e.UserID == "" {
        return ErrRequiredUserValue
    }
    
    if e.TrailID == "" {
        return ErrRequiredTrailValue
    }
    
    if e.ItemID == "" {
        return ErrRequiredItemValue
    }
    
    if e.ItemType != ProgressTypeLesson && e.ItemType != ProgressTypeChallenge {
        return ErrTypeMustBeLessonOrChallenge
    }
    
    if e.Action != ActionStarted && e.Action != ActionCompleted {
        return ErrInvalidAction
    }
    
    if e.Action == ActionCompleted && e.Choice == nil {
        return ErrRequiredChoiceValue
    }
    
    if e.Choice != nil {
        if e.Choice.Identifier == "" {
            return ErrRequiredChoiceIdentifierValue
        }
        if e.Choice.Next == "" {
            return ErrRequiredChoiceNextValue
        }
    }
    
    return nil
}

func (e *ProgressEvent) SetDefaults() {
    if e.Timestamp.IsZero() {
        e.Timestamp = time.Now()
    }
    if e.Data == nil {
        e.Data = make(map[string]interface{})
    }
}
```

**Create `internal/model/progression/summary.go`**:
```go
package progression

import "time"

type ProgressSummary struct {
    UserID      string                   `json:"userId" bson:"userId"`
    Trails      map[string]*TrailSummary `json:"trails" bson:"trails"`
    LastUpdated time.Time                `json:"lastUpdated" bson:"lastUpdated"`
    Version     int                      `json:"version" bson:"version"`
}

type TrailSummary struct {
    ID                  string            `json:"id" bson:"id"`
    LessonsCompleted    map[string]bool   `json:"lessonsCompleted" bson:"lessonsCompleted"`
    ChallengesCompleted map[string]bool   `json:"challengesCompleted" bson:"challengesCompleted"`
    CurrentItem         string            `json:"currentItem" bson:"currentItem"`
    ProgressPercent     int               `json:"progressPercent" bson:"progressPercent"`
    IsCompleted         bool              `json:"isCompleted" bson:"isCompleted"`
    TotalRewards        int               `json:"totalRewards" bson:"totalRewards"`
    LessonsRewarded     map[string]bool   `json:"lessonsRewarded" bson:"lessonsRewarded"`
    ChallengesRewarded  map[string]bool   `json:"challengesRewarded" bson:"challengesRewarded"`
    
    // Detailed progress tracking
    LessonProgress      map[string]*LessonProgress    `json:"lessonProgress" bson:"lessonProgress"`
    ChallengeProgress   map[string]*ChallengeProgress `json:"challengeProgress" bson:"challengeProgress"`
}

type LessonProgress struct {
    Current   string    `json:"current" bson:"current"`
    Completed bool      `json:"completed" bson:"completed"`
    Rewarded  bool      `json:"rewarded" bson:"rewarded"`
    Path      []string  `json:"path" bson:"path"`
}

type ChallengeProgress struct {
    Current   string                      `json:"current" bson:"current"`
    Completed bool                        `json:"completed" bson:"completed"`
    Rewarded  bool                        `json:"rewarded" bson:"rewarded"`
    Phases    map[string]*PhaseProgress   `json:"phases" bson:"phases"`
}

type PhaseProgress struct {
    Current   string    `json:"current" bson:"current"`
    Completed bool      `json:"completed" bson:"completed"`
    Path      []string  `json:"path" bson:"path"`
}

func NewProgressSummary(userID string) *ProgressSummary {
    return &ProgressSummary{
        UserID:      userID,
        Trails:      make(map[string]*TrailSummary),
        LastUpdated: time.Now(),
        Version:     1,
    }
}

func NewTrailSummary(trailID string) *TrailSummary {
    return &TrailSummary{
        ID:                  trailID,
        LessonsCompleted:    make(map[string]bool),
        ChallengesCompleted: make(map[string]bool),
        LessonsRewarded:     make(map[string]bool),
        ChallengesRewarded:  make(map[string]bool),
        LessonProgress:      make(map[string]*LessonProgress),
        ChallengeProgress:   make(map[string]*ChallengeProgress),
    }
}
```

**Create `internal/model/progression/types.go`**:
```go
package progression

type ProgressType string

const (
    ProgressTypeLesson    ProgressType = "LESSON"
    ProgressTypeChallenge ProgressType = "CHALLENGE"
)

type ActionType string

const (
    ActionStarted   ActionType = "STARTED"
    ActionCompleted ActionType = "COMPLETED"
)

type RewardType string

const (
    RewardTypeCoin    RewardType = "COIN"
    RewardTypeDiamond RewardType = "DIAMOND"
)

// Legacy compatibility
func (pt ProgressType) String() string {
    return string(pt)
}

func (at ActionType) String() string {
    return string(at)
}

func (rt RewardType) String() string {
    return string(rt)
}
```

#### Step 1.2: Update Repository Interface

**Update `internal/repository/progression/repository.go`**:
```go
package progression

import (
    "context"
    "github.com/dsoplabs/dinbora-backend/internal/model/progression"
)

type EventReader interface {
    GetUserEvents(ctx context.Context, userID string, limit int) ([]*progression.ProgressEvent, error)
    GetTrailEvents(ctx context.Context, userID, trailID string) ([]*progression.ProgressEvent, error)
    GetEventsByTimeRange(ctx context.Context, userID string, start, end time.Time) ([]*progression.ProgressEvent, error)
}

type EventWriter interface {
    CreateEvent(ctx context.Context, event *progression.ProgressEvent) error
    CreateEventsBatch(ctx context.Context, events []*progression.ProgressEvent) error
}

type SummaryRepository interface {
    GetProgressSummary(ctx context.Context, userID string) (*progression.ProgressSummary, error)
    SaveProgressSummary(ctx context.Context, summary *progression.ProgressSummary) error
    InvalidateProgressSummary(ctx context.Context, userID string) error
}

type MigrationRepository interface {
    IsUserMigrated(ctx context.Context, userID string) (bool, error)
    MarkUserMigrated(ctx context.Context, userID string) error
    GetLegacyProgression(ctx context.Context, userID string) (*progression.Progression, error)
    CreateLegacyProgression(ctx context.Context, progression *progression.Progression) error
    UpdateLegacyProgression(ctx context.Context, progression *progression.Progression) error
}

// Legacy interface (keep existing)
type Reader interface {
    Find(ctx context.Context, id string) (*progression.Progression, error)
    FindByUser(ctx context.Context, userId string) (*progression.Progression, error)
    FindAllAchievements(ctx context.Context, userId string) ([]*progression.Achievement, error)
    FindForCards(ctx context.Context, userId string) (map[string]*progression.Trail, error)
}

type Writer interface {
    Create(ctx context.Context, progression *progression.Progression) error
    Update(ctx context.Context, progression *progression.Progression) error
    Delete(ctx context.Context, id string) error
}

// New combined interface
type Repository interface {
    EventReader
    EventWriter
    SummaryRepository
    MigrationRepository
    
    // Legacy support (during migration)
    Reader
    Writer
}
```

#### Step 1.3: Create MongoDB Implementation

**Create `internal/repository/progression/event_store.go`**:
```go
package progression

import (
    "context"
    "time"
    
    "go.mongodb.org/mongo-driver/bson"
    "go.mongodb.org/mongo-driver/mongo"
    "go.mongodb.org/mongo-driver/mongo/options"
    
    "github.com/dsoplabs/dinbora-backend/internal/model/progression"
)

type eventStore struct {
    client     *mongo.Client
    database   *mongo.Database
    events     *mongo.Collection
    summaries  *mongo.Collection
    migrations *mongo.Collection
}

func NewEventStore(client *mongo.Client, databaseName string) *eventStore {
    db := client.Database(databaseName)
    return &eventStore{
        client:     client,
        database:   db,
        events:     db.Collection("progress_events"),
        summaries:  db.Collection("progress_summaries"),
        migrations: db.Collection("user_migrations"),
    }
}

func (es *eventStore) CreateEvent(ctx context.Context, event *progression.ProgressEvent) error {
    event.SetDefaults()
    
    if err := event.Validate(); err != nil {
        return err
    }
    
    _, err := es.events.InsertOne(ctx, event)
    return err
}

func (es *eventStore) CreateEventsBatch(ctx context.Context, events []*progression.ProgressEvent) error {
    if len(events) == 0 {
        return nil
    }
    
    docs := make([]interface{}, len(events))
    for i, event := range events {
        event.SetDefaults()
        if err := event.Validate(); err != nil {
            return err
        }
        docs[i] = event
    }
    
    _, err := es.events.InsertMany(ctx, docs)
    return err
}

func (es *eventStore) GetUserEvents(ctx context.Context, userID string, limit int) ([]*progression.ProgressEvent, error) {
    filter := bson.M{"userId": userID}
    opts := options.Find().SetSort(bson.D{{"timestamp", 1}})
    
    if limit > 0 {
        opts.SetLimit(int64(limit))
    }
    
    cursor, err := es.events.Find(ctx, filter, opts)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)
    
    var events []*progression.ProgressEvent
    err = cursor.All(ctx, &events)
    return events, err
}

func (es *eventStore) GetTrailEvents(ctx context.Context, userID, trailID string) ([]*progression.ProgressEvent, error) {
    filter := bson.M{
        "userId":  userID,
        "trailId": trailID,
    }
    opts := options.Find().SetSort(bson.D{{"timestamp", 1}})
    
    cursor, err := es.events.Find(ctx, filter, opts)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)
    
    var events []*progression.ProgressEvent
    err = cursor.All(ctx, &events)
    return events, err
}

func (es *eventStore) GetEventsByTimeRange(ctx context.Context, userID string, start, end time.Time) ([]*progression.ProgressEvent, error) {
    filter := bson.M{
        "userId": userID,
        "timestamp": bson.M{
            "$gte": start,
            "$lte": end,
        },
    }
    opts := options.Find().SetSort(bson.D{{"timestamp", 1}})
    
    cursor, err := es.events.Find(ctx, filter, opts)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)
    
    var events []*progression.ProgressEvent
    err = cursor.All(ctx, &events)
    return events, err
}

func (es *eventStore) GetProgressSummary(ctx context.Context, userID string) (*progression.ProgressSummary, error) {
    var summary progression.ProgressSummary
    err := es.summaries.FindOne(ctx, bson.M{"userId": userID}).Decode(&summary)
    if err != nil {
        if err == mongo.ErrNoDocuments {
            return nil, errors.New(errors.Repository, "progress summary not found", errors.NotFound, nil)
        }
        return nil, err
    }
    return &summary, nil
}

func (es *eventStore) SaveProgressSummary(ctx context.Context, summary *progression.ProgressSummary) error {
    summary.LastUpdated = time.Now()
    summary.Version++
    
    filter := bson.M{"userId": summary.UserID}
    update := bson.M{"$set": summary}
    opts := options.Update().SetUpsert(true)
    
    _, err := es.summaries.UpdateOne(ctx, filter, update, opts)
    return err
}

func (es *eventStore) InvalidateProgressSummary(ctx context.Context, userID string) error {
    _, err := es.summaries.DeleteOne(ctx, bson.M{"userId": userID})
    return err
}

func (es *eventStore) IsUserMigrated(ctx context.Context, userID string) (bool, error) {
    count, err := es.migrations.CountDocuments(ctx, bson.M{"userId": userID})
    if err != nil {
        return false, err
    }
    return count > 0, nil
}

func (es *eventStore) MarkUserMigrated(ctx context.Context, userID string) error {
    migration := bson.M{
        "userId":      userID,
        "migratedAt":  time.Now(),
        "version":     "v2",
    }
    
    _, err := es.migrations.InsertOne(ctx, migration)
    return err
}

// CreateIndexes creates necessary indexes for optimal performance
func (es *eventStore) CreateIndexes(ctx context.Context) error {
    indexes := []mongo.IndexModel{
        {
            Keys: bson.D{{"userId", 1}, {"timestamp", 1}},
        },
        {
            Keys: bson.D{{"userId", 1}, {"trailId", 1}, {"timestamp", 1}},
        },
        {
            Keys: bson.D{{"userId", 1}, {"itemId", 1}, {"itemType", 1}},
        },
        {
            Keys: bson.D{{"userId", 1}, {"action", 1}, {"timestamp", 1}},
        },
    }
    
    _, err := es.events.Indexes().CreateMany(ctx, indexes)
    return err
}
```

### Phase 2: Service Layer Implementation (Week 2)

#### Step 2.1: Create Progress Calculator

**Create `internal/service/progression/calculator.go`**:
```go
package progression

import (
    "context"
    "sort"
    
    "github.com/dsoplabs/dinbora-backend/internal/model/progression"
    "github.com/dsoplabs/dinbora-backend/internal/model/content"
    contentService "github.com/dsoplabs/dinbora-backend/internal/service/content"
)

type Calculator struct {
    trailService content.TrailService
}

func NewCalculator(trailService contentService.Service) *Calculator {
    return &Calculator{
        trailService: trailService,
    }
}

func (c *Calculator) CalculateProgressFromEvents(ctx context.Context, events []*progression.ProgressEvent, userID string) (*progression.ProgressSummary, error) {
    summary := progression.NewProgressSummary(userID)
    
    if len(events) == 0 {
        return summary, nil
    }
    
    // Sort events by timestamp
    sort.Slice(events, func(i, j int) bool {
        return events[i].Timestamp.Before(events[j].Timestamp)
    })
    
    // Group events by trail
    trailEvents := make(map[string][]*progression.ProgressEvent)
    for _, event := range events {
        trailEvents[event.TrailID] = append(trailEvents[event.TrailID], event)
    }
    
    // Calculate progress for each trail
    for trailID, events := range trailEvents {
        trailContent, err := c.trailService.Find(ctx, trailID)
        if err != nil {
            // Skip if trail not found or access denied
            continue
        }
        
        trailSummary := c.calculateTrailProgress(events, trailContent)
        summary.Trails[trailID] = trailSummary
    }
    
    return summary, nil
}

func (c *Calculator) calculateTrailProgress(events []*progression.ProgressEvent, trailContent *content.Trail) *progression.TrailSummary {
    summary := progression.NewTrailSummary(trailContent.ID)
    
    // Process events chronologically
    for _, event := range events {
        switch event.ItemType {
        case progression.ProgressTypeLesson:
            c.processLessonEvent(summary, event)
        case progression.ProgressTypeChallenge:
            c.processChallengeEvent(summary, event)
        }
    }
    
    // Calculate overall progress percentage
    c.calculateProgressPercentage(summary, trailContent)
    
    return summary
}

func (c *Calculator) processLessonEvent(summary *progression.TrailSummary, event *progression.ProgressEvent) {
    lessonID := event.ItemID
    
    if summary.LessonProgress[lessonID] == nil {
        summary.LessonProgress[lessonID] = &progression.LessonProgress{
            Path: make([]string, 0),
        }
    }
    
    progress := summary.LessonProgress[lessonID]
    
    switch event.Action {
    case progression.ActionStarted:
        if event.ContentID != "" {
            // Add to path if not already present
            found := false
            for _, contentID := range progress.Path {
                if contentID == event.ContentID {
                    found = true
                    break
                }
            }
            if !found {
                progress.Path = append(progress.Path, event.ContentID)
            }
            progress.Current = event.ContentID
        }
        
    case progression.ActionCompleted:
        progress.Completed = true
        summary.LessonsCompleted[lessonID] = true
        
        // Check if rewarded
        if event.Choice != nil && c.isRewardChoice(event.Choice.Next) {
            progress.Rewarded = true
            summary.LessonsRewarded[lessonID] = true
            summary.TotalRewards++
        }
        
        // Update current item
        summary.CurrentItem = c.findNextItem(summary, lessonID)
    }
}

func (c *Calculator) processChallengeEvent(summary *progression.TrailSummary, event *progression.ProgressEvent) {
    challengeID := event.ItemID
    
    if summary.ChallengeProgress[challengeID] == nil {
        summary.ChallengeProgress[challengeID] = &progression.ChallengeProgress{
            Phases: make(map[string]*progression.PhaseProgress),
        }
    }
    
    challenge := summary.ChallengeProgress[challengeID]
    
    // Extract phase from event data or content ID
    phaseID := c.extractPhaseID(event)
    
    if challenge.Phases[phaseID] == nil {
        challenge.Phases[phaseID] = &progression.PhaseProgress{
            Path: make([]string, 0),
        }
    }
    
    phase := challenge.Phases[phaseID]
    
    switch event.Action {
    case progression.ActionStarted:
        if event.ContentID != "" {
            found := false
            for _, contentID := range phase.Path {
                if contentID == event.ContentID {
                    found = true
                    break
                }
            }
            if !found {
                phase.Path = append(phase.Path, event.ContentID)
            }
            phase.Current = event.ContentID
            challenge.Current = phaseID
        }
        
    case progression.ActionCompleted:
        phase.Completed = true
        
        // Check if all phases completed
        allPhasesCompleted := true
        for _, p := range challenge.Phases {
            if !p.Completed {
                allPhasesCompleted = false
                break
            }
        }
        
        if allPhasesCompleted {
            challenge.Completed = true
            summary.ChallengesCompleted[challengeID] = true
            
            // Check if rewarded
            if event.Choice != nil && c.isRewardChoice(event.Choice.Next) {
                challenge.Rewarded = true
                summary.ChallengesRewarded[challengeID] = true
                summary.TotalRewards++
            }
        }
    }
}

func (c *Calculator) calculateProgressPercentage(summary *progression.TrailSummary, trailContent *content.Trail) {
    totalItems := len(trailContent.Lessons)
    if trailContent.Challenge != nil {
        totalItems++ // Challenge counts as one item
    }
    
    if totalItems == 0 {
        summary.ProgressPercent = 0
        return
    }
    
    completedItems := len(summary.LessonsCompleted)
    if len(summary.ChallengesCompleted) > 0 {
        completedItems++
    }
    
    percentage := (completedItems * 100) / totalItems
    
    // Legacy business rule: max 90% until challenge completed
    if percentage >= 100 && trailContent.Challenge != nil && len(summary.ChallengesCompleted) == 0 {
        percentage = 90
    }
    
    summary.ProgressPercent = percentage
    summary.IsCompleted = percentage >= 100
}

func (c *Calculator) isRewardChoice(next string) bool {
    return next == string(progression.RewardTypeCoin) || next == string(progression.RewardTypeDiamond)
}

func (c *Calculator) findNextItem(summary *progression.TrailSummary, currentItem string) string {
    // Simple implementation - would need trail content to determine actual next item
    return currentItem
}

func (c *Calculator) extractPhaseID(event *progression.ProgressEvent) string {
    // Extract phase ID from event data or use a default
    if phaseID, ok := event.Data["phaseId"].(string); ok {
        return phaseID
    }
    return "phase_1" // Default phase
}
```

#### Step 2.2: Create Event Service

**Create `internal/service/progression/event_service.go`**:
```go
package progression

import (
    "context"
    "fmt"
    "log"
    "time"
    
    "github.com/dsoplabs/dinbora-backend/internal/model/progression"
    "github.com/dsoplabs/dinbora-backend/internal/cache"
    "github.com/dsoplabs/dinbora-backend/internal/repository/progression"
    "github.com/dsoplabs/dinbora-backend/internal/service/vault"
)

type eventService struct {
    repository      progressionRepo.Repository
    calculator      *Calculator
    vaultService    vault.Service
    cache           cache.CacheService
    migrationMode   bool
}

func NewEventService(
    repo progressionRepo.Repository,
    calculator *Calculator,
    vaultService vault.Service,
    cache cache.CacheService,
) Service {
    return &eventService{
        repository:    repo,
        calculator:    calculator,
        vaultService:  vaultService,
        cache:         cache,
        migrationMode: true, // Enable during migration period
    }
}

func (s *eventService) RecordProgress(ctx context.Context, userID string, body *progression.ProgressionBody) error {
    // Convert legacy body to event
    event := s.createEventFromBody(userID, body)
    
    // Store event
    if err := s.repository.CreateEvent(ctx, event); err != nil {
        return fmt.Errorf("failed to create progress event: %w", err)
    }
    
    // Invalidate cached summary (lazy recalculation)
    if err := s.repository.InvalidateProgressSummary(ctx, userID); err != nil {
        log.Printf("Failed to invalidate progress summary for user %s: %v", userID, err)
    }
    
    // Process rewards if this is a completion event
    if event.Action == progression.ActionCompleted {
        if err := s.processRewards(ctx, userID, event); err != nil {
            log.Printf("Failed to process rewards for user %s: %v", userID, err)
            // Don't fail the request for reward processing errors
        }
    }
    
    return nil
}

func (s *eventService) GetUserProgress(ctx context.Context, userID string) (*progression.ProgressSummary, error) {
    // Try cache first
    cacheKey := fmt.Sprintf("progression:summary:%s", userID)
    if cached, found := s.cache.Get(ctx, cacheKey); found {
        if summary, ok := cached.(*progression.ProgressSummary); ok {
            return summary, nil
        }
    }
    
    // Try database cache
    summary, err := s.repository.GetProgressSummary(ctx, userID)
    if err == nil && summary != nil {
        // Cache in memory for faster access
        s.cache.Set(ctx, cacheKey, summary, time.Hour)
        return summary, nil
    }
    
    // Calculate from events
    events, err := s.repository.GetUserEvents(ctx, userID, 0)
    if err != nil {
        return nil, fmt.Errorf("failed to get user events: %w", err)
    }
    
    summary, err = s.calculator.CalculateProgressFromEvents(ctx, events, userID)
    if err != nil {
        return nil, fmt.Errorf("failed to calculate progress: %w", err)
    }
    
    // Cache the result
    if err := s.repository.SaveProgressSummary(ctx, summary); err != nil {
        log.Printf("Failed to save progress summary for user %s: %v", userID, err)
    }
    
    s.cache.Set(ctx, cacheKey, summary, time.Hour)
    
    return summary, nil
}

func (s *eventService) GetTrailProgress(ctx context.Context, userID, trailID string) (*progression.TrailSummary, error) {
    summary, err := s.GetUserProgress(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    trailSummary, exists := summary.Trails[trailID]
    if !exists {
        return progression.NewTrailSummary(trailID), nil
    }
    
    return trailSummary, nil
}

func (s *eventService) createEventFromBody(userID string, body *progression.ProgressionBody) *progression.ProgressEvent {
    event := &progression.ProgressEvent{
        UserID:    userID,
        TrailID:   body.Trail,
        ItemID:    body.Module,
        ItemType:  progression.ProgressType(body.Type),
        ContentID: body.Content,
        Data: map[string]interface{}{
            "originalTimestamp": body.Timestamp,
            "legacyRequest":     true,
        },
    }
    
    // Determine action based on choice
    if body.Choice != nil {
        event.Choice = &progression.Choice{
            Identifier: body.Choice.Identifier,
            Next:       body.Choice.Next,
        }
        
        // If choice indicates a reward, this is a completion
        if s.isRewardChoice(body.Choice.Next) {
            event.Action = progression.ActionCompleted
        } else {
            event.Action = progression.ActionStarted
        }
    } else {
        event.Action = progression.ActionStarted
    }
    
    return event
}

func (s *eventService) processRewards(ctx context.Context, userID string, event *progression.ProgressEvent) error {
    if event.Choice == nil {
        return nil
    }
    
    var rewardAmount int
    switch event.Choice.Next {
    case string(progression.RewardTypeCoin):
        switch event.ItemType {
        case progression.ProgressTypeLesson:
            rewardAmount = 10 // Lesson completion reward
        case progression.ProgressTypeChallenge:
            rewardAmount = 50 // Challenge completion reward
        }
    case string(progression.RewardTypeDiamond):
        rewardAmount = 1 // Diamond reward
    default:
        return nil // No reward
    }
    
    if rewardAmount > 0 {
        return s.vaultService.AddCoins(ctx, userID, rewardAmount)
    }
    
    return nil
}

func (s *eventService) isRewardChoice(next string) bool {
    return next == string(progression.RewardTypeCoin) || next == string(progression.RewardTypeDiamond)
}

// Legacy compatibility methods
func (s *eventService) CreateLesson(ctx context.Context, userID string, body *progression.ProgressionBody) error {
    body.Type = string(progression.ProgressTypeLesson)
    return s.RecordProgress(ctx, userID, body)
}

func (s *eventService) CreateChallenge(ctx context.Context, userID string, body *progression.ProgressionBody) error {
    body.Type = string(progression.ProgressTypeChallenge)
    return s.RecordProgress(ctx, userID, body)
}

func (s *eventService) LegacyCreateLessonChallenge(ctx context.Context, userID string, body *progression.ProgressionBody) error {
    return s.RecordProgress(ctx, userID, body)
}
```

### Phase 3: Migration Implementation (Week 3)

#### Step 3.1: Create Migration Service

**Create `internal/service/progression/migration.go`**:
```go
package progression

import (
    "context"
    "fmt"
    "time"
    
    "github.com/dsoplabs/dinbora-backend/internal/model/progression"
    "github.com/dsoplabs/dinbora-backend/internal/repository/progression"
)

type MigrationService struct {
    repository Repository
    calculator *Calculator
}

func NewMigrationService(repo progressionRepo.Repository, calculator *Calculator) *MigrationService {
    return &MigrationService{
        repository: repo,
        calculator: calculator,
    }
}

func (ms *MigrationService) MigrateUserData(ctx context.Context, userID string) error {
    // Check if already migrated
    migrated, err := ms.repository.IsUserMigrated(ctx, userID)
    if err != nil {
        return fmt.Errorf("failed to check migration status: %w", err)
    }
    if migrated {
        return nil // Already migrated
    }
    
    // Get legacy progression data
    legacyProgression, err := ms.repository.GetLegacyProgression(ctx, userID)
    if err != nil {
        // If no legacy data found, just mark as migrated
        if isNotFoundError(err) {
            return ms.repository.MarkUserMigrated(ctx, userID)
        }
        return fmt.Errorf("failed to get legacy progression: %w", err)
    }
    
    // Convert legacy data to events
    events := ms.convertLegacyToEvents(legacyProgression)
    
    if len(events) > 0 {
        // Store events in batches
        batchSize := 100
        for i := 0; i < len(events); i += batchSize {
            end := i + batchSize
            if end > len(events) {
                end = len(events)
            }
            
            batch := events[i:end]
            if err := ms.repository.CreateEventsBatch(ctx, batch); err != nil {
                return fmt.Errorf("failed to create migration events batch: %w", err)
            }
        }
    }
    
    // Mark user as migrated
    if err := ms.repository.MarkUserMigrated(ctx, userID); err != nil {
        return fmt.Errorf("failed to mark user as migrated: %w", err)
    }
    
    return nil
}

func (ms *MigrationService) convertLegacyToEvents(prog *progression.Progression) []*progression.ProgressEvent {
    var events []*progression.ProgressEvent
    baseTime := prog.CreatedAt
    eventCounter := 0
    
    for _, trail := range prog.Trails {
        // Create events for lessons
        for _, lesson := range trail.Lessons {
            for i, content := range lesson.Path {
                eventTime := baseTime.Add(time.Duration(eventCounter) * time.Minute)
                
                // Started event
                startEvent := &progression.ProgressEvent{
                    UserID:    prog.User,
                    TrailID:   trail.ID,
                    ItemID:    lesson.Identifier,
                    ItemType:  progression.ProgressTypeLesson,
                    Action:    progression.ActionStarted,
                    ContentID: content.Identifier,
                    Choice: &progression.Choice{
                        Identifier: content.Choice.Identifier,
                        Next:       content.Choice.Next,
                    },
                    Data: map[string]interface{}{
                        "migrated":           true,
                        "originalTimestamp":  content.Timestamp,
                        "legacyProgressionId": prog.ObjectID.Hex(),
                    },
                    Timestamp: eventTime,
                    Migrated:  true,
                    LegacyID:  prog.ObjectID.Hex(),
                }
                events = append(events, startEvent)
                eventCounter++
                
                // If this is the last content and lesson is completed, add completion event
                if i == len(lesson.Path)-1 && lesson.Completed {
                    completionEvent := &progression.ProgressEvent{
                        UserID:    prog.User,
                        TrailID:   trail.ID,
                        ItemID:    lesson.Identifier,
                        ItemType:  progression.ProgressTypeLesson,
                        Action:    progression.ActionCompleted,
                        ContentID: content.Identifier,
                        Choice: &progression.Choice{
                            Identifier: content.Choice.Identifier,
                            Next:       content.Choice.Next,
                        },
                        Data: map[string]interface{}{
                            "migrated":           true,
                            "rewarded":           lesson.Rewarded,
                            "legacyProgressionId": prog.ObjectID.Hex(),
                        },
                        Timestamp: eventTime.Add(30 * time.Second),
                        Migrated:  true,
                        LegacyID:  prog.ObjectID.Hex(),
                    }
                    events = append(events, completionEvent)
                    eventCounter++
                }
            }
        }
        
        // Create events for challenges
        if trail.Challenge != nil {
            for _, phase := range trail.Challenge.Phases {
                for i, content := range phase.Path {
                    eventTime := baseTime.Add(time.Duration(eventCounter) * time.Minute)
                    
                    // Started event
                    startEvent := &progression.ProgressEvent{
                        UserID:    prog.User,
                        TrailID:   trail.ID,
                        ItemID:    trail.Challenge.Identifier,
                        ItemType:  progression.ProgressTypeChallenge,
                        Action:    progression.ActionStarted,
                        ContentID: content.Identifier,
                        Choice: &progression.Choice{
                            Identifier: content.Choice.Identifier,
                            Next:       content.Choice.Next,
                        },
                        Data: map[string]interface{}{
                            "migrated":           true,
                            "phaseId":            phase.Identifier,
                            "originalTimestamp":  content.Timestamp,
                            "legacyProgressionId": prog.ObjectID.Hex(),
                        },
                        Timestamp: eventTime,
                        Migrated:  true,
                        LegacyID:  prog.ObjectID.Hex(),
                    }
                    events = append(events, startEvent)
                    eventCounter++
                    
                    // If phase is completed and this is the last content, add completion
                    if i == len(phase.Path)-1 && phase.Completed {
                        completionEvent := &progression.ProgressEvent{
                            UserID:    prog.User,
                            TrailID:   trail.ID,
                            ItemID:    trail.Challenge.Identifier,
                            ItemType:  progression.ProgressTypeChallenge,
                            Action:    progression.ActionCompleted,
                            ContentID: content.Identifier,
                            Choice: &progression.Choice{
                                Identifier: content.Choice.Identifier,
                                Next:       content.Choice.Next,
                            },
                            Data: map[string]interface{}{
                                "migrated":           true,
                                "phaseId":            phase.Identifier,
                                "rewarded":           trail.Challenge.Rewarded,
                                "legacyProgressionId": prog.ObjectID.Hex(),
                            },
                            Timestamp: eventTime.Add(30 * time.Second),
                            Migrated:  true,
                            LegacyID:  prog.ObjectID.Hex(),
                        }
                        events = append(events, completionEvent)
                        eventCounter++
                    }
                }
            }
        }
    }
    
    return events
}

func (ms *MigrationService) ValidateMigration(ctx context.Context, userID string) error {
    // Get legacy data
    legacyProgression, err := ms.repository.GetLegacyProgression(ctx, userID)
    if err != nil {
        return fmt.Errorf("failed to get legacy progression: %w", err)
    }
    
    // Get migrated events
    events, err := ms.repository.GetUserEvents(ctx, userID, 0)
    if err != nil {
        return fmt.Errorf("failed to get user events: %w", err)
    }
    
    // Calculate new summary
    newSummary, err := ms.calculator.CalculateProgressFromEvents(ctx, events, userID)
    if err != nil {
        return fmt.Errorf("failed to calculate progress from events: %w", err)
    }
    
    // Validate critical data points
    for _, legacyTrail := range legacyProgression.Trails {
        newTrail, exists := newSummary.Trails[legacyTrail.ID]
        if !exists {
            return fmt.Errorf("trail %s missing in migrated data", legacyTrail.ID)
        }
        
        // Validate progress percentage
        if int(legacyTrail.Total) != newTrail.ProgressPercent {
            return fmt.Errorf("progress mismatch for trail %s: legacy=%d, new=%d", 
                legacyTrail.ID, legacyTrail.Total, newTrail.ProgressPercent)
        }
        
        // Validate lesson completion
        for _, legacyLesson := range legacyTrail.Lessons {
            newLesson, exists := newTrail.LessonProgress[legacyLesson.Identifier]
            if !exists && legacyLesson.Completed {
                return fmt.Errorf("completed lesson %s missing in migrated data", legacyLesson.Identifier)
            }
            
            if exists && legacyLesson.Completed != newLesson.Completed {
                return fmt.Errorf("lesson completion mismatch for %s", legacyLesson.Identifier)
            }
            
            if exists && legacyLesson.Rewarded != newLesson.Rewarded {
                return fmt.Errorf("lesson reward mismatch for %s", legacyLesson.Identifier)
            }
        }
    }
    
    return nil
}

func isNotFoundError(err error) bool {
    // Check if error indicates not found
    // Implementation depends on your error handling pattern
    return err != nil && err.Error() == "not found"
}
```

### Phase 4: Controller Integration (Week 4)

#### Step 4.1: Update Service Interface

**Update `internal/service/progression/service.go`**:
```go
package progression

import (
    "context"
    "github.com/dsoplabs/dinbora-backend/internal/model/progression"
)

type Service interface {
    // New event-based methods
    RecordProgress(ctx context.Context, userID string, body *progression.ProgressionBody) error
    GetUserProgress(ctx context.Context, userID string) (*progression.ProgressSummary, error)
    GetTrailProgress(ctx context.Context, userID, trailID string) (*progression.TrailSummary, error)
    
    // Legacy compatibility methods
    CreateLesson(ctx context.Context, userID string, body *progression.ProgressionBody) error
    CreateChallenge(ctx context.Context, userID string, body *progression.ProgressionBody) error
    LegacyCreateLessonChallenge(ctx context.Context, userID string, body *progression.ProgressionBody) error
    
    // Query methods (with legacy format conversion)
    FindByUser(ctx context.Context, userID string) (*progression.Progression, error)
    FindTrail(ctx context.Context, userID, trailID string) (*progression.Trail, error)
    FindLesson(ctx context.Context, userID, trailID, lessonIdentifier string) (*progression.Lesson, error)
    FindChallenge(ctx context.Context, userID, trailID, challengeIdentifier string) (*progression.Challenge, error)
    FindChallengePhase(ctx context.Context, userID, trailID, challengeIdentifier, phaseIdentifier string) (*progression.ChallengePhase, error)
    
    // Card methods
    FindTrailCards(ctx context.Context, userID string, trailID string) ([]*progression.TrailCard, error)
    FindModuleCards(ctx context.Context, userID, trailID, lessonID string) ([]*progression.LessonCard, *progression.ChallengeCard, error)
    FindChallengePhaseCards(ctx context.Context, userID, trailID, phaseID string) ([]*progression.ChallengePhaseCard, error)
    
    // Achievement methods
    FindAllAchievementsContent(ctx context.Context, userID string) ([]*progression.Achievement, error)
}
```

#### Step 4.2: Create Legacy Compatibility Layer

**Create `internal/service/progression/legacy_adapter.go`**:
```go
package progression

import (
    "context"
    "fmt"
    
    "github.com/dsoplabs/dinbora-backend/internal/model/progression"
)

// Legacy compatibility methods that convert new event-based data to old format

func (s *eventService) FindByUser(ctx context.Context, userID string) (*progression.Progression, error) {
    // Check if user is migrated
    migrated, err := s.repository.IsUserMigrated(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    if !migrated && s.migrationMode {
        // Try to migrate on-the-fly
        migrationService := NewMigrationService(s.repository, s.calculator)
        if err := migrationService.MigrateUserData(ctx, userID); err != nil {
            // Fallback to legacy data if migration fails
            return s.repository.GetLegacyProgression(ctx, userID)
        }
    }
    
    // Get data from new system and convert to legacy format
    summary, err := s.GetUserProgress(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    return s.convertSummaryToLegacy(summary), nil
}

func (s *eventService) FindTrail(ctx context.Context, userID, trailID string) (*progression.Trail, error) {
    trailSummary, err := s.GetTrailProgress(ctx, userID, trailID)
    if err != nil {
        return nil, err
    }
    
    return s.convertTrailSummaryToLegacy(trailSummary), nil
}

func (s *eventService) FindLesson(ctx context.Context, userID, trailID, lessonIdentifier string) (*progression.Lesson, error) {
    trailSummary, err := s.GetTrailProgress(ctx, userID, trailID)
    if err != nil {
        return nil, err
    }
    
    lessonProgress, exists := trailSummary.LessonProgress[lessonIdentifier]
    if !exists {
        return &progression.Lesson{
            Identifier: lessonIdentifier,
            Available:  true,
            Path:       make([]*progression.LessonContent, 0),
        }, nil
    }
    
    return s.convertLessonProgressToLegacy(lessonProgress), nil
}

func (s *eventService) convertSummaryToLegacy(summary *progression.ProgressSummary) *progression.Progression {
    trails := make([]*progression.Trail, 0, len(summary.Trails))
    
    for _, trailSummary := range summary.Trails {
        legacyTrail := s.convertTrailSummaryToLegacy(trailSummary)
        trails = append(trails, legacyTrail)
    }
    
    return &progression.Progression{
        User:      summary.UserID,
        Trails:    trails,
        Achievements:  make([]*progression.Achievement, 0), // Load separately if needed
        CreatedAt: summary.LastUpdated,
        UpdatedAt: summary.LastUpdated,
    }
}

func (s *eventService) convertTrailSummaryToLegacy(summary *progression.TrailSummary) *progression.Trail {
    lessons := make([]*progression.Lesson, 0, len(summary.LessonProgress))
    
    for lessonID, progress := range summary.LessonProgress {
        legacyLesson := s.convertLessonProgressToLegacy(progress)
        legacyLesson.Identifier = lessonID
        lessons = append(lessons, legacyLesson)
    }
    
    var challenge *progression.Challenge
    for challengeID, progress := range summary.ChallengeProgress {
        challenge = s.convertChallengeProgressToLegacy(progress)
        challenge.Identifier = challengeID
        break // Only one challenge per trail
    }
    
    return &progression.Trail{
        ID:                 summary.ID,
        Total:              uint8(summary.ProgressPercent),
        Available:          true, // Calculate based on requirements
        LessonsCompleted:   len(summary.LessonsCompleted) > 0,
        ChallengeCompleted: summary.IsCompleted,
        Lessons:            lessons,
        Challenge:          challenge,
    }
}

func (s *eventService) convertLessonProgressToLegacy(progress *progression.LessonProgress) *progression.Lesson {
    path := make([]*progression.LessonContent, len(progress.Path))
    
    for i, contentID := range progress.Path {
        path[i] = &progression.LessonContent{
            Identifier: contentID,
            Choice: &progression.ModuleContentChoice{
                Identifier: fmt.Sprintf("choice_%d", i),
                Next:       "COIN", // Default choice
            },
            Timestamp: time.Now().Add(-time.Duration(len(progress.Path)-i) * time.Hour),
        }
    }
    
    return &progression.Lesson{
        Identifier: "", // Set by caller
        Current:    progress.Current,
        Completed:  progress.Completed,
        Available:  true,
        Rewarded:   progress.Rewarded,
        Path:       path,
    }
}

func (s *eventService) convertChallengeProgressToLegacy(progress *progression.ChallengeProgress) *progression.Challenge {
    phases := make([]*progression.ChallengePhase, 0, len(progress.Phases))
    
    for phaseID, phase := range progress.Phases {
        legacyPhase := &progression.ChallengePhase{
            Identifier: phaseID,
            Current:    phase.Current,
            Completed:  phase.Completed,
            Available:  true,
            Path:       s.convertPhasePathToLegacy(phase.Path),
        }
        phases = append(phases, legacyPhase)
    }
    
    return &progression.Challenge{
        Identifier: "", // Set by caller
        Current:    progress.Current,
        Completed:  progress.Completed,
        Available:  true,
        Rewarded:   progress.Rewarded,
        Phases:     phases,
    }
}

func (s *eventService) convertPhasePathToLegacy(path []string) []*progression.ChallengePhaseContent {
    legacyPath := make([]*progression.ChallengePhaseContent, len(path))
    
    for i, contentID := range path {
        legacyPath[i] = &progression.ChallengePhaseContent{
            Identifier: contentID,
            Choice: &progression.ModuleContentChoice{
                Identifier: fmt.Sprintf("choice_%d", i),
                Next:       "COIN",
            },
            Timestamp: time.Now().Add(-time.Duration(len(path)-i) * time.Hour),
        }
    }
    
    return legacyPath
}
```

### Phase 5: Testing and Deployment (Week 5)

#### Step 5.1: Update Controller Implementation

**Update `internal/controller/progression/controller.go`**:
```go
// Update the controller to use the new service implementation
// The controller interface remains the same, but the service methods
// now use the event-driven system internally

func (tc *controller) LegacyCreate() echo.HandlerFunc {
    return func(c echo.Context) error {
        ctx := c.Request().Context()
        var body progression.ProgressionBody
        
        if err := c.Bind(&body); err != nil {
            return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
        }
        
        userToken, err := token.GetClaimsFromRequest(c.Request())
        if err != nil {
            return err
        }
        
        // New implementation handles this transparently
        err = tc.Service.LegacyCreateLessonChallenge(ctx, userToken.Uid, &body)
        if err != nil {
            return err
        }
        
        return c.JSON(http.StatusNoContent, nil)
    }
}

// All other controller methods remain unchanged
// They will automatically use the new event-driven system
```

#### Step 5.2: Database Migration Script

**Create `scripts/progression_migration.go`**:
```go
package main

import (
    "context"
    "log"
    "time"
    
    "go.mongodb.org/mongo-driver/mongo"
    "go.mongodb.org/mongo-driver/mongo/options"
    
    progressionRepo "github.com/dsoplabs/dinbora-backend/internal/repository/progression"
    progressionService "github.com/dsoplabs/dinbora-backend/internal/service/progression"
)

func main() {
    ctx := context.Background()
    
    // Connect to database
    client, err := mongo.Connect(ctx, options.Client().ApplyURI("mongodb://localhost:27017"))
    if err != nil {
        log.Fatal("Failed to connect to MongoDB:", err)
    }
    defer client.Disconnect(ctx)
    
    // Setup repositories and services
    eventStore := progressionRepo.NewEventStore(client, "dinbora")
    calculator := progressionService.NewCalculator(nil) // Pass real trail service
    migration := progressionService.NewMigrationService(eventStore, calculator)
    
    // Create indexes
    if err := eventStore.CreateIndexes(ctx); err != nil {
        log.Printf("Failed to create indexes: %v", err)
    }
    
    // Get all users to migrate
    users, err := getAllUsers(ctx, client)
    if err != nil {
        log.Fatal("Failed to get users:", err)
    }
    
    log.Printf("Starting migration for %d users", len(users))
    
    // Migrate users in batches
    batchSize := 100
    for i := 0; i < len(users); i += batchSize {
        end := i + batchSize
        if end > len(users) {
            end = len(users)
        }
        
        batch := users[i:end]
        migrateBatch(ctx, migration, batch)
        
        log.Printf("Migrated batch %d/%d", (i/batchSize)+1, (len(users)+batchSize-1)/batchSize)
    }
    
    log.Println("Migration completed successfully")
}

func migrateBatch(ctx context.Context, migration *progressionService.MigrationService, users []string) {
    for _, userID := range users {
        if err := migration.MigrateUserData(ctx, userID); err != nil {
            log.Printf("Failed to migrate user %s: %v", userID, err)
            continue
        }
        
        // Validate migration
        if err := migration.ValidateMigration(ctx, userID); err != nil {
            log.Printf("Migration validation failed for user %s: %v", userID, err)
        }
        
        // Small delay to avoid overwhelming the database
        time.Sleep(10 * time.Millisecond)
    }
}

func getAllUsers(ctx context.Context, client *mongo.Client) ([]string, error) {
    // Implementation to get all user IDs with progression data
    // This would query the existing progressions collection
    return []string{}, nil
}
```

## Deployment Strategy

### Phase 1: Infrastructure Setup
1. Deploy new collections and indexes
2. Update application with new models and services
3. Keep legacy system running in parallel

### Phase 2: Background Migration
1. Run migration script for all existing users
2. Validate migration results
3. Monitor for any data inconsistencies

### Phase 3: Feature Flag Rollout
1. Enable new system for 10% of users
2. Monitor performance and error rates
3. Gradually increase to 50%, then 100%

### Phase 4: Legacy Cleanup
1. Remove legacy service implementations
2. Clean up old database collections
3. Remove migration compatibility code

## Monitoring and Validation

### Key Metrics to Monitor
1. **Migration Success Rate**: % of users successfully migrated
2. **API Response Times**: Before/after performance comparison
3. **Error Rates**: Monitor for increased errors during transition
4. **Data Consistency**: Regular validation of migrated data

### Rollback Plan
1. Feature flag to instantly switch back to legacy system
2. Keep legacy data intact during migration period
3. Automated consistency checks
4. Manual verification for critical users

This implementation guide provides a complete roadmap for transforming the progression system while maintaining full backward compatibility and zero downtime.
