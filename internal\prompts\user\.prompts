@/internal/model/user.go @/internal/service/user/service.go @/internal/controller/user/controller.go @/internal/controller/user/admin.go 

Title:
Refactor User Controller to Separate Admin and User Endpoints

Description:

Separation of Concerns:
Split the user controller endpoints to clearly differentiate between admin and regular user operations. Admin endpoints (e.g., GET /:id, GET ``, PUT /:id) now explicitly require admin privileges using middlewares.AdminGuard(), while endpoints like GET /me, PATCH /me, and DELETE /me remain dedicated to the logged-in user.

Route Grouping:
Continue using two distinct groups—legacyGroup for legacy routes and currentGroup for new endpoints. The new structure ensures that admin-specific logic is isolated from self-service user operations.

Future Maintainability:
This refactoring lays the groundwork for further modularization. In the future, consider placing admin endpoints in a separate file (e.g., admin.go) within the user controller package to avoid file bloat and improve clarity.