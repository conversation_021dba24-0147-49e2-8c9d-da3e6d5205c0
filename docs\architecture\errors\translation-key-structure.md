# Translation Key Structure

This document outlines the hierarchical dot notation structure used for translation keys in the Dinbora backend error translation system.

## Key Structure Pattern

**Format**: `package.error.specificError`

### Components

1. **Package**: The domain package or module (e.g., auth, financialSheet, financialDna, user, etc.)
2. **Category**: Always `error` for error messages
3. **Specific Error**: The specific error condition

## Key Format

All translation keys follow the standardized format:
- **Pattern**: `<package>.error.<specificError>`
- **Category**: Always uses `error` as the middle component
- **Organization**: Keys are organized by package in alphabetical order

**Examples:**
- `user.error.notFound`
- `user.error.notFoundById`
- `auth.error.invalidCredentials`
- `financialSheet.error.createFailed`

## Packages

The translation keys are organized by package in alphabetical order. Each package contains error-related translation keys following the `<package>.error.<specificError>` format.

### Core Packages

#### `auth`
Authentication and authorization operations.

**Error Keys:**
- `auth.error.invalidCredentials`
- `auth.error.passwordProcessFailed`
- `auth.error.passwordResetFailed`

#### `financialDna`
Financial DNA operations.

**Error Keys:**
- `financialDna.error.conflictExists`
- `financialDna.error.createFailed`
- `financialDna.error.findFailed`
- `financialDna.error.notFound`
- `financialDna.error.userNotFound`
- `financialDna.error.findByUserFailed`
- `financialDna.error.conflictUpdate`
- `financialDna.error.updateFailed`
- `financialDna.error.deleteFailed`
- `financialDna.error.findAllFailed`
- `financialDna.error.findByIdFailed`
- `financialDna.error.findByNameFailed`
- `financialDna.error.findByUserIdFailed`

#### `financialSheet`
Financial sheet operations.

**Error Keys:**
- `financialSheet.error.conflictExists`
- `financialSheet.error.createFailed`
- `financialSheet.error.findFailed`
- `financialSheet.error.findAllFailed`
- `financialSheet.error.notFound`
- `financialSheet.error.userNotFound`
- `financialSheet.error.findByUserFailed`
- `financialSheet.error.conflictUpdate`

#### `user`
User-related operations.

**Error Keys:**
- `user.error.notFound`
- `user.error.notFoundById`
- `user.error.notFoundByEmail`
- `user.error.notFoundByReferralCode`
- `user.error.deletedNotFoundByEmail`
- `user.error.notFoundForUpdate`
- `user.error.notFoundForDeletion`
- `user.error.conflictExists`
- `user.error.conflictUpdate`
- `user.error.deletedConflictExists`

### Additional Packages

As new packages are added to the system, their translation keys should follow the same pattern and be organized alphabetically. Examples of other packages that may have translation keys:

- `aiAssistant.error.*`
- `aiContext.error.*`
- `apple.error.*`
- `billing.error.*`
- `content.error.*`
- `dashboard.error.*`
- `dreamboard.error.*`
- `firebase.error.*`
- `gamification.error.*`
- `google.error.*`
- `i18n.error.*`
- `league.error.*`
- `leagueRanking.error.*`
- `notification.error.*`
- `progression.error.*`
- `rbac.error.*`
- `s3.error.*`
- `vault.error.*`

## Naming Conventions

### Constants
Translation key constants follow the pattern: `Key<Package>Error<SpecificError>`

**Examples:**
- `KeyUserErrorNotFound = "user.error.notFound"`
- `KeyUserErrorNotFoundById = "user.error.notFoundById"`
- `KeyAuthErrorInvalidCredentials = "auth.error.invalidCredentials"`
- `KeyFinancialSheetErrorCreateFailed = "financialSheet.error.createFailed"`

### Specific Error Names
- Use camelCase for the specific error part
- Be descriptive but concise
- Use common patterns:
  - `notFound` for missing resources
  - `notFoundById`, `notFoundByEmail` for specific lookup failures
  - `conflictExists` for duplicate resources
  - `createFailed`, `updateFailed`, `deleteFailed` for operation failures
  - `invalidCredentials` for authentication errors

## Usage Examples

### Creating Errors with Translation Keys

```go
// Not found error
return errors.NewNotFoundError(
    errors.Service,
    errors.UserNotFound,
    errors.KeyUserErrorNotFound,
    nil,
)

// Not found by ID error
return errors.NewNotFoundError(
    errors.Service,
    errors.UserNotFound,
    errors.KeyUserErrorNotFoundById,
    nil,
)

// Authentication error
return errors.NewUnauthorizedError(
    errors.Controller,
    errors.AuthInvalidCredentials,
    errors.KeyAuthErrorInvalidCredentials,
    nil,
)
```

### Adding New Translation Keys

1. **Choose appropriate package** (organized alphabetically)
2. **Add to all translation files** (`locales/pt.json`, `locales/en.json`, `locales/es.json`)
3. **Add constant** to `internal/errors/translation_keys.go` following the pattern `Key<Package>Error<SpecificError>`
4. **Use in error creation** with the appropriate error helper function

This structure provides clear organization, easy maintenance, and consistent naming across the entire application.
