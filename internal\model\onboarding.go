package model

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

type Onboarding struct {
	AgeRange           AgeRange            `json:"ageRange" bson:"ageRange"`
	FinancialSituation FinancialSituation  `json:"financialSituation" bson:"financialSituation"`
	FinancialGoal      FinancialGoal       `json:"financialGoal" bson:"financialGoal"`
	PersonalInterests  []PersonalInterests `json:"personalInterests" bson:"personalInterests"`
	CompletedAt        time.Time           `json:"completedAt" bson:"completedAt"`
}

// AgeRange
type AgeRange struct {
	ID     byte              `json:"id" bson:"id"`
	Labels map[string]string `json:"labels" bson:"labels"`
}

const (
	UndefinedAgeRange byte = iota // Indefinido
	AgeRange0To11                 // Menos de 12 anos
	AgeRange12To17                // 12 a 17 anos
	AgeRange18To29                // 18 a 29 anos
	AgeRange30To59                // 30 a 59 anos
	AgeRange60Plus                // 60 anos ou mais
)

func (a *AgeRange) AddLabel() {
	a.Labels = make(map[string]string)
	switch a.ID {
	case AgeRange0To11:
		a.Labels["en"] = "Under 12 years"
		a.Labels["pt"] = "Menos de 12 anos"
		a.Labels["es"] = "Menos de 12 años"
	case AgeRange12To17:
		a.Labels["en"] = "12 to 17 years"
		a.Labels["pt"] = "12 a 17 anos"
		a.Labels["es"] = "12 a 17 años"
	case AgeRange18To29:
		a.Labels["en"] = "18 to 29 years"
		a.Labels["pt"] = "18 a 29 anos"
		a.Labels["es"] = "18 a 29 años"
	case AgeRange30To59:
		a.Labels["en"] = "30 to 59 years"
		a.Labels["pt"] = "30 a 59 anos"
		a.Labels["es"] = "30 a 59 años"
	case AgeRange60Plus:
		a.Labels["en"] = "60 years or more"
		a.Labels["pt"] = "60 anos ou mais"
		a.Labels["es"] = "60 años o más"
	default:
		a.Labels["en"] = "Undefined age range"
		a.Labels["pt"] = "Faixa etária indefinida"
		a.Labels["es"] = "Rango de edad indefinido"
	}
}

func (a AgeRange) String() string {
	return a.LabelFor("pt")
}

func (a AgeRange) LabelFor(lang string) string {
	if label, ok := a.Labels[lang]; ok {
		return label
	}
	return a.Labels["pt"]
}

func (a AgeRange) IsValid() error {
	switch a.ID {
	case AgeRange0To11, AgeRange12To17, AgeRange18To29, AgeRange30To59, AgeRange60Plus:
		return nil
	}
	return errors.New(errors.Model, "invalid onboarding age range type", errors.Validation, nil)
}

// FinancialSituation
type FinancialSituation struct {
	ID     byte              `json:"id" bson:"id"`
	Labels map[string]string `json:"labels" bson:"labels"`
}

const (
	UndefinedFinancialSituation byte = iota
	CantPayBills                     // Não consigo pagar as contas
	InDebtButManaging                // Tenho dívidas, mas consigo lidar com elas
	NoDebtButCantSave                // Não tenho dívidas, mas economizar é difícil
	SavesMonthly                     // Consigo guardar dinheiro todo mês
	InvestsAndWantsMore              // Já invisto e quero ir além
)

func (s *FinancialSituation) AddLabel() {
	s.Labels = make(map[string]string)
	switch s.ID {
	case CantPayBills:
		s.Labels["en"] = "Can't pay bills"
		s.Labels["pt"] = "Não consigo pagar as contas"
		s.Labels["es"] = "No puedo pagar las cuentas"
	case InDebtButManaging:
		s.Labels["en"] = "In debt but managing"
		s.Labels["pt"] = "Tenho dívidas, mas consigo lidar"
		s.Labels["es"] = "Tengo deudas pero las manejo"
	case NoDebtButCantSave:
		s.Labels["en"] = "No debt but can't save"
		s.Labels["pt"] = "Sem dívidas, mas não consigo economizar"
		s.Labels["es"] = "Sin deudas pero no puedo ahorrar"
	case SavesMonthly:
		s.Labels["en"] = "Saves monthly"
		s.Labels["pt"] = "Consigo guardar dinheiro todo mês"
		s.Labels["es"] = "Ahorro dinero cada mes"
	case InvestsAndWantsMore:
		s.Labels["en"] = "Invests and wants more"
		s.Labels["pt"] = "Já invisto e quero ir além"
		s.Labels["es"] = "Ya invierto y quiero más"
	default:
		s.Labels["en"] = "Undefined financial situation"
		s.Labels["pt"] = "Situação financeira indefinida"
		s.Labels["es"] = "Situación financiera indefinida"
	}
}

// String returns the Portuguese label as default representation
func (fs FinancialSituation) String() string {
	return fs.LabelFor("pt")
}

// LabelFor returns the label for requested language or Portuguese if not found
func (s FinancialSituation) LabelFor(lang string) string {
	if label, ok := s.Labels[lang]; ok {
		return label
	}
	return s.Labels["pt"]
}

func (fs FinancialSituation) IsValid() error {
	switch fs.ID {
	case CantPayBills, InDebtButManaging, NoDebtButCantSave, SavesMonthly, InvestsAndWantsMore:
		return nil
	}
	return errors.New(errors.Model, "invalid onboarding financial situation type", errors.Validation, nil)
}

// FinancialGoal
type FinancialGoal struct {
	ID     byte              `json:"id" bson:"id"`
	Labels map[string]string `json:"labels" bson:"labels"`
}

const (
	UndefinedFinancialGoal byte = iota
	DebtControl                 // Controlar as dívidas e organizar tudo
	MoneySavings                // Economizar e guardar dinheiro
	StrategicFund               // Criar uma reserva financeira
	FutureInvestments           // Investir para ter um bom futuro
	FinancialLiteracy           // Aumentar o conhecimento em finanças
)

func (g *FinancialGoal) AddLabel() {
	g.Labels = make(map[string]string)
	switch g.ID {
	case DebtControl:
		g.Labels["en"] = "Control debts and get organized"
		g.Labels["pt"] = "Controlar as dívidas e organizar tudo"
		g.Labels["es"] = "Controlar deudas y organizarse"
	case MoneySavings:
		g.Labels["en"] = "Save and set aside money"
		g.Labels["pt"] = "Economizar e guardar dinheiro"
		g.Labels["es"] = "Ahorrar y guardar dinero"
	case StrategicFund:
		g.Labels["en"] = "Build an emergency fund"
		g.Labels["pt"] = "Criar uma reserva financeira"
		g.Labels["es"] = "Crear un fondo de emergencia"
	case FutureInvestments:
		g.Labels["en"] = "Invest for a better future"
		g.Labels["pt"] = "Investir para ter um bom futuro"
		g.Labels["es"] = "Invertir para un buen futuro"
	case FinancialLiteracy:
		g.Labels["en"] = "Improve financial knowledge"
		g.Labels["pt"] = "Aumentar o conhecimento em finanças"
		g.Labels["es"] = "Mejorar conocimiento financiero"
	default:
		g.Labels["en"] = "Undefined financial goal"
		g.Labels["pt"] = "Objetivo financeiro indefinido"
		g.Labels["es"] = "Objetivo financiero indefinido"
	}
}

func (g FinancialGoal) String() string {
	return g.LabelFor("pt")
}

func (g FinancialGoal) LabelFor(lang string) string {
	if label, ok := g.Labels[lang]; ok {
		return label
	}
	return g.Labels["pt"]
}

func (g FinancialGoal) IsValid() error {
	switch g.ID {
	case DebtControl, MoneySavings, StrategicFund, FutureInvestments, FinancialLiteracy:
		return nil
	}
	return errors.New(errors.Model, "invalid onboarding financial goal type", errors.Validation, nil)
}

// PersonalInterests
type PersonalInterests struct {
	ID     byte              `json:"id" bson:"id"`
	Labels map[string]string `json:"labels" bson:"labels"`
}

const (
	UndefinedPersonalInterests byte = iota
	Fashion                         // Moda
	Family                          // Família
	Pets                            // Pets
	Travel                          // Viagens
	Nature                          // Natureza
	Music                           // Música
	SelfCare                        // Autocuidado
	Books                           // Livros
	Shopping                        // Compras
	Movies                          // Filmes
	Games                           // Jogos
	Fitness                         // Fitness
	Technology                      // Tecnologia
	Languages                       // Idiomas
	Astrology                       // Astrologia
	Food                            // Comida
	Work                            // Trabalho
	Culture                         // Cultura
	Sports                          // Esportes
	SpaceExploration                // Exploração espacial
	Crypto                          // Criptoativos
	Health                          // Saúde
)

func (pi *PersonalInterests) AddLabel() {
	pi.Labels = make(map[string]string)
	switch pi.ID {
	case Fashion:
		pi.Labels["en"] = "Fashion"
		pi.Labels["pt"] = "Moda"
		pi.Labels["es"] = "Moda"
	case Family:
		pi.Labels["en"] = "Family"
		pi.Labels["pt"] = "Família"
		pi.Labels["es"] = "Familia"
	case Pets:
		pi.Labels["en"] = "Pets"
		pi.Labels["pt"] = "Pets"
		pi.Labels["es"] = "Mascotas"
	case Travel:
		pi.Labels["en"] = "Travel"
		pi.Labels["pt"] = "Viagens"
		pi.Labels["es"] = "Viajes"
	case Nature:
		pi.Labels["en"] = "Nature"
		pi.Labels["pt"] = "Natureza"
		pi.Labels["es"] = "Naturaleza"
	case Music:
		pi.Labels["en"] = "Music"
		pi.Labels["pt"] = "Música"
		pi.Labels["es"] = "Música"
	case SelfCare:
		pi.Labels["en"] = "Self care"
		pi.Labels["pt"] = "Autocuidado"
		pi.Labels["es"] = "Autocuidado"
	case Books:
		pi.Labels["en"] = "Books"
		pi.Labels["pt"] = "Livros"
		pi.Labels["es"] = "Libros"
	case Shopping:
		pi.Labels["en"] = "Shopping"
		pi.Labels["pt"] = "Compras"
		pi.Labels["es"] = "Compras"
	case Movies:
		pi.Labels["en"] = "Movies"
		pi.Labels["pt"] = "Filmes"
		pi.Labels["es"] = "Películas"
	case Games:
		pi.Labels["en"] = "Games"
		pi.Labels["pt"] = "Jogos"
		pi.Labels["es"] = "Juegos"
	case Fitness:
		pi.Labels["en"] = "Fitness"
		pi.Labels["pt"] = "Fitness"
		pi.Labels["es"] = "Fitness"
	case Technology:
		pi.Labels["en"] = "Technology"
		pi.Labels["pt"] = "Tecnologia"
		pi.Labels["es"] = "Tecnología"
	case Languages:
		pi.Labels["en"] = "Languages"
		pi.Labels["pt"] = "Idiomas"
		pi.Labels["es"] = "Idiomas"
	case Astrology:
		pi.Labels["en"] = "Astrology"
		pi.Labels["pt"] = "Astrologia"
		pi.Labels["es"] = "Astrología"
	case Food:
		pi.Labels["en"] = "Food"
		pi.Labels["pt"] = "Comida"
		pi.Labels["es"] = "Comida"
	case Work:
		pi.Labels["en"] = "Work"
		pi.Labels["pt"] = "Trabalho"
		pi.Labels["es"] = "Trabajo"
	case Culture:
		pi.Labels["en"] = "Culture"
		pi.Labels["pt"] = "Cultura"
		pi.Labels["es"] = "Cultura"
	case Sports:
		pi.Labels["en"] = "Sports"
		pi.Labels["pt"] = "Esportes"
		pi.Labels["es"] = "Deportes"
	case SpaceExploration:
		pi.Labels["en"] = "Space exploration"
		pi.Labels["pt"] = "Exploração espacial"
		pi.Labels["es"] = "Exploración espacial"
	case Crypto:
		pi.Labels["en"] = "Cryptocurrency"
		pi.Labels["pt"] = "Criptoativos"
		pi.Labels["es"] = "Criptomonedas"
	case Health:
		pi.Labels["en"] = "Health"
		pi.Labels["pt"] = "Saúde"
		pi.Labels["es"] = "Salud"
	default:
		pi.Labels["en"] = "Undefined interest"
		pi.Labels["pt"] = "Interesse indefinido"
		pi.Labels["es"] = "Interés indefinido"
	}
}

func (pi PersonalInterests) String() string {
	return pi.LabelFor("pt")
}

func (pi PersonalInterests) LabelFor(lang string) string {
	if label, ok := pi.Labels[lang]; ok {
		return label
	}
	return pi.Labels["pt"]
}

func (pi PersonalInterests) IsValid() error {
	switch pi.ID {
	case Fashion, Family, Pets, Travel, Nature, Music, SelfCare, Books,
		Shopping, Movies, Games, Fitness, Technology, Languages, Astrology,
		Food, Work, Culture, Sports, SpaceExploration, Crypto, Health:
		return nil
	}
	return errors.New(errors.Model, "invalid onboarding personal interests type", errors.Validation, nil)
}
