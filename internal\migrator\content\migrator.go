package content

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

const SEPARATOR = "/"

// Mode defines how the migrator should handle existing records
type Mode int

const (
	// CreateOnly will only create new records, never update existing ones
	CreateOnly Mode = iota
	// CreateOrUpdate will create new records and update existing ones
	CreateOrUpdate
)

// Migrator handles the migration of content files
type Migrator interface {
	Migrate(ctx context.Context) error
	MigrateContent(ctx context.Context, collection, path string) error
	SetMode(mode Mode)
	GetMode() Mode
}

type migrator struct {
	db       *mongo.Database
	basePath string
	mode     Mode
}

// NewMigrator creates a new instance of content migrator
func NewMigrator(db *mongo.Database) Migrator {
	return &migrator{
		db:       db,
		basePath: os.Getenv("MIGRATION_BASE_PATH"),
		mode:     CreateOrUpdate, // Default to create or update
	}
}

func (m *migrator) SetMode(mode Mode) {
	m.mode = mode
}

func (m *migrator) GetMode() Mode {
	return m.mode
}

func (m *migrator) Migrate(ctx context.Context) error {
	if m.basePath == "" {
		log.Printf("Ignoring migration due to invalid basePath")
		return nil
	}

	dir, err := os.ReadDir(m.basePath)
	if err != nil {
		return err
	}

	for _, dirEntry := range dir {
		if dirEntry.IsDir() {
			err = m.MigrateContent(ctx, dirEntry.Name(), fmt.Sprintf("%s%s%s", m.basePath, SEPARATOR, dirEntry.Name()))
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (m *migrator) MigrateContent(ctx context.Context, collection, path string) error {
	log.Printf("STARTED MIGRATION FOR COLLECTION %s", collection)

	entries, err := os.ReadDir(path)
	if err != nil {
		return err
	}

	// get only json files
	entries = filterEntriesByExtension(entries, "json")

	for index, entry := range entries {
		log.Printf("Migrating %d/%d file %s", index+1, len(entries), entry.Name())
		bytes, err := os.ReadFile(fmt.Sprintf("%s%s%s", path, SEPARATOR, entry.Name()))
		if err != nil {
			return err
		}

		var newValue interface{}
		err = json.Unmarshal(bytes, &newValue)
		if err != nil {
			line, character, _ := getLineAndCharacter(string(bytes), int(err.(*json.SyntaxError).Offset))
			return fmt.Errorf("error at line %d, character %d: %v", line, character, err.(*json.SyntaxError).Error())
		}

		id := getFilenameWithoutExtension(entry.Name())
		currentValue, err := m.getCurrentValue(ctx, id, collection)

		if err == nil && currentValue != nil {
			if m.mode == CreateOrUpdate {
				convertedValue, err := GetConverter(collection).Update(currentValue, &newValue, id)
				if err != nil {
					return err
				}

				if ok, err := m.update(ctx, id, collection, convertedValue); !ok {
					if err != nil {
						return err
					}
				} else {
					log.Printf("object %s UPDATED successfully", entry.Name())
				}
			} else {
				log.Printf("object %s SKIPPED (create-only mode)", entry.Name())
			}
		} else if err == nil {
			log.Println(collection)
			convertedValue, err := GetConverter(collection).Create(&newValue, id)
			if err != nil {
				return err
			}

			if ok, err := m.insert(ctx, collection, convertedValue); !ok {
				if err != nil {
					return err
				}
			} else {
				log.Printf("object %s CREATED successfully", entry.Name())
			}
		} else {
			return err
		}
	}

	return nil
}

func filterEntriesByExtension(entries []os.DirEntry, extension string) []os.DirEntry {
	var result []os.DirEntry
	for _, entry := range entries {
		if !entry.IsDir() && getFileExtension(entry.Name()) == extension {
			result = append(result, entry)
		}
	}
	return result
}

func getFileExtension(filename string) string {
	if filename != "" {
		splitString := strings.Split(filename, ".")
		return splitString[len(splitString)-1]
	}
	return ""
}

func getFilenameWithoutExtension(filename string) string {
	if filename != "" {
		splitString := strings.Split(filename, ".")
		return splitString[0]
	}
	return ""
}

func (m *migrator) getCurrentValue(ctx context.Context, id string, collection string) (interface{}, error) {
	var value map[string]interface{}
	objectId, _ := primitive.ObjectIDFromHex(id)
	err := m.db.Collection(collection).FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectId}}).Decode(&value)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, err
	}
	return &value, nil
}

func (m *migrator) update(ctx context.Context, id string, collection string, value interface{}) (bool, error) {
	objectId, _ := primitive.ObjectIDFromHex(id)
	updateResult, err := m.db.Collection(collection).UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectId}},
		primitive.M{"$set": value})
	if err != nil {
		return false, err
	}
	if updateResult.MatchedCount <= 0 {
		return false, nil
	}
	return true, nil
}

func (m *migrator) insert(ctx context.Context, collection string, value interface{}) (bool, error) {
	_, err := m.db.Collection(collection).InsertOne(ctx, value)
	if err != nil {
		return false, err
	}
	return true, nil
}

func getLineAndCharacter(input string, offset int) (line int, character int, err error) {
	lf := rune(0x0A)

	if offset > len(input) || offset < 0 {
		return 0, 0, fmt.Errorf("couldn't find offset %d within the input", offset)
	}

	// Humans tend to count from 1.
	line = 1
	for i, b := range input {
		if b == lf {
			line++
			character = 0
		}
		character++
		if i == offset {
			break
		}
	}
	return line, character, nil
}
