# Using Air for Live Reloading in Development

## Overview

[Air](https://github.com/air-verse/air) is a live-reload tool for Go applications that we use in our development workflow. It watches for file changes and automatically rebuilds and restarts the application, making the development process more efficient.

## Installation

If you haven't installed Air yet, you can install it using Go:

```bash
go install github.com/air-verse/air@latest
```

Make sure your Go bin directory is in your PATH.

## Configuration

Our project includes a pre-configured `.air.toml` file in the root directory with the following settings:

```toml
root = "."
tmp_dir = "tmp"

[build]
  cmd = "go build -o ./tmp/main.exe cmd/dinbora/main.go"
  bin = "./tmp/main.exe"
  delay = 1000 # ms
  exclude_dir = ["tmp", "vendor", "node_modules", ".git"]
  exclude_file = []
  exclude_regex = ["_test.go"]
  follow_symlink = true
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html"]
  kill_delay = 500  # ms
  log = "air_build.log"
  poll = false
  stop_on_error = true

[log]
  main_prefix = "[MAIN]"
  runner_prefix = "[RUNNER]"
  timestamp = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"
```

This configuration:
- Builds the application from `cmd/dinbora/main.go`
- Outputs the binary to `./tmp/main.exe`
- Watches for changes in files with `.go`, `.tpl`, `.tmpl`, and `.html` extensions
- Excludes test files and directories like `tmp`, `vendor`, `node_modules`, and `.git`
- Logs build information to `air_build.log`

## Running the Application with Air

To run the application with Air, simply execute the `air` command from the root directory of the project:

```bash
air
```

### On Windows

On Windows machines, you can run:

```bash
air
```

Or if you're using PowerShell:

```powershell
air
```

## What Happens When Air Runs

When you run Air:

1. It builds the application according to the build command in `.air.toml`
2. It starts the application
3. It watches for file changes in the included directories and with the included extensions
4. When a file change is detected, it:
   - Kills the running application
   - Rebuilds the application
   - Restarts the application

## Logs and Output

Air provides colored output in the terminal:
- Yellow for build events
- Magenta for main application logs
- Green for runner events
- Cyan for file watcher events

Build logs are also saved to `air_build.log` for reference.

## Temporary Files

Air creates a `tmp` directory to store the compiled binary and other temporary files. This directory is excluded from watching to prevent infinite rebuild loops.

## Customizing Air Configuration

If you need to customize the Air configuration for your local environment, you can:

1. Copy the `.air.toml` file to a new file (e.g., `.air.local.toml`)
2. Modify the settings as needed
3. Run Air with the custom config:

```bash
air -c .air.local.toml
```

## Troubleshooting

### Common Issues

1. **Application doesn't restart on file changes**
   - Check if the file extension is included in the `include_ext` list
   - Ensure the directory isn't in the `exclude_dir` list
   - Try using `poll = true` if you're on a system where file watching isn't working correctly

2. **Build errors**
   - Check the `air_build.log` file for detailed error messages
   - Ensure your Go environment is properly set up

3. **Port already in use**
   - If you see an error about the port being in use, it might mean the previous instance wasn't properly terminated
   - You can manually kill the process and restart Air

### Cleaning Up

If you need to clean up Air's temporary files:

```bash
rm -rf ./tmp
```

On Windows:

```cmd
rmdir /s /q tmp
```

## Integration with Make

While our current Makefile doesn't include a specific command for Air, you can add one by creating a custom Make command:

```makefile
# Run the application with Air for live reloading
dev:
	air
```

Then you can use:

```bash
make dev
```

## Best Practices

1. **Keep the `.air.toml` file in version control** so all developers use the same configuration
2. **Don't commit the `tmp` directory** (it's already in `.gitignore`)
3. **Use Air only for development**, not for production builds
4. **Check Air's output** for build errors and warnings
