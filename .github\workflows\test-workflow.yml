name: Test Workflow

on:
  workflow_dispatch:  # Allow manual triggering

permissions:
  contents: read

jobs:
  test:
    name: Test GitHub Actions
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Print environment info
      run: |
        echo "GitHub Actions is working!"
        echo "Repository: ${{ github.repository }}"
        echo "Branch: ${{ github.ref }}"
        echo "Workflow: ${{ github.workflow }}"
        echo "Event: ${{ github.event_name }}"

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.23.0'

    - name: Check Go installation
      run: |
        go version
        echo "Go environment is properly set up."
