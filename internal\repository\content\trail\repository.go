package trail

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/content"
)

type Reader interface {
	// All Trails Read - Regular and Extra Trails
	Find(ctx context.Context, id string) (*content.Trail, error)
	FindAll(ctx context.Context) ([]*content.Trail, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.Trail, error)

	FindCardData(ctx context.Context, id string) (*content.TrailCard, error)
	FindAllCardData(ctx context.Context) ([]*content.TrailCard, error)

	// Trail Read
	FindRegularTrail(ctx context.Context, id string) (*content.Trail, error)
	FindAllRegularTrails(ctx context.Context) ([]*content.Trail, error)
	FindRegularTrailByIdentifier(ctx context.Context, identifier string) (*content.Trail, error)

	FindRegularTrailCardData(ctx context.Context, id string) (*content.TrailCard, error)
	FindAllRegularTrailsCardData(ctx context.Context) ([]*content.TrailCard, error)

	// Trail Extra Read
	FindExtraTrail(ctx context.Context, id string, userClassification string) (*content.Trail, error)
	FindAllExtraTrails(ctx context.Context, userClassification string) ([]*content.Trail, error)
	FindExtraTrailByIdentifier(ctx context.Context, identifier string, userClassification string) (*content.Trail, error)

	FindExtraTrailCardData(ctx context.Context, id string, userClassification string) (*content.TrailCard, error)
	FindAllExtraTrailsCardData(ctx context.Context, userClassification string) ([]*content.TrailCard, error)

	// Tutorial Read
	FindTutorial(ctx context.Context, id string) (*content.Tutorial, error)
	FindAllTutorials(ctx context.Context) ([]*content.Tutorial, error)
	FindTutorialByIdentifier(ctx context.Context, identifier string) (*content.Tutorial, error)
	FindTutorialByTicker(ctx context.Context, id string) (*content.Tutorial, error)
}

type Writer interface {
	// Trail Write
	CreateRegularTrail(ctx context.Context, content *content.Trail) error
	UpdateRegularTrail(ctx context.Context, content *content.Trail) error
	DeleteRegularTrail(ctx context.Context, id string) error

	// Trail Extra Write
	CreateExtraTrail(ctx context.Context, content *content.Trail) error
	UpdateExtraTrail(ctx context.Context, content *content.Trail) error
	DeleteExtraTrail(ctx context.Context, id string) error

	// Tutorial Write
	CreateTutorial(ctx context.Context, content *content.Tutorial) error
	UpdateTutorial(ctx context.Context, content *content.Tutorial) error
	DeleteTutorial(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
