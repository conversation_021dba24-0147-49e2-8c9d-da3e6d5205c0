package notification

import (
	"context"
	"fmt"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	brevo "github.com/getbrevo/brevo-go/lib"
)

// BrevoNotifier implements the Notifier interface using Brevo (formerly SendinBlue)
type BrevoNotifier struct {
	client *brevo.APIClient
	config *Config
}

// Brevo template IDs - these would need to be configured in Brevo dashboard
const (
	brevoPasswordResetTemplateID       = 1 // Replace with actual template ID
	brevoSubscriptionConfirmTemplateID = 2 // Replace with actual template ID
	brevoSubscriptionCancelTemplateID  = 3 // Replace with actual template ID
	brevoNewUserWelcomeTemplateID      = 4 // Replace with actual template ID
)

// NewBrevoNotifier creates a new Brevo notifier
func NewBrevoNotifier(config *Config) (*BrevoNotifier, error) {
	if config.BrevoAPIKey == "" {
		return nil, errors.New(errors.Service, "Brevo API key is required", errors.BadRequest, nil)
	}

	cfg := brevo.NewConfiguration()
	cfg.AddDefaultHeader("api-key", config.BrevoAPIKey)

	client := brevo.NewAPIClient(cfg)

	return &BrevoNotifier{
		client: client,
		config: config,
	}, nil
}

// SendPasswordReset sends a password reset email using Brevo
func (b *BrevoNotifier) SendPasswordReset(ctx context.Context, recipientEmail, recipientName, resetLink string) error {
	// Create the email request
	sendSmtpEmail := brevo.SendSmtpEmail{
		To: []brevo.SendSmtpEmailTo{
			{
				Email: recipientEmail,
				Name:  recipientName,
			},
		},
		Sender: &brevo.SendSmtpEmailSender{
			Email: b.config.BrevoSenderEmail,
			Name:  b.config.BrevoSenderName,
		},
		TemplateId: int64(brevoPasswordResetTemplateID),
		Params: map[string]interface{}{
			"resetLink":     resetLink,
			"recipientName": recipientName,
		},
	}

	// Send the email
	result, response, err := b.client.TransactionalEmailsApi.SendTransacEmail(ctx, sendSmtpEmail)
	if err != nil {
		return errors.New(errors.Service, "failed to send password reset email", errors.Internal, err)
	}

	if response.StatusCode >= 400 {
		return errors.New(errors.Service, errors.Message(fmt.Sprintf("Brevo returned error status: %d", response.StatusCode)), errors.Internal, nil)
	}

	// Log success (optional)
	_ = result // result contains message ID for tracking

	return nil
}

// SendSubscriptionConfirmation sends a subscription confirmation email
func (b *BrevoNotifier) SendSubscriptionConfirmation(ctx context.Context, recipientEmail, recipientName, planType string) error {
	sendSmtpEmail := brevo.SendSmtpEmail{
		To: []brevo.SendSmtpEmailTo{
			{
				Email: recipientEmail,
				Name:  recipientName,
			},
		},
		Sender: &brevo.SendSmtpEmailSender{
			Email: b.config.BrevoSenderEmail,
			Name:  b.config.BrevoSenderName,
		},
		TemplateId: int64(brevoSubscriptionConfirmTemplateID),
		Params: map[string]interface{}{
			"recipientName": recipientName,
			"planType":      planType,
		},
	}

	result, response, err := b.client.TransactionalEmailsApi.SendTransacEmail(ctx, sendSmtpEmail)
	if err != nil {
		return errors.New(errors.Service, "failed to send subscription confirmation email", errors.Internal, err)
	}

	if response.StatusCode >= 400 {
		return errors.New(errors.Service, errors.Message(fmt.Sprintf("Brevo returned error status: %d", response.StatusCode)), errors.Internal, nil)
	}

	_ = result
	return nil
}

// SendSubscriptionCancellation sends a subscription cancellation email
func (b *BrevoNotifier) SendSubscriptionCancellation(ctx context.Context, recipientEmail, recipientName, planType string) error {
	sendSmtpEmail := brevo.SendSmtpEmail{
		To: []brevo.SendSmtpEmailTo{
			{
				Email: recipientEmail,
				Name:  recipientName,
			},
		},
		Sender: &brevo.SendSmtpEmailSender{
			Email: b.config.BrevoSenderEmail,
			Name:  b.config.BrevoSenderName,
		},
		TemplateId: int64(brevoSubscriptionCancelTemplateID),
		Params: map[string]interface{}{
			"recipientName": recipientName,
			"planType":      planType,
		},
	}

	result, response, err := b.client.TransactionalEmailsApi.SendTransacEmail(ctx, sendSmtpEmail)
	if err != nil {
		return errors.New(errors.Service, "failed to send subscription cancellation email", errors.Internal, err)
	}

	if response.StatusCode >= 400 {
		return errors.New(errors.Service, errors.Message(fmt.Sprintf("Brevo returned error status: %d", response.StatusCode)), errors.Internal, nil)
	}

	_ = result
	return nil
}

// SendNewUserWelcome sends a welcome email to new users with password setup link
func (b *BrevoNotifier) SendNewUserWelcome(ctx context.Context, recipientEmail, recipientName, resetLink, planType string) error {
	sendSmtpEmail := brevo.SendSmtpEmail{
		To: []brevo.SendSmtpEmailTo{
			{
				Email: recipientEmail,
				Name:  recipientName,
			},
		},
		Sender: &brevo.SendSmtpEmailSender{
			Email: b.config.BrevoSenderEmail,
			Name:  b.config.BrevoSenderName,
		},
		TemplateId: int64(brevoNewUserWelcomeTemplateID),
		Params: map[string]interface{}{
			"recipientName": recipientName,
			"resetLink":     resetLink,
			"planType":      planType,
		},
	}

	result, response, err := b.client.TransactionalEmailsApi.SendTransacEmail(ctx, sendSmtpEmail)
	if err != nil {
		return errors.New(errors.Service, "failed to send new user welcome email", errors.Internal, err)
	}

	if response.StatusCode >= 400 {
		return errors.New(errors.Service, errors.Message(fmt.Sprintf("Brevo returned error status: %d", response.StatusCode)), errors.Internal, nil)
	}

	_ = result
	return nil
}

// SendGenericEmail sends a generic email with custom content
func (b *BrevoNotifier) SendGenericEmail(ctx context.Context, recipientEmail, recipientName, subject, content string) error {
	sendSmtpEmail := brevo.SendSmtpEmail{
		To: []brevo.SendSmtpEmailTo{
			{
				Email: recipientEmail,
				Name:  recipientName,
			},
		},
		Sender: &brevo.SendSmtpEmailSender{
			Email: b.config.BrevoSenderEmail,
			Name:  b.config.BrevoSenderName,
		},
		Subject:     subject,
		HtmlContent: content,
	}

	result, response, err := b.client.TransactionalEmailsApi.SendTransacEmail(ctx, sendSmtpEmail)
	if err != nil {
		return errors.New(errors.Service, "failed to send generic email", errors.Internal, err)
	}

	if response.StatusCode >= 400 {
		return errors.New(errors.Service, errors.Message(fmt.Sprintf("Brevo returned error status: %d", response.StatusCode)), errors.Internal, nil)
	}

	_ = result
	return nil
}
