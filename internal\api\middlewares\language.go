package middlewares

import (
	"github.com/dsoplabs/dinbora-backend/internal/service/i18n"
	"github.com/labstack/echo/v4"
)

// LanguageDetectionConfig holds configuration for language detection middleware
type LanguageDetectionConfig struct {
	// Skipper defines a function to skip middleware
	Skipper func(echo.Context) bool
	
	// I18nService is the internationalization service instance
	I18nService *i18n.Service
}

// DefaultLanguageDetectionConfig returns default configuration for language detection middleware
func DefaultLanguageDetectionConfig(i18nService *i18n.Service) LanguageDetectionConfig {
	return LanguageDetectionConfig{
		Skipper:     func(c echo.Context) bool { return false },
		I18nService: i18nService,
	}
}

// LanguageDetection returns a middleware that detects user language from Accept-Language header
// and stores it in the Echo context for later use by error handlers and other components.
//
// The middleware:
// 1. Extracts the Accept-Language header from the request
// 2. Uses the i18n service to detect the best matching supported language
// 3. Stores the detected language in the Echo context
// 4. Falls back to the default language (pt) if no supported language is found
//
// Usage:
//   i18nService, _ := i18n.New()
//   e.Use(LanguageDetection(i18nService))
func LanguageDetection(i18nService *i18n.Service) echo.MiddlewareFunc {
	config := DefaultLanguageDetectionConfig(i18nService)
	return LanguageDetectionWithConfig(config)
}

// LanguageDetectionWithConfig returns a language detection middleware with custom configuration
func LanguageDetectionWithConfig(config LanguageDetectionConfig) echo.MiddlewareFunc {
	// Validate configuration
	if config.I18nService == nil {
		panic("i18n service is required for language detection middleware")
	}
	
	if config.Skipper == nil {
		config.Skipper = func(c echo.Context) bool { return false }
	}

	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Skip middleware if configured to do so
			if config.Skipper(c) {
				return next(c)
			}

			// Get Accept-Language header from request
			acceptLanguage := c.Request().Header.Get("Accept-Language")
			
			// Detect language using i18n service
			detectedLanguage := config.I18nService.DetectLanguageFromHeader(acceptLanguage)
			
			// Store language in Echo context
			c.Set(i18n.LanguageContextKey, detectedLanguage)
			
			// Continue to next handler
			return next(c)
		}
	}
}

// GetLanguageFromEchoContext extracts the detected language from Echo context
// Returns the default language if no language was detected or stored
func GetLanguageFromEchoContext(c echo.Context) string {
	if lang, ok := c.Get(i18n.LanguageContextKey).(string); ok {
		return lang
	}
	return i18n.DefaultLanguage
}
