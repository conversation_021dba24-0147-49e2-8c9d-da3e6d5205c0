package league

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// League represents a user-created competition group
type League struct {
	ObjectID          primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID                string             `json:"id,omitempty" bson:"-"`
	Code              string             `json:"code,omitempty" bson:"code,omitempty"` // Can be generated on demand
	Name              string             `json:"name" bson:"name"`
	BackgroundColor   string             `json:"backgroundColor" bson:"backgroundColor"`
	OwnerUserID       string             `json:"ownerUserID" bson:"ownerUserID"`
	Members           []LeagueMember     `json:"members" bson:"members"`
	CurrentSeason     Season             `json:"currentSeason" bson:"currentSeason"`
	LevelRequirements []LevelRequirement `json:"levelRequirements" bson:"levelRequirements"`
}

type LeagueCard struct {
	ObjectID                primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID                      string             `json:"id,omitempty" bson:"-"`
	Name                    string             `json:"name" bson:"name"`
	BackgroundColor         string             `json:"backgroundColor" bson:"backgroundColor"`
	LeaderName              string             `json:"leaderName" bson:"leaderName"`
	LeaderPhotoURL          string             `json:"leaderPhotoURL" bson:"leaderPhotoURL"`
	LeaderTransactionStreak int                `json:"leaderTransactionStreak" bson:"leaderTransactionStreak"`

	UserName              string `json:"userName" bson:"userName"`
	UserPhotoURL          string `json:"userPhotoURL" bson:"userPhotoURL"`
	UserTransactionStreak int    `json:"userTransactionStreak" bson:"userTransactionStreak"`

	RemainingDaysInSeason int `json:"remainingDaysInSeason" bson:"remainingDaysInSeason"`
}

// LeagueMember represents a user within a league
type LeagueMember struct {
	UserID                 string    `json:"userID" bson:"userID"`
	UserName               string    `json:"userName" bson:"userName"`
	PhotoURL               string    `json:"photo,omitempty" bson:"photo,omitempty"` // Renamed from AvatarURL
	JoinedAt               time.Time `json:"joinedAt" bson:"joinedAt"`
	TransactionStreak      int       `json:"transactionStreak" bson:"transactionStreak"`                               // Streak within this specific league for the current season
	CurrentLeagueLevel     Level     `json:"currentLeagueLevel" bson:"currentLeagueLevel"`                             // User's level within this specific league
	LastStreakActivityDate time.Time `json:"lastStreakActivityDate,omitempty" bson:"lastStreakActivityDate,omitempty"` // To track daily streak activity
}

// Season represents a league season period
type Season struct {
	ObjectID  primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID        string             `json:"id,omitempty" bson:"-"`
	StartDate time.Time          `json:"startDate" bson:"startDate"`
	EndDate   time.Time          `json:"endDate" bson:"endDate"`
}

// LevelRequirement represents requirements for each league level
type LevelRequirement struct {
	Level                   Level  `json:"level" bson:"level"`
	TransactionStreakNeeded int    `json:"transactionStreakNeeded" bson:"transactionStreakNeeded"`
	Emoji                   string `json:"emoji" bson:"emoji"`
	Color                   string `json:"color" bson:"color"`
	Description             string `json:"description" bson:"description"`
}

// Level represents a league level (e.g., bronze, silver)
type Level string

const (
	BronzeLevel  Level = "bronze"
	SilverLevel  Level = "silver"
	GoldLevel    Level = "gold"
	DiamondLevel Level = "diamond"
)

// LeagueRanking represents user's position in a specific league
type LeagueRanking struct {
	Podium  []*UserRanking `json:"podium" bson:"podium"`
	General []*UserRanking `json:"general" bson:"general"`
}

// UserRanking represents a user's rank in a league
type UserRanking struct {
	UserID                 string `json:"userID" bson:"userID"`
	UserName               string `json:"userName" bson:"userName"`
	PhotoURL               string `json:"photo" bson:"photo"`
	CurrentLeagueLevel     Level  `json:"currentLeagueLevel" bson:"currentLeagueLevel"`
	TotalTransactionStreak int    `json:"totalTransactionStreak" bson:"totalTransactionStreak"`
}
