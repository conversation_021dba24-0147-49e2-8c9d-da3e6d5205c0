package errors

type Layer string

const (
	Server     Layer = "server"
	Middleware Layer = "middleware"
	Controller Layer = "controller"
	Logger     Layer = "logger"
	Migrator   Layer = "migrator"
	Model      Layer = "model"
	Repository Layer = "repository"
	Service    Layer = "service"
	Token      Layer = "token"
)

type Kind string

const (
	// 4xx Client Errors
	BadRequest           Kind = "bad request"            // 400: Generic bad request
	Validation           Kind = "validation"             // 400: Input data fails validation checks
	Unauthorized         Kind = "unauthorized"           // 401: Authentication credentials missing
	Forbidden            Kind = "forbidden"              // 403: Access to resource is prohibited
	NotFound             Kind = "not found"              // 404: Requested resource doesn't exist
	RequestTimeout       Kind = "request timeout"        // 408: Request exceeded time limit
	Conflict             Kind = "conflict"               // 409: Request conflicts with current state
	Gone                 Kind = "gone"                   // 410: Resource no longer available
	LengthRequired       Kind = "length required"        // 411: Content-Length header required
	PreconditionFailed   Kind = "precondition failed"    // 412: Conditional request not met
	PayloadTooLarge      Kind = "payload too large"      // 413: Request body exceeds size limit
	URITooLong           Kind = "uri too long"           // 414: Request URI too long
	UnsupportedMediaType Kind = "unsupported media type" // 415: Unsupported content type
	RangeNotSatisfiable  Kind = "range not satisfiable"  // 416: Requested range cannot be fulfilled
	ExpectationFailed    Kind = "expectation failed"     // 417: Expectation in headers cannot be met
	Teapot               Kind = "teapot"                 // 418: I'm a teapot (RFC 2324)
	MisdirectedRequest   Kind = "misdirected request"    // 421: Request sent to server unable to produce response
	UnprocessableEntity  Kind = "unprocessable entity"   // 422: Semantic errors in request entity
	Locked               Kind = "locked"                 // 423: Resource is locked
	FailedDependency     Kind = "failed dependency"      // 424: Failed dependency on previous request
	TooEarly             Kind = "too early"              // 425: Server unwilling to risk processing replay
	UpgradeRequired      Kind = "upgrade required"       // 426: Client should upgrade protocol
	PreconditionRequired Kind = "precondition required"  // 428: Precondition required but not sent
	TooManyRequests      Kind = "too many requests"      // 429: Rate limit exceeded

	// 5xx Server Errors
	Internal            Kind = "internal"              // 500: Unexpected server-side error
	NotImplemented      Kind = "not implemented"       // 501: Functionality not implemented
	BadGateway          Kind = "bad gateway"           // 502: Invalid response from upstream server
	ServiceUnavailable  Kind = "service unavailable"   // 503: Server temporarily unavailable
	GatewayTimeout      Kind = "gateway timeout"       // 504: Upstream server timeout
	VersionNotSupported Kind = "version not supported" // 505: HTTP version not supported
	InsufficientStorage Kind = "insufficient storage"  // 507: Insufficient storage to complete request
	LoopDetected        Kind = "loop detected"         // 508: Infinite loop detected in request
	NotExtended         Kind = "not extended"          // 510: Further extensions needed
	NetworkAuthRequired Kind = "network auth required" // 511: Network authentication required
)

type Message string

const (
	// User Forbidden Errors
	// Pattern: User + Forbidden + Reason
	UserForbidden Message = "user forbidden"

	// User Repository Errors - Not Found
	// Pattern: Entity + Criteria + NotFound
	UserNotFound               Message = "user not found"
	UserByIDNotFound           Message = "user by ID not found"
	UserByEmailNotFound        Message = "user email not found"
	UserByReferralCodeNotFound Message = "user by referral code not found"
	DeletedUserByEmailNotFound Message = "deleted user by email not found"
	UserForUpdateNotFound      Message = "user not found for update"
	UserForDeletionNotFound    Message = "user not found for deletion"

	// User Repository Errors - Operation Failed
	// Pattern: Entity + Action + Failed
	UserCreateFailed             Message = "failed to create user"
	UserUpdateFailed             Message = "failed to update user"
	UserDeleteFailed             Message = "failed to delete user"
	UserFindByIDFailed           Message = "find user by ID failed"
	UserFindByEmailFailed        Message = "find user by email failed"
	UserFindByReferralCodeFailed Message = "find user by referral code failed"
	DeletedUserCreateFailed      Message = "failed to create deleted user entry"
	DeletedUserFindByEmailFailed Message = "find deleted user by email failed"

	// User Conflict Errors
	// Pattern: Entity + Conflict + Reason
	UserConflictExists        Message = "user already exists"
	UserConflictUpdate        Message = "user update would cause conflict"
	DeletedUserConflictExists Message = "deleted user already exists in trash"

	// Admin Errors
	// Pattern: Admin + Entity + Error
	AdminUsersNotFound Message = "admin users not found"

	// Dreamboard Repository Errors
	DreamboardConflictExists     Message = "dreamboard conflict exists"
	DreamboardCreateFailed       Message = "dreamboard create failed"
	DreamboardFindFailed         Message = "dreamboard find failed"
	DreamboardNotFound           Message = "dreamboard not found"
	DreamboardInvalidID          Message = "dreamboard invalid id"
	DreamboardFindAllFailed      Message = "dreamboard find all failed"
	DreamboardDecodeFailed       Message = "dreamboard decode failed"
	DreamboardFindByUserFailed   Message = "dreamboard find by user failed"
	DreamboardConflictUpdate     Message = "dreamboard conflict update"
	DreamboardUpdateFailed       Message = "dreamboard update failed"
	DreamboardDeleteFailed       Message = "dreamboard delete failed"
	DreamAddFailed               Message = "dream add failed"
	DreamUpdateFailed            Message = "dream update failed"
	DreamNotFound                Message = "dream not found"
	DreamRemoveFailed            Message = "dream remove failed"
	DreamboardDeleteCreateFailed Message = "dreamboard delete create failed"
	DeletedDreamboardNotFound    Message = "deleted dreamboard not found"
	DeletedDreamboardFindFailed  Message = "deleted dreamboard find failed"

	// Financial Sheet Repository Errors
	FinancialSheetConflictExists   Message = "financial sheet conflict exists"
	FinancialSheetCreateFailed     Message = "financial sheet create failed"
	FinancialSheetFindFailed       Message = "financial sheet find failed"
	FinancialSheetFindAllFailed    Message = "financial sheet find all failed"
	FinancialSheetNotFound         Message = "financial sheet not found"
	FinancialSheetInvalidID        Message = "financial sheet invalid id"
	FinancialSheetUserNotFound     Message = "financial sheet user not found"
	FinancialSheetFindByUserFailed Message = "financial sheet find by user failed"
	FinancialSheetConflictUpdate   Message = "financial sheet conflict update"
	FinancialSheetUpdateFailed     Message = "financial sheet update failed"
	FinancialSheetDeleteFailed     Message = "financial sheet delete failed"

	// Financial Sheet Category Repository Errors
	FinancialSheetCategoryConflictExists   Message = "financial sheet category conflict exists"
	FinancialSheetCategoryCreateFailed     Message = "financial sheet category create failed"
	FinancialSheetCategoryFindFailed       Message = "financial sheet category find failed"
	FinancialSheetCategoryNotFound         Message = "financial sheet category not found"
	FinancialSheetCategoryInvalidID        Message = "financial sheet category invalid id"
	FinancialSheetCategoryConflictUpdate   Message = "financial sheet category conflict update"
	FinancialSheetCategoryUpdateFailed     Message = "financial sheet category update failed"
	FinancialSheetCategoryDeleteFailed     Message = "financial sheet category delete failed"
	FinancialSheetCategoryFindByIDFailed   Message = "financial sheet category find by ID failed"
	FinancialSheetCategoryFindByNameFailed Message = "financial sheet category find by name failed"

	// Financial DNA Repository Errors
	FinancialDNAConflictExists     Message = "financial dna tree conflict exists"
	FinancialDNACreateFailed       Message = "financial dna tree create failed"
	FinancialDNAFindFailed         Message = "financial dna tree find failed"
	FinancialDNANotFound           Message = "financial dna tree not found"
	FinancialDNAInvalidID          Message = "financial dna tree invalid id"
	FinancialDNAUserNotFound       Message = "financial dna tree user not found"
	FinancialDNAFindByUserFailed   Message = "financial dna tree find by user failed"
	FinancialDNAConflictUpdate     Message = "financial dna tree conflict update"
	FinancialDNAUpdateFailed       Message = "financial dna tree update failed"
	FinancialDNADeleteFailed       Message = "financial dna tree delete failed"
	FinancialDNAFindAllFailed      Message = "financial dna tree find all failed"
	FinancialDNAFindByIDFailed     Message = "financial dna tree find by ID failed"
	FinancialDNAFindByNameFailed   Message = "financial dna tree find by name failed"
	FinancialDNAFindByUserIDFailed Message = "financial dna tree find by user ID failed"

	// Token Errors
	TokenInvalid             Message = "invalid token"
	TokenCannotGetClaims     Message = "cannot get claims from token"
	TokenInvalidSigninMethod Message = "invalid token signing method"
	TokenCannotCreateAccess  Message = "cannot create access token"
	TokenCannotCreateRefresh Message = "cannot create refresh token"

	// Progression Errors
	// Pattern: Progression + Required/Invalid + Field/Entity
	ProgressionRequiredTrailValue            Message = "required trail value"
	ProgressionRequiredLessonValue           Message = "required lesson value"
	ProgressionRequiredTypeValue             Message = "required type value"
	ProgressionTypeMustBeLessonOrChallenge   Message = "type must be LESSON or CHALLENGE"
	ProgressionRequiredChoiceValue           Message = "required choice value"
	ProgressionRequiredChoiceIdentifierValue Message = "required choice.identifier value"
	ProgressionRequiredChoiceNextValue       Message = "required choice.next value"
	ProgressionInvalidLessonProgress         Message = "invalid lesson progression"
	ProgressionInvalidTrailProgress          Message = "invalid trail progression"
	ProgressionInvalidLesson                 Message = "invalid lesson"
	ProgressionInvalidContent                Message = "invalid content"
	ProgressionInvalidContentProgress        Message = "invalid content progression"
	ProgressionInvalidChallengeProgress      Message = "invalid challenge progression"
	ProgressionInvalidChallenge              Message = "invalid challenge"
	ProgressionInvalidChallengePhaseProgress Message = "invalid challenge content progression"
)
