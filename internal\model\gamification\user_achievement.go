package gamification

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Achievement represents a single achievement earned by a user
type Achievement struct {
	Identifier string    `json:"identifier" bson:"identifier"`
	EarnedAt   time.Time `json:"earnedAt" bson:"earnedAt"`
}

// UserAchievement represents a consolidated document containing all achievements for a user
type UserAchievement struct {
	ObjectID     primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID           string             `json:"id,omitempty" bson:"-"`
	UserID       string             `json:"userId" bson:"userId"`
	Achievements []*Achievement     `json:"achievements" bson:"achievements"`
	CreatedAt    time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt    time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// PrepareCreate prepares the user achievement document for creation
func (ua *UserAchievement) PrepareCreate() {
	now := time.Now()
	ua.CreatedAt = now
	ua.UpdatedAt = now
}

// PrepareUpdate prepares the user achievement document for update
func (ua *UserAchievement) PrepareUpdate() {
	ua.UpdatedAt = time.Now()
}

// SetID sets the ID from ObjectID
func (ua *UserAchievement) SetID() {
	if !ua.ObjectID.IsZero() {
		ua.ID = ua.ObjectID.Hex()
	}
}

// HasAchievement checks if the user has a specific achievement
func (ua *UserAchievement) HasAchievement(identifier string) bool {
	for _, achievement := range ua.Achievements {
		if achievement.Identifier == identifier {
			return true
		}
	}
	return false
}

// FindAchievement returns a specific achievement if the user has it
func (ua *UserAchievement) FindAchievement(identifier string) *Achievement {
	for _, achievement := range ua.Achievements {
		if achievement.Identifier == identifier {
			return achievement
		}
	}
	return nil
}

// CreateAchievement creates a new achievement in the user's achievements list
func (ua *UserAchievement) CreateAchievement(identifier string, earnedAt time.Time) {
	// Check if achievement already exists
	if !ua.HasAchievement(identifier) {
		ua.Achievements = append(ua.Achievements, &Achievement{
			Identifier: identifier,
			EarnedAt:   earnedAt,
		})
	}
}
