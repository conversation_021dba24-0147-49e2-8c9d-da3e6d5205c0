package league

import (
	"testing"
)

func TestInviteLeagueResponse(t *testing.T) {
	response := InviteDetailsResponse{
		Name:           "League Name",
		OwnerUserName:  "Owner Name",
		SeasonDuration: 30,
	}

	if response.Name != "League Name" {
		t.<PERSON><PERSON>rf("Expected Name to be 'League Name', got %s", response.Name)
	}

	if response.OwnerUserName != "Owner Name" {
		t.<PERSON><PERSON>("Expected OwnerUserName to be 'Owner Name', got %s", response.OwnerUserName)
	}

	if response.SeasonDuration != 30 {
		t.<PERSON><PERSON><PERSON>("Expected SeasonDuration to be 30, got %d", response.SeasonDuration)
	}
}

func TestJoinLeagueEndpoint(t *testing.T) {
	t.Run("JoinLeague supports both query param and body", func(t *testing.T) {
		// This is a conceptual test to document the expected behavior
		// In practice, you would need to set up a full Echo context with mocks

		// Test case 1: Query parameter should work
		// GET /v2/leagues/join?code=ABCDE12345
		// POST /v2/leagues/join?code=ABCDE12345

		// Test case 2: JSON body should work
		// POST /v2/leagues/join with {"inviteCode": "ABCDE12345"}

		// Test case 3: Query parameter takes precedence over body
		// POST /v2/leagues/join?code=QUERY123 with {"inviteCode": "BODY456"}
		// Should use "QUERY123"

		t.Log("Single JoinLeague endpoint supports both GET and POST methods")
		t.Log("Supports both query parameter (?code=) and JSON body (inviteCode)")
		t.Log("Query parameter takes precedence over JSON body when both are present")
	})
}
