import json
import os
import argparse
import re # Keep re for standardization
from urllib.parse import urlparse, unquote, urlunparse
from pathlib import Path

# --- Configuration ---
OLD_URL_PREFIX = "https://arkc-images.nyc3.cdn.digitaloceanspaces.com/gifs/"
NEW_URL_BASE = "https://images.dinbora.com.br/gifs/"
# Keys that might contain the target URLs
URL_KEYS = {'logo', 'image'} # Add other keys if needed
# --- End Configuration ---

def standardize_filename(filename):
    """
    Standardizes the filename part according to the rules:
    - Removes leading digits followed by a hyphen (e.g., '1044-name' -> 'name').
    - Removes trailing number suffixes like (1), (2).
    - Converts to lowercase.
    - Replaces spaces and underscores with hyphens.
    - Cleans up multiple consecutive hyphens.
    - Removes leading/trailing hyphens.
    - Ensures it ends with .gif
    """
    if not filename:
        return None

    base_name, ext = os.path.splitext(filename)
    ext = ext.lower()

    # Ensure it's a gif extension we are dealing with
    if ext != ".gif":
         if not ext:
             ext = ".gif" # Assume .gif if no extension
         else:
             # Keep original if not gif, maybe it's intentional
             return filename

    # --- Step 1: Remove unwanted prefixes/suffixes ---
    # Remove trailing (number) suffixes, handling potential spaces
    base_name = re.sub(r'\s*\(\d+\)$', '', base_name).strip()
    # Remove leading digits followed by a hyphen (e.g., 123-)
    base_name = re.sub(r'^\d+-', '', base_name)

    # --- Step 2: Standardize characters and case ---
    # Convert to lowercase and replace spaces/underscores with hyphens
    standardized = base_name.lower().replace(' ', '-').replace('_', '-')

    # --- Step 3: Clean up hyphens ---
    # Clean up multiple consecutive hyphens
    standardized = re.sub(r'-+', '-', standardized)
    # Remove leading/trailing hyphens that might result from replacements
    standardized = standardized.strip('-')

    # Return None if standardization results in empty name (edge case)
    if not standardized:
        print(f"Warning: Standardization resulted in empty base name for '{filename}'. Skipping.")
        return None

    return f"{standardized}{ext}"


def process_node(data):
    """
    Recursively processes the JSON structure (dictionaries, lists, or values).
    Handles URL replacements within dictionaries automatically based on standardization rules.
    """
    if isinstance(data, dict):
        # Process dictionary: handle URL keys and recurse for other values
        processed_dict = {}
        for key, value in data.items():
            if key in URL_KEYS and isinstance(value, str) and value.startswith(OLD_URL_PREFIX):
                # This is a target URL key
                try:
                    parsed_url = urlparse(value)
                    # Extract filename, decode URL encoding (like %20)
                    original_filename = unquote(os.path.basename(parsed_url.path))

                    if not original_filename:
                        print(f"Warning: Could not extract filename from URL: {value}. Keeping original.")
                        processed_dict[key] = value
                        continue

                    # --- Apply standardization directly ---
                    new_filename = standardize_filename(original_filename)

                    if new_filename is None:
                        # Standardization failed or resulted in empty name
                        print(f"Warning: Failed to standardize filename '{original_filename}' from URL: {value}. Keeping original.")
                        processed_dict[key] = value # Keep original if failed
                        continue
                    # --- End direct standardization ---


                    # --- Construct the new URL ---
                    # Combine the NEW_URL_BASE path with the new filename
                    # Use os.path.join for safer path joining, then ensure correct slashes for URL
                    new_path_segment = os.path.join(urlparse(NEW_URL_BASE).path, new_filename).replace("\\", "/") # Ensure forward slashes

                    # Make sure path starts with / if base path does, handle potential double slashes
                    base_path = urlparse(NEW_URL_BASE).path
                    if base_path.endswith('/'):
                         base_path = base_path[:-1] # Avoid double slash if joining "/" and "file.gif"
                    if new_path_segment.startswith('/'):
                         new_path_segment = new_path_segment[1:] # Avoid double slash if joining "/base/" and "/file.gif"

                    new_path = f"{base_path}/{new_path_segment}"


                    new_url_parts = urlparse(NEW_URL_BASE)
                    final_new_url = urlunparse((
                        new_url_parts.scheme,
                        new_url_parts.netloc,
                        new_path,
                        '', # params
                        '', # query
                        ''  # fragment
                    ))

                    if value != final_new_url: # Only print if change occurs
                        print(f"Replacing ('{key}'): '{value}' -> '{final_new_url}'")
                    processed_dict[key] = final_new_url # Use the modified URL

                except Exception as e:
                    print(f"Error processing URL '{value}' in key '{key}': {e}. Keeping original.")
                    processed_dict[key] = value # Keep original on error
            else:
                # For non-URL keys, or non-matching URLs, recurse
                processed_dict[key] = process_node(value)

        return processed_dict

    elif isinstance(data, list):
        # Process list: recurse for each item
        return [process_node(item) for item in data]
    else:
        # Return primitive types (string, int, bool, null) unchanged
        return data

# --- Main Execution ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Automatically update specific GIF URLs in a JSON file, standardizing filenames (including removing leading 'digits-')." # Updated description
        )
    parser.add_argument("json_file_path",
                        help="Path to the JSON file to analyze and modify.")
    # Removed gif_folder_path argument

    args = parser.parse_args()

    json_path = Path(args.json_file_path)
    script_dir = Path(__file__).parent # Directory where the script is running

    # --- Input Validation ---
    if not json_path.is_file():
        print(f"Error: JSON file not found at '{json_path}'")
        exit(1)

    # --- Load JSON Data ---
    try:
        print(f"\nLoading JSON file: {json_path}")
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON file '{json_path}': {e}")
        exit(1)
    except Exception as e:
        print(f"Error reading JSON file '{json_path}': {e}")
        exit(1)

    # --- Process the JSON Data using the automatic approach ---
    print("\nProcessing JSON data...")
    modified_data = process_node(data) # Start the recursive processing
    print("\nProcessing complete.")

    # --- Save Modified JSON ---
    output_filename = json_path.name # Use the same name as the input file
    output_path = script_dir / output_filename # Save in the script's directory

    try:
        print(f"\nSaving modified JSON to: {output_path}")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(modified_data, f, indent=2, ensure_ascii=False) # Use indent=2 for readability
        print("File saved successfully.")
    except Exception as e:
        print(f"Error saving modified JSON file to '{output_path}': {e}")
        exit(1)