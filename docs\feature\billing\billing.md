# Billing System Documentation Index

> **Note**: This file provides a quick overview. For comprehensive documentation, see the detailed guides below.

## 📚 Documentation Structure

### Core Documentation
- **[Complete System Guide](README.md)** - Comprehensive overview with examples and integration patterns
- **[System Architecture](architecture.md)** - Detailed architectural design and data flow
- **[Webhook Integration](webhooks.md)** - Payment provider webhook implementation and testing

### Quick Reference
- **[API Endpoints](#-api-endpoints)** - Quick reference for all billing endpoints
- **[Feature Integration](#-feature-integration)** - How to integrate billing with other services
- **[Common Use Cases](#-common-use-cases)** - Typical implementation patterns

## 🏗️ System Overview

The billing system provides comprehensive subscription management with:

### Core Components
1. **Plan Management** - Subscription tiers with pricing and features
2. **Subscription Lifecycle** - Trial periods, renewals, and cancellations
3. **Payment Processing** - Multi-provider webhook integration
4. **Access Control** - Feature-based middleware for premium content

### Architecture Layers
```
┌─────────────────┐
│   Controller    │ ← REST API endpoints (v2)
├─────────────────┤
│    Service      │ ← Business logic & orchestration
├─────────────────┤
│   Repository    │ ← MongoDB data persistence
├─────────────────┤
│     Model       │ ← Data structures & validation
└─────────────────┘
```

## 🚀 Quick Start

### 1. Basic Plan Creation (Admin Only)
```bash
curl -X POST /v2/billing/plans \
  -H "Authorization: Bearer <admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Premium Plan",
    "price": 2999,
    "currency": "BRL",
    "features": ["premium_content", "advanced_analytics"],
    "durationMonths": 12,
    "trialDays": 7
  }'
```

### 2. User Subscription
```bash
curl -X POST /v2/billing/subscriptions \
  -H "Authorization: Bearer <user_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "507f1f77bcf86cd799439011",
    "provider": "hotmart"
  }'
```

### 3. Protect Endpoints with Middleware
```go
// Require active subscription
router.GET("/premium-feature", handler,
    middlewares.RequireActiveSubscription(billingService))

// Require specific features
router.GET("/advanced-analytics", handler,
    middlewares.RequireFeature([]string{"advanced_analytics"}, billingService))
```

## 📋 API Endpoints

### Plan Management (Admin Only)
- `POST /v2/billing/plans` - Create plan
- `GET /v2/billing/plans` - List plans
- `GET /v2/billing/plans/:id` - Get plan details
- `PUT /v2/billing/plans/:id` - Update plan
- `DELETE /v2/billing/plans/:id` - Delete plan

### Subscription Management
- `POST /v2/billing/admin/subscriptions` - Create subscription (admin only)
- `GET /v2/billing/subscriptions/me` - Get user subscriptions
- `GET /v2/billing/subscriptions/:id` - Get subscription details
- `POST /v2/billing/subscriptions/:id/cancel` - Cancel subscription

### Access Control
- `GET /v2/billing/access/me` - Get user access and features

### Webhooks
- `POST /v2/billing/webhooks/hotmart` - Hotmart payment notifications
- `POST /v2/billing/webhooks/apple` - Apple Pay notifications

## 🔧 Feature Integration

### Dashboard Package Example
```go
func (dc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
    dashboardGroup := currentGroup.Group("/dashboard", middlewares.AuthGuard())

    // Basic features (free)
    dashboardGroup.GET("/me", dc.FindDashboard())

    // Premium features
    premiumGroup := dashboardGroup.Group("", middlewares.RequirePremiumAccess(billingService))
    premiumGroup.GET("/advanced-analytics", dc.FindAdvancedAnalytics())
}
```

### Service Layer Access Check
```go
func (s *service) GetPremiumData(ctx context.Context, userID primitive.ObjectID) (*Data, error) {
    hasAccess, err := s.billingService.HasActiveSubscription(ctx, userID)
    if err != nil {
        return nil, err
    }

    if !hasAccess {
        return nil, errors.New(errors.Service, "premium subscription required", errors.Forbidden, nil)
    }

    // Continue with premium logic...
}
```

## 🎯 Common Use Cases

### 1. Premium Content Access
```go
// Middleware approach
router.GET("/premium-content", handler,
    middlewares.RequireFeature([]string{"premium_content"}, billingService))

// Service layer approach
if !billingService.HasFeature(ctx, userID, "premium_content") {
    return errors.New(errors.Service, "premium content requires subscription", errors.Forbidden, nil)
}
```

### 2. Trial Period Handling
```go
// Allow trial users but track trial status
router.GET("/trial-feature", handler,
    middlewares.SubscriptionGuard(middlewares.SubscriptionGuardConfig{
        RequireActiveSubscription: true,
        AllowTrial: true,
    }, billingService))

// In handler, check trial status
subscriptionCtx := ctx.Get("subscription_context").(*middlewares.SubscriptionContext)
if subscriptionCtx.IsInTrial {
    // Show trial-specific UI or limitations
}
```

### 3. Multiple Feature Requirements
```go
// Require multiple features (AND logic)
router.GET("/advanced-feature", handler,
    middlewares.RequireFeature([]string{
        "premium_content",
        "advanced_analytics",
        "priority_support",
    }, billingService))
```

## 🔒 Security Features

- **JWT Authentication** - All endpoints require valid tokens
- **Role-based Access** - Admin-only plan management
- **Feature Validation** - Middleware validates access on every request
- **Webhook Security** - Signature verification and deduplication
- **User Isolation** - Users can only access their own data

## 📊 Key Features

- ✅ **12-month subscriptions** with 7-day free trials
- ✅ **Grace period handling** - Access continues until end date after cancellation
- ✅ **Multiple subscriptions** per user with union of benefits
- ✅ **Multi-provider support** - Hotmart and Apple Pay webhooks
- ✅ **Feature-based access control** - Granular permission system
- ✅ **Audit trail** - Complete payment transaction history
- ✅ **Auto-renewal** - Configurable automatic subscription renewal

## � Next Steps

1. **Read the [Complete System Guide](README.md)** for detailed implementation examples
2. **Review [Architecture Documentation](architecture.md)** for system design details
3. **Set up [Webhook Integration](webhooks.md)** for payment processing
4. **Write comprehensive tests** for your billing integration
5. **Configure monitoring** for subscription and payment events
