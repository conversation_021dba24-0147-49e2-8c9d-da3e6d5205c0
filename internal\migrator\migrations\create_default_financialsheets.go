package migrations

import (
	"context"
	"fmt"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// CreateDefaultFinancialsheetsMigration implements the Migration interface for creating default financialsheets
type CreateDefaultFinancialsheetsMigration struct {
	db      *mongo.Database
	service financialsheet.Service
}

func NewCreateDefaultFinancialsheetsMigration(db *mongo.Database, service financialsheet.Service) *CreateDefaultFinancialsheetsMigration {
	return &CreateDefaultFinancialsheetsMigration{
		db:      db,
		service: service,
	}
}

func (m *CreateDefaultFinancialsheetsMigration) Name() string {
	return "recreate_default_financialsheets_20250225"
}

func (m *CreateDefaultFinancialsheetsMigration) Up(ctx context.Context) error {
	// Drop the financial sheets collection to start fresh
	log.Printf("Dropping financial sheets collection...")
	err := m.db.Collection(repository.FINANCIAL_SHEETS_COLLECTION).Drop(ctx)
	if err != nil {
		return fmt.Errorf("failed to drop financial sheets collection: %w", err)
	}

	// Get all users since we need to recreate sheets for everyone
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.D{}}}, // Match all users
	}

	userCollection := m.db.Collection(repository.USERS_COLLECTION)
	cursor, err := userCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return fmt.Errorf("failed to find users without financial sheets: %w", err)
	}
	defer cursor.Close(ctx)

	successCount := 0
	errorCount := 0

	for cursor.Next(ctx) {
		var user model.User
		if err := cursor.Decode(&user); err != nil {
			log.Printf("Error decoding user: %v", err)
			errorCount++
			continue
		}

		err = m.service.Initialize(ctx, user.ObjectID.Hex(), user.Name)
		if err != nil {
			log.Printf("Error creating financial sheet for user %s (%s): %v",
				user.Name, user.ObjectID.Hex(), err)
			errorCount++
			continue
		}

		log.Printf("Created financial sheet for user: %s (%s)",
			user.Name, user.ObjectID.Hex())
		successCount++
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor iteration error: %w", err)
	}

	log.Printf("Financial sheets migration completed - Created: %d, Errors: %d (Recreated all sheets)",
		successCount, errorCount)

	if successCount == 0 && errorCount > 0 {
		return fmt.Errorf("migration failed: no financial sheets created and encountered %d errors", errorCount)
	}

	return nil
}

func (m *CreateDefaultFinancialsheetsMigration) Down(ctx context.Context) error {
	// Down migration is intentionally not supported to protect user data
	log.Printf("Down migration not supported for create_default_financialsheets")
	return nil
}
