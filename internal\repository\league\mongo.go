package league

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.LEAGUES_COLLECTION),
	}

	// Create unique index on inviteCode if it's intended to be unique
	// For now, we assume invite codes might not be strictly unique globally
	// or their uniqueness is handled at the service layer / generation time.
	// If a league can have only one active invite code, an index on leagueID + inviteCode might be useful.

	// Example: Index on ownerUserID to quickly find leagues by owner
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "ownerUserID", Value: 1}},
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on leagues.ownerUserID field:", err)
	}

	// Example: Index on members.userID to quickly find leagues a user is part of
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "members.userID", Value: 1}},
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on leagues.members.userID field:", err)
	}

	return repo
}

func (m *mongoDB) Create(ctx context.Context, l *league.League) (string, error) {
	l.ObjectID = primitive.NewObjectID()
	l.ID = l.ObjectID.Hex()
	insertedResult, err := m.collection.InsertOne(ctx, l)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) { // e.g. if we add a unique index on Name
			return "", errors.New(errors.Repository, "league_conflict_exists", errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, "league_create_failed", errors.Internal, err)
	}
	return insertedResult.InsertedID.(primitive.ObjectID).Hex(), nil
}

// Find a specific league by its ID, ensuring the user has access
func (m *mongoDB) Find(ctx context.Context, id primitive.ObjectID) (*league.League, error) {
	var l league.League
	err := m.collection.FindOne(ctx, bson.D{{Key: "_id", Value: id}}).Decode(&l)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "league_not_found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "league_find_failed", errors.Internal, err)
	}
	l.ID = l.ObjectID.Hex()
	return &l, nil
}

func (m *mongoDB) FindAllLeagues(ctx context.Context, userID string) ([]*league.League, error) {
	var leagues []*league.League
	// Find leagues where the userID is in the members array
	// or the userID is the ownerUserID
	filter := bson.M{
		"$or": []bson.M{
			{"members.userID": userID},
			{"ownerUserID": userID},
		},
	}
	cursor, err := m.collection.Find(ctx, filter)
	if err != nil {
		return nil, errors.New(errors.Repository, "league_find_user_leagues_failed", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &leagues); err != nil {
		return nil, errors.New(errors.Repository, "league_find_user_leagues_decode_failed", errors.Internal, err)
	}

	for _, l := range leagues {
		l.ID = l.ObjectID.Hex()
	}
	return leagues, nil
}

func (m *mongoDB) FindByID(ctx context.Context, leagueID string) (*league.League, error) {
	objID, err := primitive.ObjectIDFromHex(leagueID)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid_league_id_format", errors.BadRequest, err)
	}
	return m.Find(ctx, objID)
}

func (m *mongoDB) FindByIDLoggedUser(ctx context.Context, leagueID string, userID string) (*league.League, error) {
	objID, err := primitive.ObjectIDFromHex(leagueID)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid_league_id_format", errors.BadRequest, err)
	}

	// Combine the _id check with the ownership/membership check
	filter := bson.M{
		"_id": objID, // The specific league ID
		"$or": []bson.M{ // And one of these must be true
			{"ownerUserID": userID},
			{"members.userID": userID},
		},
	}

	var l league.League
	err = m.collection.FindOne(ctx, filter).Decode(&l)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "league_not_found_or_access_denied", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "league_find_with_access_failed", errors.Internal, err)
	}

	l.ID = l.ObjectID.Hex()
	return &l, nil
}

func (m *mongoDB) FindByInviteCode(ctx context.Context, code string) (*league.League, error) {
	var l league.League
	// Assuming invite codes are unique enough or the first one found is fine.
	// If multiple leagues can have the same invite code, this logic might need adjustment
	// or a compound index if invite codes are unique per owner or some other criteria.
	err := m.collection.FindOne(ctx, bson.D{{Key: "code", Value: code}}).Decode(&l)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "league_invite_code_not_found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "league_find_by_invite_code_failed", errors.Internal, err)
	}
	l.ID = l.ObjectID.Hex()
	return &l, nil
}

func (m *mongoDB) Update(ctx context.Context, l *league.League) error {
	if l.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(l.ID)
		if err != nil {
			return errors.New(errors.Repository, "invalid_league_id_format_for_update", errors.BadRequest, err)
		}
		l.ObjectID = objID
	}

	opts := options.Update().SetUpsert(false)
	result, err := m.collection.UpdateOne(ctx,
		bson.D{{Key: "_id", Value: l.ObjectID}},
		bson.D{{Key: "$set", Value: l}}, // This replaces the entire document.
		// For specific member updates (like Investidas), a more targeted update would be better
		// e.g., using $set on "members.$[elem].investidas" with arrayFilters.
		// This full document update is simpler for now but less efficient for partial updates.
		opts,
	)
	if err != nil {
		// Handle potential duplicate key errors if unique indexes are violated during update
		return errors.New(errors.Repository, "league_update_failed", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "league_not_found_for_update", errors.NotFound, nil)
	}
	return nil
}

func (m *mongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.collection.DeleteOne(ctx, bson.D{{Key: "_id", Value: id}})
	if err != nil {
		return errors.New(errors.Repository, "league_delete_failed", errors.Internal, err)
	}
	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "league_not_found_for_delete", errors.NotFound, nil)
	}
	return nil
}
