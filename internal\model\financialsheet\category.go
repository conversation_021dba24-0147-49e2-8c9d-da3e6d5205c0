package financialsheet

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Category represents a type of financial transaction
type Category struct {
	ObjectID      primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID            string             `json:"id,omitempty" bson:"-"`
	User          string             `json:"user" bson:"user"`
	Identifier    CategoryIdentifier `json:"identifier" bson:"identifier"`
	Type          CategoryType       `json:"type" bson:"type"`
	Name          string             `json:"name" bson:"name"`
	Icon          CategoryIcon       `json:"icon" bson:"icon"`
	Background    CategoryBackground `json:"background" bson:"background"`
	MoneySource   []MoneySource      `json:"moneySource" bson:"moneySource"`
	PaymentMethod []PaymentMethod    `json:"paymentMethod" bson:"paymentMethod"`
}

// categoryJSON is used for temporary JSON decoding
type categoryJSON struct {
	ObjectID      primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID            string             `json:"id,omitempty" bson:"-"`
	User          string             `json:"user" bson:"user"`
	Identifier    CategoryIdentifier `json:"identifier" bson:"identifier"`
	Type          CategoryType       `json:"type" bson:"type"`
	Name          string             `json:"name" bson:"name"`
	Icon          CategoryIcon       `json:"icon" bson:"icon"`
	Background    CategoryBackground `json:"background" bson:"background"`
	MoneySource   json.RawMessage    `json:"moneySource"`
	PaymentMethod json.RawMessage    `json:"paymentMethod"`
}

// String implements fmt.Stringer
func (c Category) String() string {
	return c.StringValue()
}

// StringValue returns a formatted string representation of the category
func (c Category) StringValue() string {
	var parts []string

	// Add category details
	parts = append(parts, fmt.Sprintf("User: %s", c.User))
	parts = append(parts, fmt.Sprintf("Identifier: %s", c.Identifier))
	parts = append(parts, fmt.Sprintf("Type: %s", c.Type))
	parts = append(parts, fmt.Sprintf("Name: %s", c.Name))

	// Add MoneySource values
	if len(c.MoneySource) > 0 {
		moneySourceStrings := make([]string, len(c.MoneySource))
		for i, ms := range c.MoneySource {
			moneySourceStrings[i] = c.GetMoneySourceInfo(ms).Name
		}
		parts = append(parts, fmt.Sprintf("Money Sources: [%s]", strings.Join(moneySourceStrings, ", ")))
	}

	// Add PaymentMethod values
	if len(c.PaymentMethod) > 0 {
		paymentMethodStrings := make([]string, len(c.PaymentMethod))
		for i, pm := range c.PaymentMethod {
			paymentMethodStrings[i] = c.GetPaymentMethodInfo(pm).Name
		}
		parts = append(parts, fmt.Sprintf("Payment Methods: [%s]", strings.Join(paymentMethodStrings, ", ")))
	}

	return strings.Join(parts, " | ")
}

// MarshalJSON implements json.Marshaler
func (c *Category) MarshalJSON() ([]byte, error) {
	type Alias Category
	type MoneySourceTmp struct {
		Name string       `json:"name"`
		Icon CategoryIcon `json:"icon"`
	}

	type PaymentMethodTmp struct {
		Name string       `json:"name"`
		Icon CategoryIcon `json:"icon"`
	}

	tmp := struct {
		*Alias
		MoneySource   []MoneySourceTmp   `json:"moneySource"`
		PaymentMethod []PaymentMethodTmp `json:"paymentMethod"`
	}{
		Alias: (*Alias)(c),
	}

	tmp.MoneySource = make([]MoneySourceTmp, len(c.MoneySource))
	for i, ms := range c.MoneySource {
		tmp.MoneySource[i].Name = c.GetMoneySourceInfo(ms).Name
		tmp.MoneySource[i].Icon = c.GetMoneySourceInfo(ms).Icon
	}

	tmp.PaymentMethod = make([]PaymentMethodTmp, len(c.PaymentMethod))
	for i, pm := range c.PaymentMethod {
		tmp.PaymentMethod[i].Name = c.GetPaymentMethodInfo(pm).Name
		tmp.PaymentMethod[i].Icon = c.GetPaymentMethodInfo(pm).Icon
	}

	return json.Marshal(&tmp)
}

// UnmarshalJSON implements json.Unmarshaler
func (c *Category) UnmarshalJSON(data []byte) error {
	var temp categoryJSON
	if err := json.Unmarshal(data, &temp); err != nil {
		return err
	}

	c.ObjectID = temp.ObjectID
	c.ID = temp.ID
	c.User = temp.User
	c.Identifier = temp.Identifier
	c.Type = temp.Type
	c.Name = temp.Name
	c.Icon = temp.Icon

	// Handle MoneySource
	var moneySourceStrings []string
	// Ensure it is not empty before unmarshaling
	if len(temp.MoneySource) > 0 {
		if err := json.Unmarshal(temp.MoneySource, &moneySourceStrings); err != nil {
			return err
		}
	}

	c.MoneySource = make([]MoneySource, len(moneySourceStrings))
	for i, s := range moneySourceStrings {
		for ms := MoneySourceOpt1; ms <= MoneySourceOther; ms++ {
			if c.GetMoneySourceInfo(ms).Name == s {
				c.MoneySource[i] = ms
				break
			}
		}
	}

	// Handle PaymentMethod
	var paymentMethodStrings []string
	// Ensure it is not empty before unmarshaling
	if len(temp.PaymentMethod) > 0 {
		if err := json.Unmarshal(temp.PaymentMethod, &paymentMethodStrings); err != nil {
			return err
		}
	}

	c.PaymentMethod = make([]PaymentMethod, len(paymentMethodStrings))
	for i, s := range paymentMethodStrings {
		for pm := PaymentMethodOpt1; pm <= PaymentMethodOther; pm++ {
			if c.GetPaymentMethodInfo(pm).Name == s {
				c.PaymentMethod[i] = pm
				break
			}
		}
	}

	return nil
}

type CategoryInfo struct {
	Identifier CategoryIdentifier `json:"identifier"`
	Name       string             `json:"name"`
	Icon       CategoryIcon       `json:"icon"`
}

// categoryInfoMap is a package-level map for efficient lookups
var categoryInfoMap = map[CategoryIdentifier]CategoryInfo{
	CategoryIdentifierCompensation:       {Compensation.Identifier, Compensation.Name, Compensation.Icon},
	CategoryIdentifierBenefits:           {Benefits.Identifier, Benefits.Name, Benefits.Icon},
	CategoryIdentifierInvestmentIncome:   {InvestmentIncome.Identifier, InvestmentIncome.Name, InvestmentIncome.Icon},
	CategoryIdentifierAdditionalEarnings: {AdditionalEarnings.Identifier, AdditionalEarnings.Name, AdditionalEarnings.Icon},
	CategoryIdentifierPersonalNeeds:      {PersonalNeeds.Identifier, PersonalNeeds.Name, PersonalNeeds.Icon},
	CategoryIdentifierDreams:             {Dreams.Identifier, Dreams.Name, Dreams.Icon},
	CategoryIdentifierDebts:              {Debts.Identifier, Debts.Name, Debts.Icon},
	CategoryIdentifierPersonalReserves:   {PersonalReserves.Identifier, PersonalReserves.Name, PersonalReserves.Icon},
	CategoryIdentifierHousing:            {Housing.Identifier, Housing.Name, Housing.Icon},
	CategoryIdentifierHouseBills:         {HouseBills.Identifier, HouseBills.Name, HouseBills.Icon},
	CategoryIdentifierEducation:          {Education.Identifier, Education.Name, Education.Icon},
	CategoryIdentifierFamily:             {Family.Identifier, Family.Name, Family.Icon},
	CategoryIdentifierCommunication:      {Communication.Identifier, Communication.Name, Communication.Icon},
	CategoryIdentifierTransportation:     {Transportation.Identifier, Transportation.Name, Transportation.Icon},
	CategoryIdentifierHealth:             {Health.Identifier, Health.Name, Health.Icon},
	CategoryIdentifierPersonalCare:       {PersonalCare.Identifier, PersonalCare.Name, PersonalCare.Icon},
	CategoryIdentifierFood:               {Food.Identifier, Food.Name, Food.Icon},
	CategoryIdentifierLeisure:            {Leisure.Identifier, Leisure.Name, Leisure.Icon},
	CategoryIdentifierCasualShopping:     {CasualShopping.Identifier, CasualShopping.Name, CasualShopping.Icon},
}

func (c *Category) GetCategoryInfo() CategoryInfo {
	// Extract base identifier if it's in the format "base_number"
	baseIdentifier := c.Identifier
	parts := strings.Split(string(c.Identifier), "_")
	if len(parts) > 1 {
		// Try to parse the last part as a number
		if _, err := strconv.Atoi(parts[len(parts)-1]); err == nil {
			// If successful, use everything before the last underscore as the base identifier
			baseIdentifier = CategoryIdentifier(strings.Join(parts[:len(parts)-1], "_"))
		}
	}

	// Look up the category info in the map
	if info, exists := categoryInfoMap[baseIdentifier]; exists {
		return info
	}

	// Return undefined category as fallback
	return CategoryInfo{
		Identifier: UndefinedCategory.Identifier,
		Name:       UndefinedCategory.Name,
		Icon:       UndefinedCategory.Icon,
	}
}

// CategoryIdentifier represents the category identifier
type CategoryIdentifier string

const (
	CategoryIdentifierUndefined          CategoryIdentifier = "undefined"
	CategoryIdentifierCompensation       CategoryIdentifier = "compensation"
	CategoryIdentifierBenefits           CategoryIdentifier = "benefits"
	CategoryIdentifierInvestmentIncome   CategoryIdentifier = "investment_income"
	CategoryIdentifierAdditionalEarnings CategoryIdentifier = "additional_earnings"
	CategoryIdentifierPersonalNeeds      CategoryIdentifier = "personal_needs"
	CategoryIdentifierDreams             CategoryIdentifier = "dreams"
	CategoryIdentifierDebts              CategoryIdentifier = "debts"
	CategoryIdentifierPersonalReserves   CategoryIdentifier = "personal_reserves"
	CategoryIdentifierHousing            CategoryIdentifier = "housing"
	CategoryIdentifierHouseBills         CategoryIdentifier = "house_bills"
	CategoryIdentifierEducation          CategoryIdentifier = "education"
	CategoryIdentifierFamily             CategoryIdentifier = "family"
	CategoryIdentifierCommunication      CategoryIdentifier = "communication"
	CategoryIdentifierTransportation     CategoryIdentifier = "transportation"
	CategoryIdentifierHealth             CategoryIdentifier = "health"
	CategoryIdentifierPersonalCare       CategoryIdentifier = "personal_care"
	CategoryIdentifierFood               CategoryIdentifier = "food"
	CategoryIdentifierLeisure            CategoryIdentifier = "leisure"
	CategoryIdentifierCasualShopping     CategoryIdentifier = "casual_shopping"
	CategoryIdentifierNewCategory        CategoryIdentifier = "new_category"

	// Hidden networth tracking categories (not visible to users)
	CategoryIdentifierNetworthStrategicFund CategoryIdentifier = "networth_strategic_fund"
	CategoryIdentifierNetworthInvestments   CategoryIdentifier = "networth_investments"
	CategoryIdentifierNetworthAssets        CategoryIdentifier = "networth_assets"
)

// IsValid validates the category identifier value
func (ci CategoryIdentifier) IsValid() bool {
	// First check if it's one of the predefined identifiers
	switch ci {
	case
		CategoryIdentifierUndefined,
		CategoryIdentifierCompensation,
		CategoryIdentifierBenefits,
		CategoryIdentifierInvestmentIncome,
		CategoryIdentifierAdditionalEarnings,
		CategoryIdentifierPersonalNeeds,
		CategoryIdentifierDreams,
		CategoryIdentifierDebts,
		CategoryIdentifierPersonalReserves,
		CategoryIdentifierHousing,
		CategoryIdentifierHouseBills,
		CategoryIdentifierEducation,
		CategoryIdentifierFamily,
		CategoryIdentifierCommunication,
		CategoryIdentifierTransportation,
		CategoryIdentifierHealth,
		CategoryIdentifierPersonalCare,
		CategoryIdentifierFood,
		CategoryIdentifierLeisure,
		CategoryIdentifierCasualShopping,
		CategoryIdentifierNewCategory,
		// Hidden networth tracking categories
		CategoryIdentifierNetworthStrategicFund,
		CategoryIdentifierNetworthInvestments,
		CategoryIdentifierNetworthAssets:
		return true
	}

	// Then check if it follows the pattern base_number
	parts := strings.Split(string(ci), "_")
	if len(parts) < 2 {
		return false
	}

	// Try to parse the last part as a number
	if _, err := strconv.Atoi(parts[len(parts)-1]); err == nil {
		// If the last part is a number, check if everything before it forms a valid base
		baseIdentifier := CategoryIdentifier(strings.Join(parts[:len(parts)-1], "_"))
		return baseIdentifier.isBaseCategoryNewCategory()
	}

	return false
}

// isBaseCategoryValid checks if the identifier without number is a new category
func (ci CategoryIdentifier) isBaseCategoryNewCategory() bool {
	switch ci {
	case
		CategoryIdentifierNewCategory:
		return true
	default:
		return false
	}
}

func (ci CategoryIdentifier) Validate() error {
	if !ci.IsValid() {
		return errors.New(errors.Model, "invalid category identifier value", errors.Validation, nil)
	}
	return nil
}

// String implements fmt.Stringer
func (ci CategoryIdentifier) String() string {
	return string(ci)
}

// StringValue returns the string representation directly
func (ci CategoryIdentifier) StringValue() string {
	return string(ci)
}

// MarshalJSON implements json.Marshaler
func (ci CategoryIdentifier) MarshalJSON() ([]byte, error) {
	return json.Marshal(string(ci))
}

// UnmarshalJSON implements json.Unmarshaler
func (ci *CategoryIdentifier) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	*ci = CategoryIdentifier(s)
	if !ci.IsValid() {
		return errors.New(errors.Model, "invalid category identifier value", errors.Validation, nil)
	}
	return nil
}

// CategoryType represents the category type
type CategoryType string

const (
	CategoryTypeUndefined        CategoryType = "undefined"
	CategoryTypeIncome           CategoryType = "income"
	CategoryTypeCostsOfLiving    CategoryType = "costs_of_living"
	CategoryTypeExpense          CategoryType = "expense"
	CategoryTypeNetworthTracking CategoryType = "networth_tracking" // Hidden type for networth history
)

// IsValid validates the category type value
func (ct CategoryType) IsValid() bool {
	switch ct {
	case CategoryTypeIncome, CategoryTypeCostsOfLiving, CategoryTypeExpense, CategoryTypeUndefined, CategoryTypeNetworthTracking:
		return true
	default:
		return false
	}
}

func (ct CategoryType) Validate() error {
	if !ct.IsValid() {
		return errors.New(errors.Model, "invalid category type value", errors.Validation, nil)
	}
	return nil
}

// String implements fmt.Stringer
func (ct CategoryType) String() string {
	return string(ct)
}

// StringValue returns the string representation directly
func (ct CategoryType) StringValue() string {
	return string(ct)
}

// MarshalJSON implements json.Marshaler
func (ct CategoryType) MarshalJSON() ([]byte, error) {
	return json.Marshal(string(ct))
}

// UnmarshalJSON implements json.Unmarshaler
func (ct *CategoryType) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	*ct = CategoryType(s)
	if !ct.IsValid() {
		return errors.New(errors.Model, "invalid category type value", errors.Validation, nil)
	}
	return nil
}

// CategoryBackground represents the category background color according to its type
type CategoryBackground string

const (
	CategoryBackgroundUndefined     CategoryBackground = "#808080" // Gray for undefined
	CategoryBackgroundIncome        CategoryBackground = "#C7FFF0" // Green for income
	CategoryBackgroundCostsOfLiving CategoryBackground = "#F6E4FF" // Blue for costs of living
	CategoryBackgroundExpense       CategoryBackground = "#FFD9D9" // Red for expense
)

func (cb CategoryBackground) IsValid() bool {
	switch cb {
	case CategoryBackgroundUndefined, CategoryBackgroundIncome, CategoryBackgroundCostsOfLiving, CategoryBackgroundExpense:
		return true
	default:
		return false
	}
}

func (cb CategoryBackground) Validate() error {
	if !cb.IsValid() {
		return errors.New(errors.Model, "invalid category background value", errors.Validation, nil)
	}
	return nil
}

func (cb CategoryBackground) String() string {
	return string(cb)
}

func (cb CategoryBackground) StringValue() string {
	return string(cb)
}

func (cb CategoryBackground) MarshalJSON() ([]byte, error) {
	return json.Marshal(string(cb))
}

func (cb *CategoryBackground) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	*cb = CategoryBackground(s)
	if !cb.IsValid() {
		return errors.New(errors.Model, "invalid category background value", errors.Validation, nil)
	}
	return nil
}
