# Controllers
The project has many controller.go file. I'm working in the organization of those files.  Use @/internal/controller/user/controller.go  as referece.

- Do not change any business logic or try to fix anything I'm just want to organize the files. 
- Do not change anything user controller.go
- Make the comments simpler. Prefer "// Utility" instead of "// Utility functions" or "// Utility operations".
- Utility function is a function starting with a capital Letter (public) a Helper function is a function starting with small case (private).

1. Grouped the interface methods by functionality (CRUD, Sync, Initialize)
2. CRUD methods first
3. Placed interface definition at the top
4. Followed by struct definition and constructor
5. Followed by New()
6. Organized implementation methods in the same groups as the interface
7. Placed helper methods at the bottom
8. Admin Methods are last after helper methods.
9. Admin Methods are the ones with the "middlewares.AdminGuard()" in their routes you can check "RegisterRoutes" to know that.
10. Routes in the RegisterRoutes function should be in the same order of the controller interface.
11. Clear separation between operations

Organize all controller.go files.

# Services
The project has many service.go file. I'm working in the organization of those files.  Use @/internal/service/user/service.go  as referece.

- Do not change any business logic or try to fix anything I'm just want to organize the files. 
- Do not change anything on user service.go
- Make the comments simpler. Prefer "// Utility" instead of "// Utility functions" or "// Utility operations".
- Utility function is a function starting with a capital Letter (public) a Helper function is a function starting with small case (private).

1. Grouped the interface methods by functionality (Sync operations, CRUD)
2. CRUD methods first
3. Placed interface definition at the top
4. Followed by struct definition and constructor
5. Followed by New()
6. Organized implementation methods in the same groups as the interface
7. Placed helper methods at the bottom
8. Admin Methods are last after helper methods.
9. Clear separation between operations

Organize all service.go files.