# Project Guidelines

## Code Style & Patterns

Best practices for REST API (controller package)
- Use nouns instead of verbs in endpoint paths
- Name collections with plural nouns
- Handle errors gracefully and return standard error codes
- Maintain Good Security Practices

Error handling pattern defined in:

- internal/api/errors/domain.go
- internal/api/errors/rest.go
- internal/api/errors/types.go