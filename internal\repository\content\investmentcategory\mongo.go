package investmentcategory

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.INVESTMENTS_CATEGORIES_COLLECTION),
	}

	// Create unique index on user field
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "identifier", Value: 1}},
			Options: options.Index().SetUnique(true).SetName("identifier"),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on investment category.identifier field")
		db.Client().Disconnect(context.Background())
	}

	return repo
}

func (m mongoDB) Create(ctx context.Context, investmentCategory *content.InvestmentCategory) error {
	_, err := m.collection.InsertOne(ctx, investmentCategory)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "investment category already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create investment category", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) Find(ctx context.Context, id string) (*content.InvestmentCategory, error) {
	objectId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid ID format", errors.Validation, err)
	}

	var investmentCategory content.InvestmentCategory
	if err = m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectId}}).Decode(&investmentCategory); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "investment category not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "find investment category failed", errors.Internal, err)
	}

	return &investmentCategory, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*content.InvestmentCategory, error) {
	cursor, err := m.collection.Find(ctx, bson.D{}, nil)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query investment categories", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var investmentCategories []*content.InvestmentCategory
	for cursor.Next(ctx) {
		var investmentCategory content.InvestmentCategory
		if err = cursor.Decode(&investmentCategory); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode investment category", errors.Internal, err)
		}
		investmentCategories = append(investmentCategories, &investmentCategory)
	}

	return investmentCategories, nil
}

func (m mongoDB) FindByIdentifier(ctx context.Context, identifier string) (*content.InvestmentCategory, error) {
	var investmentCategory content.InvestmentCategory
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "identifier", Value: identifier}}).Decode(&investmentCategory); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "investment category not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "find investment category by identifier failed", errors.Internal, err)
	}

	return &investmentCategory, nil
}

func (m mongoDB) Update(ctx context.Context, investmentCategory *content.InvestmentCategory) error {
	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: investmentCategory.ObjectID}},
		primitive.M{"$set": investmentCategory})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "investment category update would cause conflict", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update investment category", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "investment category not found for update", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid ID format", errors.Validation, err)
	}

	result, err := m.collection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectID}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete investment category", errors.Internal, err)
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "investment category not found for deletion", errors.NotFound, nil)
	}

	return nil
}
