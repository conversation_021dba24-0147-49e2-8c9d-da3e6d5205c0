package gamification

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialdna"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Descobriu o DNA implementation
func (s *service) CheckDNAAchievement(ctx context.Context, userID string) error {
	// Step 1: Check if user already has DNA achievement (fail-fast)
	hasAchievement, err := s.Repository.HasAchievement(ctx, userID, dnaAchievementIdentifier)
	if err != nil {
		return errors.New(errors.Service, "failed to check if user has dna achievement", errors.Internal, err)
	}
	if hasAchievement {
		// User already has the achievement, no need to check further
		return nil
	}

	// Step 2: Check if user completed "dna-financeiro" challenge (fail-fast)
	hasCompletedChallenge, err := s.hasCompletedDNAFinanceiroChallenge(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check dna-financeiro challenge completion", errors.Internal, err)
	}
	if !hasCompletedChallenge {
		// Challenge not completed, user doesn't qualify for DNA achievement
		return nil
	}

	// Step 3: Check if all family members have financial status (fail-fast)
	hasCompletedDNA, err := s.hasCompletedFinancialDNA(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check financial dna completion", errors.Internal, err)
	}
	if !hasCompletedDNA {
		// DNA not completed, user doesn't qualify for DNA achievement
		return nil
	}

	// Step 4: All checks passed, award the achievement
	return s.awardAchievement(ctx, userID, dnaAchievementIdentifier)
}

// Helper methods to award an achievement
func (s *service) hasCompletedPerfilFinanceiroChallenge(ctx context.Context, userID string) (bool, error) {
	progression, err := s.ProgressionRepo.FindByUser(ctx, userID)
	if err != nil {
		return false, err
	}

	// Look for "perfil-financeiro" phase completion in any trail
	for _, trail := range progression.Trails {
		if trail.Challenge != nil {
			for _, phase := range trail.Challenge.Phases {
				if phase.Identifier == dnaChallengePhase001Identifier && phase.Completed {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

func (s *service) hasCompletedDNAFinanceiroChallenge(ctx context.Context, userID string) (bool, error) {
	progression, err := s.ProgressionRepo.FindByUser(ctx, userID)
	if err != nil {
		return false, err
	}

	// Look for "dna-financeiro" phase completion in any trail
	for _, trail := range progression.Trails {
		if trail.Challenge != nil {
			for _, phase := range trail.Challenge.Phases {
				if phase.Identifier == dnaChallengePhase002Identifier && phase.Completed {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

func (s *service) hasCompletedFinancialDNA(ctx context.Context, userID string) (bool, error) {
	// Convert userID string to ObjectID
	userObjectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return false, err
	}

	financialDNA, err := s.FinancialDNARepo.FindByUser(ctx, userObjectID)
	if err != nil {
		return false, err
	}

	// Populate the members ID
	for _, member := range financialDNA.Members {
		if member != nil && !member.ObjectID.IsZero() {
			member.ID = member.ObjectID.Hex()
		}
	}

	// Get the user ID in the tree
	userIDInTree := financialDNA.Members[0].ID

	// Check if all family members have financial status populated
	// Exclude children and grandchildren from the check as per requirements
	for _, member := range financialDNA.Members {
		// Check if financial status is populated (not undefined)
		if member.FinancialStatus == financialdna.FinancialStatusUndefined || member.FinancialStatus == "" {
			// Check if this is a parent/grandparent (no parent IDs) or if it's a required member
			// For now, we'll check all members except those explicitly marked as children
			if !s.isUserDescendant(userIDInTree, financialDNA.Members, member) {
				return false, nil
			}
		}
	}

	return true, nil
}

// isUserDescendant checks if memberToCheck is a descendant (child, grandchild, etc.) of the user with userID.
func (s *service) isUserDescendant(userID string, members []*financialdna.FamilyMember, memberToCheck *financialdna.FamilyMember) bool {
	// --- 1. Basic checks and data preparation ---
	if userID == "" || memberToCheck == nil || len(members) == 0 {
		return false
	}

	// For efficiency, convert the slice of members into a map for quick lookups by ID.
	// This avoids nested loops and is much faster (O(1) lookup vs O(N)).
	membersMap := make(map[string]*financialdna.FamilyMember, len(members))
	for _, m := range members {
		membersMap[m.ID] = m
	}

	// If the member to check doesn't exist in the family, it can't be a descendant.
	if _, ok := membersMap[memberToCheck.ID]; !ok {
		return false
	}

	// --- 2. The Traversal Logic (BFS) ---

	// queue holds the IDs of members we need to check the children of.
	// We start with the direct children of the user.
	user, ok := membersMap[userID]
	if !ok {
		// The user themselves is not in the list of members.
		return false
	}

	queue := make([]string, 0, len(user.ChildrenIDs))
	queue = append(queue, user.ChildrenIDs...)

	// visited prevents us from checking the same person twice (and avoids infinite loops in case of bad data).
	visited := make(map[string]bool)
	for _, id := range queue {
		visited[id] = true
	}

	// Process the queue until it's empty.
	for len(queue) > 0 {
		// Dequeue the next member ID to check
		currentMemberID := queue[0]
		queue = queue[1:]

		// Is this the person we're looking for?
		if currentMemberID == memberToCheck.ID {
			return true
		}

		// It's not them, so let's add their children to the queue to check next.
		if person, ok := membersMap[currentMemberID]; ok {
			for _, childID := range person.ChildrenIDs {
				if !visited[childID] {
					visited[childID] = true
					queue = append(queue, childID)
				}
			}
		}
	}

	// If we've checked all descendants and haven't found the member, return false.
	return false
}
