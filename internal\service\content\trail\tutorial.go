package trail

import (
	"context"
	"fmt"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
)

// Tutorial CRUD

func (s *service) CreateTutorial(ctx context.Context, tutorial *content.Tutorial) error {
	identifierKey := tutorialIdentifierKey(tutorial.Identifier)
	if _, found := s.Cache.Get(ctx, identifierKey); found {
		return errors.OldError("tutorial content already exists (cache hit)", errors.Conflict, nil)
	}

	foundContent, err := s.Repository.FindTutorialByIdentifier(ctx, tutorial.Identifier)
	if err == nil && foundContent != nil {
		foundContent.ID = foundContent.ObjectID.Hex()
		idKey := tutorialIDKey(foundContent.ID)
		_ = s.Cache.Set(ctx, identifierKey, foundContent, 0)
		_ = s.Cache.Set(ctx, idKey, foundContent, 0)
		return errors.OldError("tutorial content already exists (db hit)", errors.Conflict, err)
	}

	if err = s.Repository.CreateTutorial(ctx, tutorial); err != nil {
		return err
	}

	// Invalidate all tutorials cache
	err = s.Cache.Delete(ctx, tutorialAllKey)
	if err != nil {
		log.Printf("Error invalidating FindAllTutorials cache after create: %v", err)
	} else {
		log.Println("Tutorials FindAll cache invalidated due to Create.")
	}

	return nil
}

func (s *service) FindTutorial(ctx context.Context, id string) (*content.Tutorial, error) {
	cacheKey := tutorialIDKey(id)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if tutorial, ok := cached.(*content.Tutorial); ok {
			log.Printf("Cache hit for FindTutorial: %s", id)
			return tutorial, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to *content.Tutorial failed", cacheKey)
	}

	log.Printf("Cache miss for FindTutorial: %s. Fetching from database...", id)
	foundTutorial, err := s.Repository.FindTutorial(ctx, id)
	if err != nil {
		return nil, err
	}
	foundTutorial.ID = foundTutorial.ObjectID.Hex()

	errSet := s.Cache.Set(ctx, cacheKey, foundTutorial, 0)
	if errSet != nil {
		log.Printf("Error populating cache for tutorial ID %s: %v", id, errSet)
	} else {
		log.Printf("Cache populated for tutorial ID: %s", id)
	}

	return foundTutorial, nil
}

func (s *service) FindAllTutorials(ctx context.Context) ([]*content.Tutorial, error) {
	cacheKey := tutorialAllKey

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if tutorials, ok := cached.([]*content.Tutorial); ok {
			log.Println("Cache hit for FindAllTutorials.")
			return tutorials, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to []*content.Tutorial failed", cacheKey)
	}

	log.Println("Cache miss for FindAllTutorials. Fetching from database...")
	foundTutorials, err := s.Repository.FindAllTutorials(ctx)
	if err != nil {
		return nil, err
	}

	processedTutorials := make([]*content.Tutorial, len(foundTutorials))
	for i, foundTutorial := range foundTutorials {
		foundTutorial.ID = foundTutorial.ObjectID.Hex()
		tutorialCopy := *foundTutorial
		processedTutorials[i] = &tutorialCopy
	}

	errSet := s.Cache.Set(ctx, cacheKey, processedTutorials, 0)
	if errSet != nil {
		log.Printf("Error populating FindAllTutorials cache: %v", errSet)
	} else {
		log.Printf("FindAllTutorials Cache populated with %d tutorials.", len(processedTutorials))
	}

	return processedTutorials, nil
}

func (s *service) FindTutorialByIdentifier(ctx context.Context, identifier string) (*content.Tutorial, error) {
	cacheKey := tutorialIdentifierKey(identifier)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if tutorial, ok := cached.(*content.Tutorial); ok {
			log.Printf("Cache hit for FindTutorialByIdentifier: %s", identifier)
			return tutorial, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to *content.Tutorial failed", cacheKey)
	}

	log.Printf("Cache miss for FindTutorialByIdentifier: %s. Fetching from database...", identifier)
	foundTutorial, err := s.Repository.FindTutorialByIdentifier(ctx, identifier)
	if err != nil {
		return nil, err
	}
	foundTutorial.ID = foundTutorial.ObjectID.Hex()

	// Cache by both identifier and ID
	idKey := tutorialIDKey(foundTutorial.ID)
	errSetIdentifier := s.Cache.Set(ctx, cacheKey, foundTutorial, 0)
	errSetID := s.Cache.Set(ctx, idKey, foundTutorial, 0)

	if errSetIdentifier != nil || errSetID != nil {
		log.Printf("Error populating cache for tutorial identifier %s: identifier_err=%v, id_err=%v", identifier, errSetIdentifier, errSetID)
	} else {
		log.Printf("Cache populated for tutorial identifier: %s (ID: %s)", identifier, foundTutorial.ID)
	}

	return foundTutorial, nil
}

func (s *service) FindTutorialByTicker(ctx context.Context, ticker string) (*content.Tutorial, error) {
	cacheKey := tutorialTickerKey(ticker)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if tutorial, ok := cached.(*content.Tutorial); ok {
			log.Printf("Cache hit for FindTutorialByTicker: %s", ticker)
			return tutorial, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to *content.Tutorial failed", cacheKey)
	}

	log.Printf("Cache miss for FindTutorialByTicker: %s. Fetching from database...", ticker)
	foundTutorial, err := s.Repository.FindTutorialByTicker(ctx, ticker)
	if err != nil {
		return nil, err
	}
	foundTutorial.ID = foundTutorial.ObjectID.Hex()

	// Cache by ticker, identifier, and ID
	identifierKey := tutorialIdentifierKey(foundTutorial.Identifier)
	idKey := tutorialIDKey(foundTutorial.ID)
	errSetTicker := s.Cache.Set(ctx, cacheKey, foundTutorial, 0)
	errSetIdentifier := s.Cache.Set(ctx, identifierKey, foundTutorial, 0)
	errSetID := s.Cache.Set(ctx, idKey, foundTutorial, 0)

	if errSetTicker != nil || errSetIdentifier != nil || errSetID != nil {
		log.Printf("Error populating cache for tutorial ticker %s: ticker_err=%v, identifier_err=%v, id_err=%v", ticker, errSetTicker, errSetIdentifier, errSetID)
	} else {
		log.Printf("Cache populated for tutorial ticker: %s (ID: %s, Identifier: %s)", ticker, foundTutorial.ID, foundTutorial.Identifier)
	}

	return foundTutorial, nil
}

func (s *service) UpdateTutorial(ctx context.Context, tutorial *content.Tutorial) error {
	err := s.Repository.UpdateTutorial(ctx, tutorial)
	if err != nil {
		return err
	}

	hexID := tutorial.ObjectID.Hex()
	s.invalidateTutorialCache(ctx, hexID, tutorial.Ticker)

	return nil
}

func (s *service) DeleteTutorial(ctx context.Context, id string) error {
	var tickerToInvalidate string
	cacheKeyID := tutorialIDKey(id)

	if cached, found := s.Cache.Get(ctx, cacheKeyID); found {
		if tutorial, ok := cached.(*content.Tutorial); ok {
			tickerToInvalidate = tutorial.Ticker
		}
	}

	if tickerToInvalidate == "" {
		log.Printf("Tutorial ID %s not in cache before delete, fetching for invalidation info.", id)
		dbTutorial, err := s.Repository.FindTutorial(ctx, id)

		if err != nil {
			if domainErr, ok := err.(*errors.DomainError); ok {
				if domainErr.Kind() != errors.NotFound {
					log.Printf("Error fetching tutorial %s before delete: %v", id, err)
				}
			} else {
				log.Printf("Unexpected error type fetching tutorial %s before delete: %v", id, err)
			}
		}

		if dbTutorial != nil {
			tickerToInvalidate = dbTutorial.Ticker
		}
	}

	err := s.Repository.DeleteTutorial(ctx, id)
	if err != nil {
		return err
	}

	if tickerToInvalidate == "" {
		log.Printf("Warning: Could not determine ticker for deleted tutorial ID %s for full cache invalidation.", id)
	}
	s.invalidateTutorialCache(ctx, id, tickerToInvalidate)

	return nil
}

// Tutorial Lesson CRUD

func (s *service) CreateTutorialLesson(ctx context.Context, tutorialID string, lesson *content.Lesson) error {
	tutorial, err := s.FindTutorial(ctx, tutorialID)
	if err != nil {
		return err
	}

	tutorial.Lessons = append(tutorial.Lessons, lesson)

	return s.UpdateTutorial(ctx, tutorial)
}

func (s *service) FindTutorialLesson(ctx context.Context, tutorialID string, lessonID string) (*content.Lesson, error) {
	tutorial, err := s.FindTutorial(ctx, tutorialID)
	if err != nil {
		return nil, err
	}

	for _, lesson := range tutorial.Lessons {
		if lesson != nil && lesson.Identifier == lessonID {
			return lesson, nil
		}
	}

	return nil, errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
}

func (s *service) UpdateTutorialLesson(ctx context.Context, tutorialID string, lessonID string, newLesson *content.Lesson) error {
	tutorial, err := s.FindTutorial(ctx, tutorialID)
	if err != nil {
		return err
	}

	found := false
	for i, lesson := range tutorial.Lessons {
		if lesson != nil && lesson.Identifier == lessonID {
			tutorial.Lessons[i] = newLesson
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
	}

	return s.UpdateTutorial(ctx, tutorial)
}

func (s *service) DeleteTutorialLesson(ctx context.Context, tutorialID string, lessonID string) error {
	tutorial, err := s.FindTutorial(ctx, tutorialID)
	if err != nil {
		return err
	}

	found := false
	lessonIndex := -1
	for i, l := range tutorial.Lessons {
		if l != nil && l.Identifier == lessonID {
			lessonIndex = i
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
	}

	tutorial.Lessons = append(tutorial.Lessons[:lessonIndex], tutorial.Lessons[lessonIndex+1:]...)

	return s.UpdateTutorial(ctx, tutorial)
}

// Cache Key Generation Helpers for Tutorial
func tutorialIDKey(id string) string {
	return fmt.Sprintf("tutorial:id:%s", id)
}

func tutorialIdentifierKey(identifier string) string {
	return fmt.Sprintf("tutorial:identifier:%s", identifier)
}

func tutorialTickerKey(ticker string) string {
	return fmt.Sprintf("tutorial:ticker:%s", ticker)
}

const tutorialAllKey = "tutorial:all"

// Helper function to invalidate tutorial caches
func (s *service) invalidateTutorialCache(ctx context.Context, id string, ticker string) {
	// Invalidate all tutorials cache
	errAll := s.Cache.Delete(ctx, tutorialAllKey)
	if errAll != nil {
		log.Printf("Error invalidating all tutorials cache: %v", errAll)
	} else {
		log.Println("All tutorials cache invalidated.")
	}

	// Invalidate specific tutorial caches
	idKey := tutorialIDKey(id)
	errID := s.Cache.Delete(ctx, idKey)
	if errID != nil {
		log.Printf("Error invalidating tutorial cache for ID %s: %v", id, errID)
	} else {
		log.Printf("Tutorial cache invalidated for ID: %s", id)
	}

	if ticker != "" {
		tickerKey := tutorialTickerKey(ticker)
		errTicker := s.Cache.Delete(ctx, tickerKey)
		if errTicker != nil {
			log.Printf("Error invalidating tutorial cache for Ticker %s: %v", ticker, errTicker)
		} else {
			log.Printf("Tutorial cache invalidated for Ticker: %s", ticker)
		}
	}

	// Note: We can't easily invalidate identifier cache without knowing the identifier
	// This could be improved by fetching the tutorial before deletion or keeping track of cache keys
}
