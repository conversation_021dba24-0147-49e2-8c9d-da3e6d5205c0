package progression

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Card interface {
	ToCard() Card
}

type Progression struct {
	ObjectID  primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID        string             `json:"id,omitempty" bson:"-"`
	User      string             `json:"user" bson:"user"`
	Trails    []*Trail           `json:"trails" bson:"trails"`
	CreatedAt time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time          `json:"updatedAt" bson:"updatedAt"`
}

type ProgressionBody struct {
	Trail     string               `json:"trail"`
	Module    string               `json:"module"`
	Content   string               `json:"content"`
	Type      string               `json:"type"`
	Choice    *ModuleContentChoice `json:"choice"`
	Timestamp time.Time            `json:"timestamp"`
}

func (body *ProgressionBody) Validate() error {
	if body.Trail == "" {
		return ErrRequiredTrailValue
	}

	if body.Module == "" {
		return ErrRequiredLessonValue
	}

	if body.Type == "" {
		return ErrRequiredTypeValue
	}

	if body.Type != string(ProgressionTypeLesson) && body.Type != string(ProgressionTypeChallenge) {
		return ErrTypeMustBeLessonOrChallenge
	}

	if body.Choice == nil {
		return ErrRequiredChoiceValue
	}

	if body.Choice.Identifier == "" {
		return ErrRequiredChoiceIdentifierValue
	}

	if body.Choice.Next == "" {
		return ErrRequiredChoiceNextValue
	}

	return nil
}

func (p *Progression) checkIsFirstProgress(body *ProgressionBody, trailContent *content.Trail) error {
	if len(p.Trails) == 0 {
		if body.Type == string(ProgressionTypeChallenge) {
			return logAndReturnError("User tried a challenge without finishing all lessons (challenge jump)", ErrInvalidChallengeProgress)
		}
		// If this is the first progress record but not the first lesson, it's invalid
		if !isFirstLesson(body.Module, trailContent) {
			return logAndReturnError("User tried a lesson without requirements (lesson jump)", ErrInvalidLessonProgress)
		}

		// If the trail has requirements, the user shouldn't have progress in it yet
		if len(trailContent.Requirements) > 0 {
			return logAndReturnError("User tried an invalid trail with requirements (trail jump)", ErrInvalidTrailProgress)
		}
	}

	return nil
}

func (p *Progression) checkTrailRequirements(trailContent *content.Trail) error {
	if len(trailContent.Requirements) > 0 {
		for _, requirement := range trailContent.Requirements {
			// Check prerequisites: Trail 1, Trail 2 (requires 1), Trail 3 (requires 1, 2)
			requirementProgression := p.GetTrailProgression(requirement)
			if requirementProgression == nil || requirementProgression.Total < 90 {
				return logAndReturnErrorWithContext("User tried an invalid trail with requirements",
					ErrInvalidTrailProgress,
					map[string]interface{}{
						"trail":       trailContent.ID,
						"requirement": requirement,
					})
			}
		}
	}

	return nil
}

func (p *Progression) checkLessonRequirements(body *ProgressionBody, lessonContent *content.Lesson) error {
	if lessonContent != nil {
		if len(lessonContent.Requirements) > 0 {
			for _, requirement := range lessonContent.Requirements {
				lessonProgression := p.GetLessonProgression(body.Trail, requirement)
				if lessonProgression == nil || !lessonProgression.Completed {
					return logAndReturnErrorWithContext("User tried a lesson without completing requirements",
						ErrInvalidLessonProgress,
						map[string]interface{}{
							"trail":       body.Trail,
							"lesson":      body.Module,
							"requirement": requirement,
						})
				}
			}
		}
	} else {
		return ErrInvalidLesson
	}

	return nil
}

func (p *Progression) checkLessonNextProgress(body *ProgressionBody, lessonContent *content.Lesson, lessonProgression *Lesson) error {
	lessonContentItem := lessonContent.GetContent(body.Content)
	if lessonContentItem == nil {
		return ErrInvalidContent
	}

	isValidChoice := isLessonContentChoice(lessonContentItem.Choices, body.Choice.Identifier)

	if lessonProgression.Current != body.Choice.Identifier && !isValidChoice && !lessonProgression.ExistsContent(body.Choice.Identifier) {
		return logAndReturnErrorWithContext("User tried a lesson content without completing prerequisites",
			ErrInvalidContentProgress,
			map[string]interface{}{
				"trail":   body.Trail,
				"lesson":  body.Module,
				"content": body.Content,
				"current": lessonProgression.Current,
				"choice":  body.Choice.Identifier,
			})
	}

	expectedNext := lessonContent.GetNext(body.Content, body.Choice.Identifier)
	if expectedNext != body.Choice.Next {
		return logAndReturnErrorWithContext("User tried an invalid next content",
			ErrInvalidContentProgress,
			map[string]interface{}{
				"trail":         body.Trail,
				"lesson":        body.Module,
				"content":       body.Content,
				"choice":        body.Choice.Identifier,
				"expected_next": expectedNext,
				"actual_next":   body.Choice.Next,
			})
	}

	return nil
}

func (p *Progression) checkLessonsCompleted(challengeContent *content.Challenge, trailProgression *Trail) error {
	if challengeContent != nil {
		if !trailProgression.LessonsCompleted {
			return logAndReturnErrorWithContext("User tried a challenge without finishing all lessons",
				ErrInvalidChallengeProgress,
				map[string]interface{}{
					"trail":             trailProgression.ID,
					"challenge":         challengeContent.Identifier,
					"lessons_completed": trailProgression.LessonsCompleted,
				})
		}
	} else {
		return ErrInvalidChallenge
	}

	return nil
}

func (p *Progression) ValidateUserProgression(body *ProgressionBody, trailContent *content.Trail) error {
	// Check if this is the first progress record
	if err := p.checkIsFirstProgress(body, trailContent); err != nil {
		return err
	}

	// Check if the user has completed 90% of the prerequisite trails
	if err := p.checkTrailRequirements(trailContent); err != nil {
		return err
	}

	if body.Type == string(ProgressionTypeLesson) {
		lessonContent := trailContent.GetLesson(body.Module)
		lessonProgression := p.GetLessonProgression(body.Trail, body.Module)

		if err := p.checkLessonRequirements(body, lessonContent); err != nil {
			return err
		}

		if lessonProgression != nil {
			if err := p.checkLessonNextProgress(body, lessonContent, lessonProgression); err != nil {
				return err
			}
		} else if !isFirstContent(lessonContent, body.Choice.Identifier) {
			return logAndReturnErrorWithContext("User tried to start with non-first content in lesson",
				ErrInvalidContentProgress,
				map[string]interface{}{
					"trail":   body.Trail,
					"lesson":  body.Module,
					"content": body.Content,
					"choice":  body.Choice.Identifier,
				})
		}
	} else if body.Type == string(ProgressionTypeChallenge) {
		challengeContent := trailContent.Challenge
		trailProgression := p.GetTrailProgression(body.Trail)
		challengeProgression := trailProgression.Challenge

		if err := p.checkLessonsCompleted(challengeContent, trailProgression); err != nil {
			return err
		}

		if challengeProgression != nil {
			if err := challengeProgression.ValidateChallengeProgression(body, challengeContent, trailContent, p); err != nil {
				return err
			}
			// } else if !IsFirstPhaseOfChallenge(trailContent.Challenge, body.Module) {
			// 	return logAndReturnErrorWithContext("User tried to start with non-first phase in challenge",
			// 		ErrInvalidChallengePhaseProgress,
			// 		map[string]interface{}{
			// 			"trail":     body.Trail,
			// 			"challenge": trailContent.Challenge.Identifier,
			// 			"phase":     body.Module,
			// 		})
		}
	}

	return nil
}

func isLessonContentChoice(choices []*content.LessonChoice, target string) bool {
	if len(choices) > 0 {
		for _, choice := range choices {
			if isLessonContentChoice(choice.Choices, target) {
				return true
			}

			if choice.Identifier == target {
				return true
			}
		}
	}

	return false
}

func isChallengeContentChoice(choices []*content.ChallengeChoice, target string) bool {
	if len(choices) > 0 {
		for _, choice := range choices {
			if isChallengeContentChoice(choice.Choices, target) {
				return true
			}

			if choice.Identifier == target {
				return true
			}
		}
	}

	return false
}
func (p *Progression) GetChallengePhaseProgression(trailId string, phaseId string) *ChallengePhase {
	for _, trail := range p.Trails {
		if trail.ID == trailId {
			challenge := trail.Challenge
			if challenge != nil {
				return challenge.GetPhase(phaseId)
			}
		}
	}

	return nil
}

func (p *Progression) GetLessonProgression(trailId string, lessonIdentifier string) *Lesson {
	for _, trail := range p.Trails {
		if trail.ID == trailId {
			for _, lesson := range trail.Lessons {
				if lesson.Identifier == lessonIdentifier {
					return lesson
				}
			}
		}
	}

	return nil
}

func (p *Progression) GetTrailProgression(id string) *Trail {
	for _, trail := range p.Trails {
		if trail.ID == id {
			return trail
		}
	}

	return nil
}

func isFirstLesson(lesson string, trailContent *content.Trail) bool {
	for idx, lessonContent := range trailContent.Lessons {
		if lessonContent.Identifier == lesson {
			return idx == 0
		}
	}

	return false
}

func isFirstContent(lesson *content.Lesson, identifier string) bool {
	for idx, lessonContent := range lesson.Content {
		if lessonContent.Identifier == identifier {
			return idx == 0
		}

		if len(lessonContent.Choices) > 0 {
			if isLessonContentChoice(lessonContent.Choices, identifier) {
				return idx == 0
			}
		}
	}

	return false
}

func IsFirstChallengePhaseContent(phase *content.ChallengePhase, identifier string) bool {
	for idx, phaseContent := range phase.Content {
		if phaseContent.Identifier == identifier {
			return idx == 0
		}
	}

	return false
}

func IsFirstPhaseOfChallenge(challenge *content.Challenge, identifier string) bool {
	for idx, challengePhase := range challenge.Phases {
		if challengePhase.Identifier == identifier {
			return idx == 0
		}
	}

	return false
}

func (p *Progression) UpdateTrailTotal(trailContent *content.Trail) {
	for _, trail := range p.Trails {
		if trail.ID == trailContent.ID {
			sum := 0
			allCompleted := false
			for _, lesson := range trail.Lessons {
				if lesson.Completed {
					sum++
				}
			}
			division := float64(sum) / float64(len(trailContent.Lessons))
			trail.Total = uint8(division * 100)

			if trail.Total >= 100 {
				allCompleted = true

				// Changed to trail.Challenge.Completed ajust and verify in the future
				//if !trail.ChallengeCompleted {
				// Check if the challenge is not null before check its objects
				if trail.Challenge == nil {
					trail.Total = 90
				} else if !trail.Challenge.Completed {
					trail.Total = 90
				} else {
					trail.Total = 100
				}
			}
			trail.LessonsCompleted = allCompleted
		}
	}
}

func (p *Progression) UpdateChallengeTotal(trailContent *content.Trail) {
	for _, trail := range p.Trails {
		if trail.ID == trailContent.ID {
			challengeContent := trailContent.Challenge
			sum := 0

			// In the past we iterate through the trail of a user, this is buggy if we added/removed challenge.
			//for id, phase := range trail.Challenge.Phases {
			// Changed to iterate only to the available content.
			for _, phase := range trail.Challenge.Phases {
				if challengeContent.GetPhase(phase.Identifier) != nil {
					if phase.Completed {
						sum++
					}
				}
			}

			var division float64
			if len(challengeContent.Phases) > 0 {
				division = float64(sum) / float64(len(challengeContent.Phases))
			}
			trail.Challenge.Total = uint8(division * 100)
			isComplete := trail.Challenge.Total >= 100

			// Set BOTH completion flags based on this single, reliable calculation.
			trail.Challenge.Completed = isComplete
			trail.ChallengeCompleted = isComplete // This now gets the correct value
		}
	}
}

func (p *Progression) UpdateLessonStatus(body *ProgressionBody, lessonContent *content.Lesson) {
	for _, lessonPath := range lessonContent.Content {
		if lessonPath.Identifier == body.Choice.Identifier &&
			(lessonPath.Next == string(RewardTypeCoin)) {
			lessonProgression := p.GetLessonProgression(body.Trail, body.Module)
			if lessonProgression != nil {
				lessonProgression.Completed = true
			}
		}
	}
}

func (p *Progression) UpdateChallengeStatus(body *ProgressionBody, challengeContent *content.Challenge) {
	phase := challengeContent.GetPhase(body.Module)
	if phase == nil {
		return
	}

	// Check if the content exists in the phase
	challengePath := phase.GetContent(body.Content)
	if challengePath == nil {
		return
	}

	// Update points from choice
	p.UpdateChallengePoints(body, challengeContent)

	// Check if the next value from the choice is a reward
	if body.Choice.Next == string(RewardTypeCoin) {
		challengePhase := p.GetChallengePhaseProgression(body.Trail, body.Module)
		if challengePhase != nil {
			challengePhase.Completed = true
			challengePhase.Rewarded = true
		}
	}
}

func (p *Progression) UpdateChallengePoints(body *ProgressionBody, challengeContent *content.Challenge) {
	phase := challengeContent.GetPhase(body.Module)
	if phase == nil {
		return
	}

	// Get points from the choice
	points := phase.GetPoints(body.Content, body.Choice.Identifier)
	if points > 0 {
		// Find the trail and add points
		for _, trail := range p.Trails {
			if trail.ID == body.Trail {
				// Find the specific challenge phase and add points
				challengePhase := p.GetChallengePhaseProgression(body.Trail, body.Module)
				if challengePhase != nil {
					challengePhase.CurrentPoints += points
				}
				break
			}
		}
	}
}

func (p *Progression) Register(body *ProgressionBody, trailContent *content.Trail) {
	if body.Type == string(ProgressionTypeLesson) {
		p.registerLessonProgress(body, trailContent)
	} else if body.Type == string(ProgressionTypeChallenge) {
		p.registerChallengeProgress(body, trailContent)
	}
}

func (p *Progression) GetChallengeProgression(trailId string) *Challenge {
	for _, trail := range p.Trails {
		if trail.ID == trailId {
			return trail.Challenge
		}
	}
	return nil
}
