package user

import (
	"context"
	"net/http"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/s3"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
)

type AdminController interface {
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	Create() echo.HandlerFunc
	Find() echo.HandlerFunc
	FindAll() echo.HandlerFunc
	Update() echo.HandlerFunc
	Patch() echo.HandlerFunc
	Delete() echo.HandlerFunc

	// Card CRUD
	FindCard() echo.HandlerFunc
}

type adminController struct {
	Service   user.Service
	S3Service s3.Service
}

func NewAdmin(service user.Service, s3Service s3.Service) AdminController {
	return &adminController{
		Service:   service,
		S3Service: s3Service,
	}
}

func (ac *adminController) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	adminGroup := currentGroup.Group("/users", middlewares.AuthGuard(), middlewares.AdminGuard())

	//CRUD
	adminGroup.POST("", ac.Create())
	adminGroup.GET("/:id", ac.Find())
	adminGroup.GET("", ac.FindAll())
	adminGroup.PUT("/:id", ac.Update())
	adminGroup.PATCH("/:id", ac.Patch())
	adminGroup.DELETE("/:id", ac.Delete())

	// Card CRUD
	cardsGroup := adminGroup.Group("/:userId/cards")
	cardsGroup.GET("", ac.FindCard())
}

// CRUD
func (ac *adminController) Create() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var user model.User
		if err := c.Bind(&user); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		if user.ID != "" {
			return errors.New(errors.Controller, "user ID cannot be manipulated", errors.Validation, nil)
		}

		if err := ac.Service.Create(ctx, &user, user.ReferralCode); err != nil {
			return err
		}

		// Fetch the created user to return in response
		createdUser, err := ac.Service.Find(ctx, user.ID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, createdUser.Sanitize())
	}
}

func (ac *adminController) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		user, err := ac.Service.Find(ctx, c.Param("id"))

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, user.Sanitize())
	}
}

func (ac *adminController) FindAll() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		users, err := ac.Service.FindAll(ctx)

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, users)
	}
}

func (ac *adminController) Update() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := strings.TrimSpace(c.Param("id"))
		if id == "" {
			return errors.New(errors.Controller, "invalid user ID", errors.Validation, nil)
		}

		var user model.User
		if err := c.Bind(&user); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		if user.ID != "" {
			return errors.New(errors.Controller, "user ID cannot be manipulated", errors.Validation, nil)
		}

		if len(user.Roles) > 0 {
			return errors.New(errors.Controller, "user roles cannot be manipulated", errors.Forbidden, nil)
		}

		user.ID = id
		if err := ac.Service.Update(ctx, &user); err != nil {
			return err
		}

		// Return updated user
		updatedUser, err := ac.Service.Find(ctx, id)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedUser.Sanitize())
	}
}

func (ac *adminController) Patch() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := strings.TrimSpace(c.Param("id"))
		if id == "" {
			return errors.New(errors.Controller, "invalid user ID", errors.Validation, nil)
		}

		// Get current user
		currentUser, err := ac.Service.Find(ctx, id)
		if err != nil {
			return err
		}

		// Bind patch data
		var patchData model.User
		if err := c.Bind(&patchData); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		// Ensure ID is not modified
		if patchData.ID != "" {
			return errors.New(errors.Controller, "user ID cannot be manipulated", errors.Validation, nil)
		}

		// Ensure roles are not modified
		if len(patchData.Roles) > 0 {
			return errors.New(errors.Controller, "user roles cannot be manipulated", errors.Forbidden, nil)
		}

		// Apply patch
		if err := ac.Service.Patch(ctx, currentUser, &patchData); err != nil {
			return err
		}

		// Get updated user
		updatedUser, err := ac.Service.Find(ctx, id)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedUser.Sanitize())
	}
}

func (ac *adminController) Delete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := strings.TrimSpace(c.Param("id"))
		if id == "" {
			return errors.New(errors.Controller, "invalid user ID", errors.Validation, nil)
		}

		// Check if user exists before attempting deletion
		if _, err := ac.Service.Find(ctx, id); err != nil {
			return err
		}

		var deleteReason model.DeleteReason
		if err := c.Bind(&deleteReason); err != nil {
			return errors.New(errors.Controller, "invalid delete reason", errors.Validation, err)
		}

		if err := ac.Service.Delete(ctx, id, &deleteReason); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Card CRUD
func (ac *adminController) FindCard() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userID := strings.TrimSpace(c.Param("userId"))
		cardID := strings.TrimSpace(c.Param("id"))

		if userID == "" || cardID == "" {
			return errors.New(errors.Controller, "invalid user ID or card ID", errors.Validation, nil)
		}

		card, err := ac.Service.FindCard(ctx, userID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, card)
	}
}
