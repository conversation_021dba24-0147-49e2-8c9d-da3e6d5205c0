.PHONY: build run test clean

# Go parameters
BINARY_NAME=dinbora
CMD_PATH=./cmd/dinbora

# Build and run
all: build run

# Build the application
build:
	go build -o bin/$(BINARY_NAME) $(CMD_PATH)

# Run the application
run:
	go run $(CMD_PATH)/main.go

# Run the application with Air for live reloading
dev:
	air

# Run tests
test:
	go test ./...

# Clean build artifacts
clean:
	go clean
	rm -f bin/$(BINARY_NAME)

# Docker build
docker-build:
	docker build -t $(BINARY_NAME):dev -f docker/Dockerfile .

# Docker run
docker-run:
	docker run -it --rm $(BINARY_NAME):dev

# Docker Compose build
docker-compose-build:
	docker-compose -f docker/docker-compose.yml up -d

# Docker Compose rebuild
docker-compose-rebuild:
	docker-compose -f docker/docker-compose.yml up -d --build

# Docker Compose down
docker-compose-down:
	docker-compose -f docker/docker-compose.yml down

#make: Build and run the app
#make build: Compile the binary
#make run: Run the application
#make dev: Run the application with Air for live reloading
#make test: Run tests
#make clean: Remove build artifacts
#make docker-build: Build APP Docker image
#make docker-run: Run APP Docker container
#make docker-compose-build: Build full docker image including MongoDB and Redis
#make docker-compose-rebuild: Rebuild full docker image including MongoDB and Redis
#make docker-compose-down To stop and remove containers