package trail

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func (m mongoDB) CreateTutorial(ctx context.Context, content *content.Tutorial) error {
	_, err := m.tutorialCollection.InsertOne(ctx, content)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "tutorial already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create tutorial", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) FindTutorial(ctx context.Context, id string) (*content.Tutorial, error) {
	trailObjectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid tutorial ID format", errors.Validation, err)
	}

	var trailContent content.Tutorial
	if err = m.tutorialCollection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: trailObjectID}}).Decode(&trailContent); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "tutorial not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find tutorial", errors.Internal, err)
	}

	return &trailContent, nil
}

func (m mongoDB) FindAllTutorials(ctx context.Context) ([]*content.Tutorial, error) {
	cursor, err := m.tutorialCollection.Find(ctx, bson.D{}, nil)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query tutorials", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var tutorials []*content.Tutorial
	for cursor.Next(ctx) {
		var tutorial content.Tutorial
		if err = cursor.Decode(&tutorial); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode tutorial", errors.Internal, err)
		}
		tutorials = append(tutorials, &tutorial)
	}

	return tutorials, nil
}

func (m mongoDB) FindTutorialByIdentifier(ctx context.Context, identifier string) (*content.Tutorial, error) {
	var tutorialContent content.Tutorial
	if err := m.tutorialCollection.FindOne(ctx,
		bson.D{primitive.E{Key: "identifier", Value: identifier}}).Decode(&tutorialContent); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "tutorial not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find tutorial by identifier", errors.Internal, err)
	}

	return &tutorialContent, nil
}

func (m mongoDB) FindTutorialByTicker(ctx context.Context, ticker string) (*content.Tutorial, error) {
	var foundTutorial content.Tutorial
	if err := m.tutorialCollection.FindOne(ctx,
		bson.D{primitive.E{Key: "ticker", Value: ticker}}).Decode(&foundTutorial); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "tutorial not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find tutorial by ticker", errors.Internal, err)
	}

	return &foundTutorial, nil
}

func (m mongoDB) UpdateTutorial(ctx context.Context, content *content.Tutorial) error {
	result, err := m.tutorialCollection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: content.ObjectID}},
		primitive.M{"$set": content})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "tutorial already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update tutorial", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "tutorial not found", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) DeleteTutorial(ctx context.Context, id string) error {
	trailObjectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid tutorial ID format", errors.Validation, err)
	}

	result, err := m.tutorialCollection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: trailObjectID}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete tutorial", errors.Internal, err)
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "tutorial not found", errors.NotFound, nil)
	}

	return nil
}
