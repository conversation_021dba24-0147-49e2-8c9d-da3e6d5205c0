package middlewares

import "github.com/labstack/echo/v4/middleware"

func SecureConfig() middleware.SecureConfig {
	return middleware.SecureConfig{
		// --- Secure Middleware Configuration ---
		Skipper: middleware.DefaultSkipper,

		// XSSProtection: Default ("1; mode=block") is reasonable.
		// While modern CSP is preferred, this doesn't hurt for older browsers.
		XSSProtection: "1; mode=block",

		// ContentTypeNosniff: Default ("nosniff") is essential. Keep it.
		ContentTypeNosniff: "nosniff",

		// XFrameOptions: For an API, nothing should *ever* frame it. DENY is strongest.
		// SAMEORIGIN is okay, but DENY is clearer for APIs.
		XFrameOptions: "DENY",

		// HSTSMaxAge: MUST be non-zero for HTTPS production sites.
		// Value is in seconds. 31536000 = 1 year.
		// **IMPORTANT**: Start with a SMALL value during testing (e.g., 3600 = 1 hour)
		// because once a browser caches this, it WILL enforce HTTPS for that duration.
		// Only increase to a large value once you are SURE everything works on HTTPS.
		HSTSMaxAge: 31536000, // 1 year - USE WITH CAUTION INITIALLY

		// HSTSExcludeSubdomains: Default is false, meaning includeSubdomains is implicitly added.
		// Set to true ONLY if you have subdomains that MUST NOT be forced to HTTPS (rarely a good idea).
		HSTSExcludeSubdomains: false,

		// ContentSecurityPolicy (CSP): This is highly application-specific.
		// For a JSON API, responses don't typically contain executable content directly.
		// A basic restrictive policy for an API might be:
		// - default-src 'none': Disallow loading anything by default.
		// - frame-ancestors 'none': Disallow framing (overlaps with X-Frame-Options but CSP is preferred).
		// - form-action 'self': Allows forms (if any served by API) to submit to the same origin.
		//
		// *** NOTE: The *main* CSP policy that protects your React app against XSS should be set
		// *** on the response that serves your index.html file (wherever that is hosted).
		// *** That policy will need to allow scripts/styles/fonts from 'self', connect-src to your API endpoint, etc.
		// Setting a basic one here on the API is still good practice.
		ContentSecurityPolicy: "default-src 'none'; frame-ancestors 'none';", // Basic API policy

		// HSTSPreloadEnabled: If you plan to submit your domain to HSTS preload lists (requires commitment)
		// HSTSPreloadEnabled: false, // Default is false

		// ContentSecurityPolicyReportOnly: Useful for testing CSP without enforcing it.
		// ContentSecurityPolicyReportOnly: "default-src 'self'; report-uri /csp-report-endpoint", // Example

		// ReferrerPolicy: Controls how much referrer info is sent.
		// "no-referrer" or "strict-origin-when-cross-origin" are good security defaults.
		// ReferrerPolicy: "no-referrer", // Example policy
	}
}
