# Dinbora Backend

## Overview

Dinbora Backend is a Go-based REST API service providing the backend infrastructure for the Dinbora financial education and management application. The system focuses on scalability, maintainability, and security, offering features like content management, user progression tracking, financial sheet management, a dreamboard system, user management, billing, and a unique Financial DNA system.

For detailed project goals and requirements, see [memory-bank/projectbrief.md](memory-bank/projectbrief.md).
For product context and user experience goals, see [memory-bank/productContext.md](memory-bank/productContext.md).

## Core Features

*   **Content Management:** Structured lessons, learning trails, tutorials, and achievements.
*   **User Progression:** Tracks user progress through educational content.
*   **Financial Sheet:** Manages financial records, sources, categories, and investments.
*   **Dreamboard:** Visual goal setting and tracking.
*   **User Management:** Secure authentication, profiles, and data vault.
*   **Billing & Subscriptions:** Integration for payments and subscriptions.
*   **Financial DNA:** Tracks family financial relationships and analyzes multi-generational data.
*   **External Integrations:** Supports authentication via Google, Facebook, and Apple.

## Technology Stack

*   **Backend:** Go
*   **Database:** MongoDB
*   **Development:** Docker, Make, Air (live reload)

For more details on the tech stack and dependencies, see [memory-bank/techContext.md](memory-bank/techContext.md).

## Architecture

The backend follows a clean architecture pattern with distinct layers:

1.  **API Layer:** Handles HTTP requests, routing, and middleware.
2.  **Controllers:** Manages request validation and response formatting.
3.  **Services:** Contains core business logic and orchestrates operations.
4.  **Repositories:** Abstracts data access and interacts with data stores.

Key design patterns include Repository, Service, Controller, and Domain Model.

For a detailed architecture overview and system patterns, see [memory-bank/systemPatterns.md](memory-bank/systemPatterns.md).

## Project Structure

```
dinbora-backend/
├── cmd/            # Application entry points
├── config/         # Configuration management
├── data/           # Data layer (MongoDB)
├── internal/       # Core application code
│   ├── api/        # API handling (router, server, middleware)
│   ├── controller/ # Request handlers per feature
│   ├── model/      # Domain models per feature
│   ├── repository/ # Data access logic per feature
│   └── service/    # Business logic per feature
├── docs/           # Project documentation
├── memory-bank/    # Cline's Memory Bank
├── migration/      # Database migration scripts
└── ... (other supporting files/dirs)
```

## Development Setup

### Prerequisites

*   Go (version specified in `go.mod`)
*   Docker & Docker Compose
*   Make
*   Access to MongoDB & Redis instances

### Running Locally

1.  **Configure Environment:** Copy `.env` and adjust settings if needed (e.g., database connection strings). Ensure necessary environment variables are set (consult `.clinerules` or `config/config.go` for required vars, avoiding direct secret exposure).
2.  **Start Services:** Use Docker Compose (if applicable) or ensure MongoDB/Redis are running.
3.  **Run Application:**
    ```bash
    # Start with live reload using Air
    make dev

    # Run tests
    make test

    # Build the binary
    make build
    ```

Refer to the `Makefile` for other useful commands.

For detailed information on using Air for live reloading during development, see [docs/development/air.md](docs/development/air.md).

## CI/CD Pipeline

![Progression CI](https://github.com/dsoplabs/dinbora-backend/actions/workflows/progression-ci.yml/badge.svg?branch=test)

We use GitHub Actions for continuous integration and deployment:

- **Progression System CI**: Automatically runs when changes are pushed to the `test` branch that affect the progression system
- **Pipeline Steps**: Linting → Testing → Security Scanning → Building → Notification

For more details on the CI/CD pipeline, see the workflow files in `.github/workflows/`.

## Project Status

Most core features are implemented, including User Management, Content System, Payments, Financial Sheet, and Dreamboard. The Financial DNA system is largely implemented but requires further testing and refinement.

For detailed progress and known issues, see [memory-bank/progress.md](memory-bank/progress.md).
For the current development focus, see [memory-bank/activeContext.md](memory-bank/activeContext.md).
