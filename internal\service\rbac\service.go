package rbac

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Service provides role-based access control functionality
type Service interface {
	// BuildUserFilter creates a MongoDB filter based on user roles and permissions
	BuildUserFilter(ctx context.Context, userID string, userRoles []string) bson.M

	// BuildDepartmentFilter creates a filter for department-scoped access
	BuildDepartmentFilter(ctx context.Context, userID string, userRoles []string, departmentField string) bson.M

	// BuildCompanyFilter creates a filter for company-scoped access (HR roles)
	BuildCompanyFilter(ctx context.Context, userID string, userRoles []string, companyField string) bson.M

	// BuildStudentFilter creates a filter for teacher-student relationships
	BuildStudentFilter(ctx context.Context, userID string, userRoles []string, teacherField, studentField string) bson.M

	// BuildHREmployeeFilter creates a filter for HR-employee relationships based on referral system
	BuildHREmployeeFilter(ctx context.Context, userID string, userRoles []string) bson.M

	// CanAccessResource checks if a user can access a specific resource
	CanAccessResource(ctx context.Context, userID string, userRoles []string, resourceOwnerID string) bool

	// GetAccessibleUserIDs returns a list of user IDs that the current user can access
	GetAccessibleUserIDs(ctx context.Context, userID string, userRoles []string) ([]string, error)
}

type service struct {
	// Add dependencies here if needed (e.g., user repository for department/assignment lookups)
}

// New creates a new RBAC service
func New() Service {
	return &service{}
}

// BuildUserFilter creates a MongoDB filter based on user roles and permissions
func (s *service) BuildUserFilter(ctx context.Context, userID string, userRoles []string) bson.M {
	permissions := auth.GetPermissions(userRoles)

	// Admin can access all data
	if permissions.CanAccessAllData {
		return bson.M{} // No filter - access all
	}

	// Build filter based on permissions
	var filters []bson.M

	// Users can always access their own data
	if permissions.CanAccessOwnData {
		filters = append(filters, bson.M{"_id": userID})
		// Also handle cases where user ID is stored as string
		if objID, err := primitive.ObjectIDFromHex(userID); err == nil {
			filters = append(filters, bson.M{"_id": objID})
		}
	}

	// Directors can access department data
	if permissions.CanAccessDepartment {
		// This would need additional logic to determine department membership
		// For now, we'll allow broader access for directors
		filters = append(filters, bson.M{"roles": bson.M{"$in": []string{"student", "user", "teacher"}}})
	}

	// Teachers can access their students
	if permissions.CanAccessOwnStudents {
		// This would need additional logic to determine teacher-student relationships
		// For now, we'll allow teachers to access students and general users
		filters = append(filters, bson.M{"roles": bson.M{"$in": []string{"student", "user"}}})
	}

	// HR can access company data
	if permissions.CanAccessCompanyData {
		if len(permissions.CompanyIDs) > 0 {
			filters = append(filters, bson.M{"companyId": bson.M{"$in": permissions.CompanyIDs}})
		} else {
			// Base HR role - access all company data
			filters = append(filters, bson.M{"companyId": bson.M{"$exists": true}})
		}
	}

	// If no filters, deny access
	if len(filters) == 0 {
		return bson.M{"_id": primitive.NewObjectID()} // Impossible condition
	}

	// Combine filters with OR logic
	if len(filters) == 1 {
		return filters[0]
	}

	return bson.M{"$or": filters}
}

// BuildDepartmentFilter creates a filter for department-scoped access
func (s *service) BuildDepartmentFilter(ctx context.Context, userID string, userRoles []string, departmentField string) bson.M {
	permissions := auth.GetPermissions(userRoles)

	// Admin can access all departments
	if permissions.CanAccessAllData {
		return bson.M{}
	}

	// Directors can access their department
	if permissions.CanAccessDepartment {
		// This would need additional logic to determine user's department
		// For now, we'll return a placeholder filter
		return bson.M{departmentField: bson.M{"$exists": true}}
	}

	// Default: no access to department data
	return bson.M{"_id": primitive.NewObjectID()} // Impossible condition
}

// BuildCompanyFilter creates a filter for company-scoped access (HR roles)
func (s *service) BuildCompanyFilter(ctx context.Context, userID string, userRoles []string, companyField string) bson.M {
	permissions := auth.GetPermissions(userRoles)

	// Admin can access all companies
	if permissions.CanAccessAllData {
		return bson.M{}
	}

	// HR can access their company data
	if permissions.CanAccessCompanyData {
		if len(permissions.CompanyIDs) > 0 {
			return bson.M{companyField: bson.M{"$in": permissions.CompanyIDs}}
		} else {
			// Base HR role - access all companies
			return bson.M{companyField: bson.M{"$exists": true}}
		}
	}

	// Default: no access to company data
	return bson.M{"_id": primitive.NewObjectID()} // Impossible condition
}

// BuildStudentFilter creates a filter for teacher-student relationships
func (s *service) BuildStudentFilter(ctx context.Context, userID string, userRoles []string, teacherField, studentField string) bson.M {
	permissions := auth.GetPermissions(userRoles)

	// Admin can access all student data
	if permissions.CanAccessAllData {
		return bson.M{}
	}

	var filters []bson.M

	// Users can access their own data
	if permissions.CanAccessOwnData {
		filters = append(filters, bson.M{studentField: userID})
		if objID, err := primitive.ObjectIDFromHex(userID); err == nil {
			filters = append(filters, bson.M{studentField: objID})
		}
	}

	// Teachers can access their students
	if permissions.CanAccessOwnStudents {
		filters = append(filters, bson.M{teacherField: userID})
		if objID, err := primitive.ObjectIDFromHex(userID); err == nil {
			filters = append(filters, bson.M{teacherField: objID})
		}
	}

	// Directors can access department students
	if permissions.CanAccessDepartment {
		// This would need additional logic for department-based filtering
		filters = append(filters, bson.M{studentField: bson.M{"$exists": true}})
	}

	// If no filters, deny access
	if len(filters) == 0 {
		return bson.M{"_id": primitive.NewObjectID()} // Impossible condition
	}

	// Combine filters with OR logic
	if len(filters) == 1 {
		return filters[0]
	}

	return bson.M{"$or": filters}
}

// BuildHREmployeeFilter creates a filter for HR-employee relationships based on referral system
// This method specifically handles the case where HR users want to access employees they referred
func (s *service) BuildHREmployeeFilter(ctx context.Context, userID string, userRoles []string) bson.M {
	permissions := auth.GetPermissions(userRoles)

	// Admin can access all employee data
	if permissions.CanAccessAllData {
		return bson.M{}
	}

	// Only HR users can use this filter
	if !permissions.CanAccessCompanyData {
		// Return impossible condition for non-HR users
		return bson.M{"_id": primitive.NewObjectID()}
	}

	// HR users can access employees they referred through the referral system
	// Filter by ReferringUserID field to find employees referred by this HR user
	filters := []bson.M{
		{"referringUserId": userID},
	}

	// Also try ObjectID format in case the field stores ObjectID instead of string
	if objID, err := primitive.ObjectIDFromHex(userID); err == nil {
		filters = append(filters, bson.M{"referringUserId": objID})
	}

	// Combine filters with OR logic
	if len(filters) == 1 {
		return filters[0]
	}

	return bson.M{"$or": filters}
}

// CanAccessResource checks if a user can access a specific resource
func (s *service) CanAccessResource(ctx context.Context, userID string, userRoles []string, resourceOwnerID string) bool {
	permissions := auth.GetPermissions(userRoles)

	// Admin can access all resources
	if permissions.CanAccessAllData {
		return true
	}

	// Users can access their own resources
	if permissions.CanAccessOwnData && userID == resourceOwnerID {
		return true
	}

	// Directors can access department resources
	if permissions.CanAccessDepartment {
		// This would need additional logic to check department membership
		return true
	}

	// Teachers can access their students' resources
	if permissions.CanAccessOwnStudents {
		// This would need additional logic to check teacher-student relationships
		return true
	}

	// HR can access company resources
	if permissions.CanAccessCompanyData {
		// This would need additional logic to check company membership
		return true
	}

	return false
}

// GetAccessibleUserIDs returns a list of user IDs that the current user can access
func (s *service) GetAccessibleUserIDs(ctx context.Context, userID string, userRoles []string) ([]string, error) {
	permissions := auth.GetPermissions(userRoles)

	var accessibleIDs []string

	// Users can always access their own data
	if permissions.CanAccessOwnData {
		accessibleIDs = append(accessibleIDs, userID)
	}

	// Admin can access all users - return empty slice to indicate "all"
	if permissions.CanAccessAllData {
		return []string{}, nil
	}

	// For other roles, we would need to query the database to get the actual user IDs
	// This is a simplified implementation

	return accessibleIDs, nil
}

// Helper functions for common filtering patterns

// BuildOwnershipFilter creates a filter that allows access to owned resources
func BuildOwnershipFilter(userID string, ownerField string) bson.M {
	filters := []bson.M{
		{ownerField: userID},
	}

	// Also try ObjectID format
	if objID, err := primitive.ObjectIDFromHex(userID); err == nil {
		filters = append(filters, bson.M{ownerField: objID})
	}

	if len(filters) == 1 {
		return filters[0]
	}

	return bson.M{"$or": filters}
}

// BuildRoleBasedFilter creates a filter based on user roles and a custom field
func BuildRoleBasedFilter(userRoles []string, roleField string, allowedRoles []string) bson.M {
	permissions := auth.GetPermissions(userRoles)

	// Admin can access all
	if permissions.CanAccessAllData {
		return bson.M{}
	}

	// Filter by allowed roles
	return bson.M{roleField: bson.M{"$in": allowedRoles}}
}

// CombineFilters combines multiple filters with AND logic
func CombineFilters(filters ...bson.M) bson.M {
	var nonEmptyFilters []bson.M

	for _, filter := range filters {
		if len(filter) > 0 {
			nonEmptyFilters = append(nonEmptyFilters, filter)
		}
	}

	if len(nonEmptyFilters) == 0 {
		return bson.M{}
	}

	if len(nonEmptyFilters) == 1 {
		return nonEmptyFilters[0]
	}

	return bson.M{"$and": nonEmptyFilters}
}
