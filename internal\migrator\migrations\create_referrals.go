package migrations

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"github.com/dsoplabs/dinbora-backend/pkg"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// CreateReferralsMigration implements the Migration interface for creating user referral codes
type CreateReferralsMigration struct {
	db *mongo.Database
}

func NewCreateReferralsMigration(db *mongo.Database) *CreateReferralsMigration {
	return &CreateReferralsMigration{db: db}
}

func (m *CreateReferralsMigration) Name() string {
	return "create_referrals"
}

func (m *CreateReferralsMigration) Up(ctx context.Context) error {
	userCollection := m.db.Collection(repository.USERS_COLLECTION)

	cursor, err := userCollection.Find(ctx, bson.D{primitive.E{
		Key:   "referralCode",
		Value: nil,
	}})

	if err != nil {
		if err != mongo.ErrNoDocuments {
			return err
		}
		return nil
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var user model.User
		if err := cursor.Decode(&user); err != nil {
			return err
		}

		user.ID = user.ObjectID.Hex()

		if user.ReferralCode == "" {
			user.ReferralCode = pkg.ReferralCodeGeneration()

			result, err := userCollection.UpdateOne(ctx,
				bson.D{primitive.E{Key: "_id", Value: user.ObjectID}},
				bson.M{"$set": bson.M{"referralCode": user.ReferralCode}})

			if err != nil {
				if mongo.IsDuplicateKeyError(err) {
					return errors.New(errors.Migrator, "user already exists", errors.Conflict, err)
				}
				return err
			}

			if result.MatchedCount <= 0 {
				return errors.New(errors.Migrator, "user not found", errors.NotFound, err)
			}
		}
	}

	return cursor.Err()
}
