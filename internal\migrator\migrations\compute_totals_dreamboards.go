package migrations

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ComputeTotalsDreamboards implements the Migration interface for creating default dreamboards
type ComputeTotalsDreamboards struct {
	db *mongo.Database
}

func NewComputeTotals(db *mongo.Database) *ComputeTotalsDreamboards {
	return &ComputeTotalsDreamboards{db: db}
}

func (m *ComputeTotalsDreamboards) Name() string {
	return "compute_totals_dreamboards"
}

func (m *ComputeTotalsDreamboards) Up(ctx context.Context) error {
	dreamboardCollection := m.db.Collection(repository.DREAMBOARDS_COLLECTION)

	cursor, err := dreamboardCollection.Find(ctx, bson.D{})
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var dreamboard dreamboard.Dreamboard
		if err := cursor.Decode(&dreamboard); err != nil {
			return err
		}

		dreamboard.ComputeTotals()

		result, err := dreamboardCollection.UpdateOne(ctx,
			bson.D{primitive.E{Key: "_id", Value: dreamboard.ObjectID}},
			bson.M{"$set": bson.M{
				"totalDreamsCost": dreamboard.TotalDreamsCost,
				"savedAmount":     dreamboard.SavedAmount,
				"monthlyNeeded":   dreamboard.MonthlyNeeded,
				"remainingAmount": dreamboard.RemainingAmount,
			}},
		)

		if err != nil {
			if mongo.IsDuplicateKeyError(err) {
				return errors.New(errors.Migrator, errors.DreamboardConflictUpdate, errors.Conflict, err)
			}
			return errors.New(errors.Migrator, errors.DreamboardUpdateFailed, errors.Internal, err)
		}

		if result.MatchedCount == 0 {
			return errors.New(errors.Migrator, errors.DreamboardNotFound, errors.NotFound, nil)
		}
	}

	return cursor.Err()
}
