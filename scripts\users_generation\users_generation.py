import json
import os
import random
from bson.objectid import ObjectId

NUMBER_OF_USERS = 100  # Number of fake users to generate

# Get current working directory
generation_file = os.path.realpath(
    os.path.join(os.getcwd(), os.path.dirname(__file__))) + "/users_generation.json"

# Directory to save files
save_dir = os.getcwd() + "/migration/users"
os.makedirs(save_dir, exist_ok=True)

# Function to generate fake users
def generate_fake_users_json(count=10):
    users = []
    for i in range(1, count + 1):
        user = {
            "name": f"Fake_User_{i}",
            "email": f"fake_user{i}@example.com",
            "avatar": f"https://api.dicebear.com/9.x/personas/svg?seed=fake_user_{i}.svg",
            "password": f"Pass{i}Secure!",
            "referralCode": f"REF{i}",
            "onboarding": {
                "ageRange": {"id": random.randint(1, 5)},
                "financialSituation": {"id": random.randint(1, 5)},
                "financialGoal": {"id": random.randint(1, 5)},
                "personalInterests": []
            }
        }
        
        # Generate unique personal interests
        interest_ids = random.sample(range(1, 23), random.randint(1, 10))
        user["onboarding"]["personalInterests"] = [{"id": interest_id, "labels": None} for interest_id in interest_ids]
        
        users.append(user)
    
    with open(generation_file, "w") as file:
        json.dump(users, file, indent=4)
    
    print(f"Generated {count} fake users and saved to {generation_file}")

def generate_fake_users_mongo():
    # Read JSON data from a file
    with open(generation_file, "r") as file:
        users = json.load(file)

    # Iterate through users and save each to a file with a MongoDB ObjectId as filename
    for user in users:
        object_id = str(ObjectId())  # Generate MongoDB ObjectId
        filename = os.path.join(save_dir, f"{object_id}.json")
    
        with open(filename, "w") as file:
            json.dump(user, file, indent=4)
    
        print(f"Saved {user['name']} to {filename}")

# Check if we need to generate fake users
if not os.path.exists(generation_file) or os.stat(generation_file).st_size == 0:
    generate_fake_users_json(NUMBER_OF_USERS)  # Defined in NUMBER_OF_USERS

# Generate fake users
if not os.path.exists(save_dir) or len(os.listdir(save_dir)) == 0:
    generate_fake_users_mongo()
