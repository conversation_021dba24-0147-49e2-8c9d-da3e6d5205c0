package pkg

import (
	"crypto/aes"
	"crypto/cipher"
	"os"
)

var commonIV = []byte{0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f}

func Encrypt(value string) (string, error) {
	key := os.Getenv("ENCRYPTION_KEY")
	c, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	valueBytes := []byte(value)
	cfb := cipher.NewCFBEncrypter(c, commonIV)
	ciphertext := make([]byte, len(valueBytes))
	cfb.XORKeyStream(ciphertext, valueBytes)

	return string(ciphertext), nil
}

func Decrypt(value string) (string, error) {
	key := os.Getenv("ENCRYPTION_KEY")
	c, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}
	valueBytes := []byte(value)

	// Decrypt strings
	cfbdec := cipher.NewCFBDecrypter(c, commonIV)
	plaintextCopy := make([]byte, len(valueBytes))
	cfbdec.XORKeyStream(plaintextCopy, valueBytes)

	return string(plaintextCopy), nil
}
