#!/bin/bash
# Deployment script executed on the EC2 instance via SSM with sudo

# Print commands and exit on first error
set -ex

echo "--- Starting Application Deployment Script ---"
TIMESTAMP=$1 # Get timestamp passed as argument $1 from SSM command
if [ -z "$TIMESTAMP" ]; then
  echo "[ERROR] Timestamp argument missing! Cannot identify temporary directory."
  exit 1
fi

# Define paths based on the provided timestamp
TMP_DIR="/home/<USER>/tmp_deploy_${TIMESTAMP}"
# Define the location where refresh-env.sh was downloaded
TMP_REFRESH_SCRIPT_LOCATION="$TMP_DIR/refresh-env.sh"
# Define application paths
APP_BINARY_PATH="/usr/local/bin/dinbora"
APP_MIGRATION_DIR="/usr/local/dinbora/migration"
APP_BASE_DIR="/usr/local/dinbora"
APP_SERVICE_NAME="dinbora"
APP_USER="ec2-user" # User the app runs as (for file ownership)
APP_GROUP="ec2-user" # Group the app runs as

echo "[INFO] Using temporary deployment directory: $TMP_DIR"
echo "[INFO] Application binary path: $APP_BINARY_PATH"
echo "[INFO] Migration directory: $APP_MIGRATION_DIR"
echo "[INFO] Application service name: $APP_SERVICE_NAME"

# --- Pre-flight Check ---
if [ ! -f "$TMP_DIR/dinbora" ]; then
    echo "[ERROR] Application binary not found in temporary directory: $TMP_DIR/dinbora"
    exit 1
fi
if [ ! -d "$TMP_DIR/migration" ]; then
    echo "[ERROR] Migration directory not found in temporary directory: $TMP_DIR/migration"
    exit 1
fi
if [ ! -f "$TMP_REFRESH_SCRIPT_LOCATION" ]; then
    echo "[ERROR] Environment refresh script not found: $TMP_REFRESH_SCRIPT_LOCATION"
    exit 1
fi

# --- Deployment Steps ---
echo '[INFO] Stopping service...'
# Use systemctl directly as this script is already run with sudo
systemctl stop "$APP_SERVICE_NAME" || echo "[WARN] Service $APP_SERVICE_NAME was not running or failed to stop."

echo '[INFO] Deploying binary...'
rm -f "$APP_BINARY_PATH"
mv "$TMP_DIR/dinbora" "$APP_BINARY_PATH"
chmod +x "$APP_BINARY_PATH"
chown "$APP_USER:$APP_GROUP" "$APP_BINARY_PATH"
# Attempt to set SELinux context but don't fail if it doesn't work
chcon -t bin_t "$APP_BINARY_PATH" || echo "[WARN] Could not set SELinux context on binary (maybe SELinux is disabled or context type is wrong)."

echo '[INFO] Deploying migrations...'
# Ensure base app directory exists first
mkdir -p "$APP_BASE_DIR"
chown "$APP_USER:$APP_GROUP" "$APP_BASE_DIR"
# Clear old migrations and ensure target dir exists
rm -rf "${APP_MIGRATION_DIR:?}/"* # Safety: :? ensures variable is set before deleting *
mkdir -p "$APP_MIGRATION_DIR"
# Move new migrations
mv "$TMP_DIR/migration/"* "$APP_MIGRATION_DIR/"
# Set ownership for the whole app directory recursively
chown -R "$APP_USER:$APP_GROUP" "$APP_BASE_DIR"

echo '[INFO] Executing environment refresh script...'
# Execute the refresh script. It handles its own logging and needs root.
# Since this script (deploy_app.sh) is run with sudo, the child script also runs as root.
"$TMP_REFRESH_SCRIPT_LOCATION"
# Check exit code of the refresh script
REFRESH_EXIT_CODE=$?
if [ $REFRESH_EXIT_CODE -ne 0 ]; then
    echo "[ERROR] Environment refresh script failed with exit code $REFRESH_EXIT_CODE. Check its log file in /tmp/. Aborting start."
    # Optional: Maybe attempt to restore old .env file if you have backups?
    exit 1
fi

echo '[INFO] Flushing filesystem buffers...'
sync

echo '[INFO] Starting service...'
systemctl start "$APP_SERVICE_NAME"

echo '[INFO] Checking service status...'
# Give service a moment to start before checking status
sleep 2
systemctl status "$APP_SERVICE_NAME" --no-pager

# --- Cleanup ---
echo "[INFO] Cleaning up temporary deployment directory: $TMP_DIR"
# Use rm -rf directly as this script runs with sudo
rm -rf "$TMP_DIR"

echo "--- Application Deployment Script Finished Successfully ---"
exit 0