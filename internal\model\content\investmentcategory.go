package content

import (
	"strings"
	"time"

	"github.com/imdario/mergo"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type InvestmentCategory struct {
	ObjectID   primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID         string             `json:"_id,omitempty" bson:"-"`
	Name       string             `json:"name" bson:"name"`
	Identifier string             `json:"identifier" bson:"identifier"`
	Logo       string             `json:"logo" bson:"logo"`
	Color      string             `json:"color" bson:"color"`
	CreatedAt  time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt  time.Time          `json:"updatedAt" bson:"updatedAt"`
}

func (t *InvestmentCategory) Sanitize() *InvestmentCategory {
	t.ID = t.ObjectID.Hex()

	return t
}

func (t *InvestmentCategory) PrepareCreate() error {
	t.Identifier = strings.TrimSpace(strings.ToLower(t.Identifier))

	t.CreatedAt = time.Now()
	t.UpdatedAt = t.CreatedAt

	return t.ValidateCreate()
}

func (t *InvestmentCategory) ValidateCreate() error {
	if t.Name == "" {
		return ErrInvestmentCategoryRequiredName
	}

	if t.Identifier == "" {
		return ErrInvestmentCategoryRequiredIdentifier
	}

	if t.Logo == "" {
		return ErrInvestmentCategoryRequiredLogo
	}

	return nil
}

func (t *InvestmentCategory) PrepareUpdate(newInvestmentCategory *InvestmentCategory) error {
	if err := mergo.Merge(t, newInvestmentCategory, mergo.WithOverride); err != nil {
		return err
	}

	t.Identifier = strings.TrimSpace(strings.ToLower(t.Identifier))

	t.UpdatedAt = time.Now()

	if t.ID == "" {
		if !t.ObjectID.IsZero() {
			t.ID = t.ObjectID.Hex()
		} else {
			return ErrInvestmentCategoryInvalidId
		}
	} else {
		icObjectId, err := primitive.ObjectIDFromHex(t.ID)
		if err != nil {
			return err
		}
		t.ObjectID = icObjectId
	}

	return t.ValidateUpdate()
}

func (t *InvestmentCategory) ValidateUpdate() error {
	return t.PrepareCreate()
}
