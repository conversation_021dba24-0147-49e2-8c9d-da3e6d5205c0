# Progression System Migration Plan (Option B: Behind-the-Scenes)

## Overview

This document outlines the complete migration strategy for refactoring the progression system from a complex nested structure to a simple event-driven architecture while maintaining full backward compatibility.

## Migration Strategy: Behind-the-Scenes Replacement

**Approach**: Keep all existing API endpoints unchanged while completely replacing the internal implementation with an event-driven system.

## Phase 1: Foundation Setup (Week 1)

### 1.1 Create New Models

#### Event Model (`internal/model/progression/event.go`)
```go
type ProgressEvent struct {
    ID        primitive.ObjectID `json:"-" bson:"_id,omitempty"`
    UserID    string            `json:"userId" bson:"userId"`
    TrailID   string            `json:"trailId" bson:"trailId"`
    ItemID    string            `json:"itemId" bson:"itemId"`        // lesson/challenge identifier
    ItemType  ProgressType      `json:"itemType" bson:"itemType"`    // LESSON | CHALLENGE
    Action    ActionType        `json:"action" bson:"action"`        // STARTED | COMPLETED
    ContentID string            `json:"contentId" bson:"contentId"`  // specific content within item
    Choice    *Choice           `json:"choice,omitempty" bson:"choice,omitempty"`
    Data      map[string]interface{} `json:"data,omitempty" bson:"data,omitempty"`
    Timestamp time.Time         `json:"timestamp" bson:"timestamp"`
    
    // Migration fields
    LegacyID  string            `json:"legacyId,omitempty" bson:"legacyId,omitempty"`
    Migrated  bool              `json:"migrated" bson:"migrated"`
}

type Choice struct {
    Identifier string `json:"identifier" bson:"identifier"`
    Next       string `json:"next" bson:"next"`
}
```

#### Summary Model (`internal/model/progression/summary.go`)
```go
type ProgressSummary struct {
    UserID      string                   `json:"userId" bson:"userId"`
    Trails      map[string]*TrailSummary `json:"trails" bson:"trails"`
    LastUpdated time.Time                `json:"lastUpdated" bson:"lastUpdated"`
    Version     int                      `json:"version" bson:"version"` // For cache invalidation
}

type TrailSummary struct {
    ID                  string            `json:"id" bson:"id"`
    LessonsCompleted    map[string]bool   `json:"lessonsCompleted" bson:"lessonsCompleted"`
    ChallengesCompleted map[string]bool   `json:"challengesCompleted" bson:"challengesCompleted"`
    CurrentItem         string            `json:"currentItem" bson:"currentItem"`
    ProgressPercent     int               `json:"progressPercent" bson:"progressPercent"`
    IsCompleted         bool              `json:"isCompleted" bson:"isCompleted"`
    TotalRewards        int               `json:"totalRewards" bson:"totalRewards"`
    LessonsRewarded     map[string]bool   `json:"lessonsRewarded" bson:"lessonsRewarded"`
    ChallengesRewarded  map[string]bool   `json:"challengesRewarded" bson:"challengesRewarded"`
    
    // Detailed progress tracking
    LessonProgress      map[string]*LessonProgress    `json:"lessonProgress" bson:"lessonProgress"`
    ChallengeProgress   map[string]*ChallengeProgress `json:"challengeProgress" bson:"challengeProgress"`
}

type LessonProgress struct {
    Current   string    `json:"current" bson:"current"`
    Completed bool      `json:"completed" bson:"completed"`
    Rewarded  bool      `json:"rewarded" bson:"rewarded"`
    Path      []string  `json:"path" bson:"path"` // Content IDs visited
}

type ChallengeProgress struct {
    Current   string                      `json:"current" bson:"current"`
    Completed bool                        `json:"completed" bson:"completed"`
    Rewarded  bool                        `json:"rewarded" bson:"rewarded"`
    Phases    map[string]*PhaseProgress   `json:"phases" bson:"phases"`
}

type PhaseProgress struct {
    Current   string    `json:"current" bson:"current"`
    Completed bool      `json:"completed" bson:"completed"`
    Path      []string  `json:"path" bson:"path"`
}
```

### 1.2 Update Repository Interface

#### New Repository Interface (`internal/repository/progression/repository.go`)
```go
type Repository interface {
    // Event operations
    CreateEvent(ctx context.Context, event *ProgressEvent) error
    GetUserEvents(ctx context.Context, userID string, limit int) ([]*ProgressEvent, error)
    GetTrailEvents(ctx context.Context, userID, trailID string) ([]*ProgressEvent, error)
    
    // Summary operations (cached)
    GetProgressSummary(ctx context.Context, userID string) (*ProgressSummary, error)
    SaveProgressSummary(ctx context.Context, summary *ProgressSummary) error
    InvalidateProgressSummary(ctx context.Context, userID string) error
    
    // Legacy support (during migration)
    GetLegacyProgression(ctx context.Context, userID string) (*Progression, error)
    CreateLegacyProgression(ctx context.Context, progression *Progression) error
    UpdateLegacyProgression(ctx context.Context, progression *Progression) error
    
    // Migration utilities
    MigrateLegacyToEvents(ctx context.Context, userID string) error
    IsUserMigrated(ctx context.Context, userID string) (bool, error)
    MarkUserMigrated(ctx context.Context, userID string) error
}
```

### 1.3 Create Event Store Implementation

#### MongoDB Event Store (`internal/repository/progression/event_store.go`)
```go
type eventStore struct {
    client     *mongo.Client
    database   *mongo.Database
    events     *mongo.Collection
    summaries  *mongo.Collection
    legacy     *mongo.Collection
}

func (es *eventStore) CreateEvent(ctx context.Context, event *ProgressEvent) error {
    event.Timestamp = time.Now()
    _, err := es.events.InsertOne(ctx, event)
    return err
}

func (es *eventStore) GetUserEvents(ctx context.Context, userID string, limit int) ([]*ProgressEvent, error) {
    filter := bson.M{"userId": userID}
    opts := options.Find().SetSort(bson.D{{"timestamp", 1}})
    if limit > 0 {
        opts.SetLimit(int64(limit))
    }
    
    cursor, err := es.events.Find(ctx, filter, opts)
    if err != nil {
        return nil, err
    }
    defer cursor.Close(ctx)
    
    var events []*ProgressEvent
    err = cursor.All(ctx, &events)
    return events, err
}
```

## Phase 2: Service Layer Implementation (Week 2)

### 2.1 Create Progress Calculator

#### Progress Calculator (`internal/service/progression/calculator.go`)
```go
type Calculator struct {
    trailService trail.Service
}

func (c *Calculator) CalculateProgressFromEvents(ctx context.Context, events []*ProgressEvent, userID string) (*ProgressSummary, error) {
    summary := &ProgressSummary{
        UserID:      userID,
        Trails:      make(map[string]*TrailSummary),
        LastUpdated: time.Now(),
        Version:     1,
    }
    
    // Group events by trail
    trailEvents := make(map[string][]*ProgressEvent)
    for _, event := range events {
        trailEvents[event.TrailID] = append(trailEvents[event.TrailID], event)
    }
    
    // Calculate progress for each trail
    for trailID, events := range trailEvents {
        trailContent, err := c.trailService.Find(ctx, trailID)
        if err != nil {
            continue // Skip if trail not found
        }
        
        trailSummary := c.calculateTrailProgress(events, trailContent)
        summary.Trails[trailID] = trailSummary
    }
    
    return summary, nil
}

func (c *Calculator) calculateTrailProgress(events []*ProgressEvent, trailContent *content.Trail) *TrailSummary {
    summary := &TrailSummary{
        ID:                  trailContent.ID,
        LessonsCompleted:    make(map[string]bool),
        ChallengesCompleted: make(map[string]bool),
        LessonsRewarded:     make(map[string]bool),
        ChallengesRewarded:  make(map[string]bool),
        LessonProgress:      make(map[string]*LessonProgress),
        ChallengeProgress:   make(map[string]*ChallengeProgress),
    }
    
    // Process events chronologically
    for _, event := range events {
        switch event.ItemType {
        case ProgressTypeLesson:
            c.processLessonEvent(summary, event, trailContent)
        case ProgressTypeChallenge:
            c.processChallengeEvent(summary, event, trailContent)
        }
    }
    
    // Calculate overall progress percentage
    c.calculateProgressPercentage(summary, trailContent)
    
    return summary
}

func (c *Calculator) processLessonEvent(summary *TrailSummary, event *ProgressEvent, trailContent *content.Trail) {
    lessonID := event.ItemID
    
    if summary.LessonProgress[lessonID] == nil {
        summary.LessonProgress[lessonID] = &LessonProgress{
            Path: make([]string, 0),
        }
    }
    
    progress := summary.LessonProgress[lessonID]
    
    switch event.Action {
    case ActionStarted:
        if event.ContentID != "" {
            progress.Path = append(progress.Path, event.ContentID)
            progress.Current = event.ContentID
        }
    case ActionCompleted:
        progress.Completed = true
        summary.LessonsCompleted[lessonID] = true
        
        // Check if rewarded
        if event.Choice != nil && (event.Choice.Next == string(RewardTypeCoin) || event.Choice.Next == string(RewardTypeDiamond)) {
            progress.Rewarded = true
            summary.LessonsRewarded[lessonID] = true
            summary.TotalRewards++
        }
    }
}
```

### 2.2 Create New Service Implementation

#### Event-Based Service (`internal/service/progression/event_service.go`)
```go
type eventService struct {
    repository      Repository
    calculator      *Calculator
    trailService    trail.Service
    vaultService    vault.Service
    cache           cache.CacheService
    migrationMode   bool // Flag to control migration behavior
}

func (s *eventService) RecordProgress(ctx context.Context, userID string, body *ProgressionBody) error {
    // Convert legacy body to event
    event := &ProgressEvent{
        UserID:    userID,
        TrailID:   body.Trail,
        ItemID:    body.Module,
        ItemType:  ProgressType(body.Type),
        Action:    ActionCompleted, // Most operations are completions
        ContentID: body.Content,
        Choice:    &Choice{
            Identifier: body.Choice.Identifier,
            Next:       body.Choice.Next,
        },
        Data: map[string]interface{}{
            "timestamp": body.Timestamp,
        },
    }
    
    // Store event
    if err := s.repository.CreateEvent(ctx, event); err != nil {
        return err
    }
    
    // Update cached summary
    if err := s.updateProgressSummary(ctx, userID); err != nil {
        // Log error but don't fail the operation
        log.Printf("Failed to update progress summary for user %s: %v", userID, err)
    }
    
    // Handle rewards
    if err := s.processRewards(ctx, userID, event); err != nil {
        log.Printf("Failed to process rewards for user %s: %v", userID, err)
    }
    
    return nil
}

func (s *eventService) GetUserProgress(ctx context.Context, userID string) (*ProgressSummary, error) {
    // Try cache first
    summary, err := s.repository.GetProgressSummary(ctx, userID)
    if err == nil && summary != nil {
        return summary, nil
    }
    
    // Calculate from events
    events, err := s.repository.GetUserEvents(ctx, userID, 0)
    if err != nil {
        return nil, err
    }
    
    summary, err = s.calculator.CalculateProgressFromEvents(ctx, events, userID)
    if err != nil {
        return nil, err
    }
    
    // Cache the result
    if err := s.repository.SaveProgressSummary(ctx, summary); err != nil {
        log.Printf("Failed to cache progress summary for user %s: %v", userID, err)
    }
    
    return summary, nil
}
```

## Phase 3: Migration Implementation (Week 3)

### 3.1 Data Migration Service

#### Migration Service (`internal/service/progression/migration.go`)
```go
type MigrationService struct {
    repository   Repository
    calculator   *Calculator
}

func (ms *MigrationService) MigrateUserData(ctx context.Context, userID string) error {
    // Check if already migrated
    migrated, err := ms.repository.IsUserMigrated(ctx, userID)
    if err != nil {
        return err
    }
    if migrated {
        return nil // Already migrated
    }
    
    // Get legacy progression data
    legacyProgression, err := ms.repository.GetLegacyProgression(ctx, userID)
    if err != nil {
        if err.(*errors.DomainError).Kind() == errors.NotFound {
            // No legacy data, mark as migrated
            return ms.repository.MarkUserMigrated(ctx, userID)
        }
        return err
    }
    
    // Convert legacy data to events
    events := ms.convertLegacyToEvents(legacyProgression)
    
    // Store events
    for _, event := range events {
        if err := ms.repository.CreateEvent(ctx, event); err != nil {
            return fmt.Errorf("failed to create migration event: %w", err)
        }
    }
    
    // Mark user as migrated
    return ms.repository.MarkUserMigrated(ctx, userID)
}

func (ms *MigrationService) convertLegacyToEvents(progression *Progression) []*ProgressEvent {
    var events []*ProgressEvent
    baseTime := progression.CreatedAt
    
    for _, trail := range progression.Trails {
        // Create events for lessons
        for _, lesson := range trail.Lessons {
            for i, content := range lesson.Path {
                event := &ProgressEvent{
                    UserID:    progression.User,
                    TrailID:   trail.ID,
                    ItemID:    lesson.Identifier,
                    ItemType:  ProgressTypeLesson,
                    Action:    ActionStarted,
                    ContentID: content.Identifier,
                    Choice: &Choice{
                        Identifier: content.Choice.Identifier,
                        Next:       content.Choice.Next,
                    },
                    Timestamp: baseTime.Add(time.Duration(i) * time.Minute),
                    Migrated:  true,
                    LegacyID:  progression.ObjectID.Hex(),
                }
                events = append(events, event)
                
                // Add completion event if it's the last content and lesson is completed
                if i == len(lesson.Path)-1 && lesson.Completed {
                    completionEvent := *event
                    completionEvent.Action = ActionCompleted
                    completionEvent.Timestamp = event.Timestamp.Add(30 * time.Second)
                    events = append(events, &completionEvent)
                }
            }
        }
        
        // Create events for challenges
        if trail.Challenge != nil {
            for _, phase := range trail.Challenge.Phases {
                for i, content := range phase.Path {
                    event := &ProgressEvent{
                        UserID:    progression.User,
                        TrailID:   trail.ID,
                        ItemID:    phase.Identifier,
                        ItemType:  ProgressTypeChallenge,
                        Action:    ActionStarted,
                        ContentID: content.Identifier,
                        Choice: &Choice{
                            Identifier: content.Choice.Identifier,
                            Next:       content.Choice.Next,
                        },
                        Timestamp: baseTime.Add(time.Duration(len(events)+i) * time.Minute),
                        Migrated:  true,
                        LegacyID:  progression.ObjectID.Hex(),
                    }
                    events = append(events, event)
                    
                    // Add completion event if phase is completed
                    if i == len(phase.Path)-1 && phase.Completed {
                        completionEvent := *event
                        completionEvent.Action = ActionCompleted
                        completionEvent.Timestamp = event.Timestamp.Add(30 * time.Second)
                        events = append(events, &completionEvent)
                    }
                }
            }
        }
    }
    
    return events
}
```

### 3.2 Hybrid Service Implementation

During migration, the service will handle both legacy and new data:

```go
func (s *eventService) FindByUser(ctx context.Context, userID string) (*Progression, error) {
    // Check if user is migrated
    migrated, err := s.repository.IsUserMigrated(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    if !migrated {
        // Trigger migration
        if err := s.migrationService.MigrateUserData(ctx, userID); err != nil {
            // Fallback to legacy data
            return s.repository.GetLegacyProgression(ctx, userID)
        }
    }
    
    // Get data from new system and convert to legacy format
    summary, err := s.GetUserProgress(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    return s.convertSummaryToLegacy(summary), nil
}
```

## Phase 4: Controller Compatibility (Week 4)

### 4.1 Maintain Existing Endpoints

All existing controller methods remain unchanged but use the new service implementation:

```go
func (tc *controller) LegacyCreate() echo.HandlerFunc {
    return func(c echo.Context) error {
        ctx := c.Request().Context()
        var body progression.ProgressionBody
        
        if err := c.Bind(&body); err != nil {
            return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
        }
        
        userToken, err := token.GetClaimsFromRequest(c.Request())
        if err != nil {
            return err
        }
        
        // New implementation handles this transparently
        err = tc.Service.RecordProgress(ctx, userToken.Uid, &body)
        if err != nil {
            return err
        }
        
        return c.JSON(http.StatusNoContent, nil)
    }
}
```

## Phase 5: Testing and Validation (Week 5)

### 5.1 Migration Testing
- Test data migration for all existing users
- Validate that converted events produce identical progress calculations
- Performance testing for event queries vs legacy queries

### 5.2 Integration Testing
- All existing API endpoints return identical responses
- New event-driven system handles edge cases correctly
- Cache invalidation works properly

### 5.3 Rollback Plan
- Keep legacy collections during migration period
- Feature flag to switch between implementations
- Automated data consistency checks

## Migration Timeline

| Week | Phase | Tasks |
|------|-------|-------|
| 1 | Foundation | New models, repository interface, event store |
| 2 | Service Layer | Calculator, event service, progress logic |
| 3 | Migration | Data migration service, hybrid implementation |
| 4 | Integration | Controller updates, endpoint compatibility |
| 5 | Testing | Migration testing, validation, performance |
| 6 | Deployment | Gradual rollout, monitoring, cleanup |

## Success Criteria

1. **Zero API Breaking Changes**: All existing endpoints work identically
2. **Data Integrity**: All user progress data preserved during migration
3. **Performance**: New system performs comparably or better
4. **Maintainability**: Codebase reduced by 70%+ lines
5. **Reliability**: Zero data loss during migration

## Risk Mitigation

1. **Gradual Migration**: Migrate users in batches
2. **Feature Flags**: Ability to rollback to legacy system
3. **Data Validation**: Automated checks for data consistency
4. **Monitoring**: Detailed logging during migration period
5. **Backup Strategy**: Full database backups before migration waves

This migration plan ensures a smooth transition to the new event-driven architecture while maintaining complete backward compatibility and zero downtime.
