# Challenge System Documentation

## Overview

The Challenge System is a core component of the Dinbora Backend that manages interactive learning challenges within trails. Challenges consist of multiple phases, each containing content with choices that users can select to progress through the learning experience.

## Architecture

### Core Components

1. **Challenge Structure**
   - `Challenge`: Main challenge container
   - `ChallengePhase`: Individual phases within a challenge
   - `ChallengeContent`: Content items within phases
   - `ChallengeChoice`: User choice options

2. **Progression Tracking**
   - `Trail`: User's progress through challenges
   - `ChallengePhase`: Phase-level progression
   - `ChallengeContent`: Content-level progression

## Data Models

### Challenge Content Model

```go
type Challenge struct {
    Name        string            `json:"name" bson:"name"`
    Identifier  string            `json:"identifier" bson:"identifier"`
    Description string            `json:"description" bson:"description"`
    Logo        string            `json:"logo" bson:"logo"`
    Color       string            `json:"color" bson:"color"`
    Phases      []*ChallengePhase `json:"phases" bson:"phases"`
    Locked      bool              `json:"locked" bson:"locked"`
}

type ChallengePhase struct {
    Name         string               `json:"name" bson:"name"`
    Identifier   string               `json:"identifier" bson:"identifier"`
    Description  string               `json:"description" bson:"description"`
    Requirements []string             `json:"requirements" bson:"requirements"`
    Order        uint8                `json:"order" bson:"order"`
    Logo         string               `json:"logo" bson:"logo"`
    Color        string               `json:"color" bson:"color"`
    Advertising  ChallengeAdvertising `json:"advertising" bson:"advertising"`
    Content      []*ChallengeContent  `json:"content" bson:"content"`
}

type ChallengeContent struct {
    Image       string             `json:"image,omitempty" bson:"image,omitempty"`
    Identifier  string             `json:"identifier" bson:"identifier"`
    Description string             `json:"description" bson:"description"`
    Next        string             `json:"next,omitempty" bson:"next,omitempty"`
    Choices     []*ChallengeChoice `json:"choices,omitempty" bson:"choices,omitempty"`
}

type ChallengeChoice struct {
    Name       string             `json:"name"`
    Identifier string             `json:"identifier"`
    Type       string             `json:"type"`
    Next       string             `json:"next,omitempty"`
    Points     int                `json:"points,omitempty" bson:"points,omitempty"`
    Choices    []*ChallengeChoice `json:"choices,omitempty"`
}
```

### Progression Model

```go
type Challenge struct {
    Identifier string            `json:"identifier,omitempty" bson:"identifier"`
    Phases     []*ChallengePhase `json:"phases,omitempty" bson:"phases"`
    Current    string            `json:"current,omitempty" bson:"current"`
    Completed  bool              `json:"completed,omitempty" bson:"completed"`
    Total      uint8             `json:"total" bson:"total"`
    Available  bool              `json:"available"`
    Rewarded   bool              `json:"rewarded" bson:"rewarded"`
}

type Trail struct {
    ID                 string     `json:"id" bson:"id"`
    Lessons            []*Lesson  `json:"lessons,omitempty" bson:"lessons,omitempty"`
    Challenge          *Challenge `json:"challenge,omitempty" bson:"challenge,omitempty"`
    Current            string     `json:"current,omitempty" bson:"current,omitempty"`
    Total              uint8      `json:"total" bson:"total,omitempty"`
    Available          bool       `json:"available" bson:"available"`
    LessonsCompleted   bool       `json:"lessonsCompleted" bson:"lessonsCompleted"`
    ChallengeCompleted bool       `json:"challengeCompleted" bson:"challengeCompleted"`
    Rewarded           bool       `json:"rewarded" bson:"rewarded"`
    CurrentPoints      int        `json:"currentPoints" bson:"currentPoints"`
}
```

## Points System

### Overview

The points system allows challenges to award points to users based on their choices throughout the challenge progression. Points accumulate at the trail level and provide a scoring mechanism for user engagement.

### Points Implementation

#### 1. **Choice Points Configuration**

Points are configured in the challenge content JSON structure:

```json
{
  "name": "Financial Decision Challenge",
  "identifier": "financial_decisions",
  "phases": [
    {
      "name": "Investment Choices",
      "identifier": "investment_phase",
      "content": [
        {
          "identifier": "investment_question",
          "description": "What investment strategy do you prefer?",
          "choices": [
            {
              "name": "Conservative Savings",
              "identifier": "conservative",
              "points": 0,
              "next": "conservative_result"
            },
            {
              "name": "Balanced Portfolio",
              "identifier": "balanced",
              "points": 5,
              "next": "balanced_result"
            },
            {
              "name": "Growth Investments",
              "identifier": "growth",
              "points": 10,
              "next": "growth_result"
            },
            {
              "name": "High-Risk/High-Reward",
              "identifier": "high_risk",
              "points": 20,
              "next": "risk_result"
            }
          ]
        }
      ]
    }
  ]
}
```

#### 2. **Points Accumulation**

- Points are stored in the `Trail.CurrentPoints` field
- Points accumulate as users progress through challenge content
- Points are calculated and updated when users make choices
- Final point total represents the sum of all points earned throughout the challenge

#### 3. **Points Calculation Flow**

```mermaid
graph TD
    A[User Makes Choice] --> B[Progression Service]
    B --> C[Validate Choice]
    C --> D[Lookup Choice Points]
    D --> E[Add Points to Trail.CurrentPoints]
    E --> F[Update Database]
    F --> G[Return Updated Progress]
    
    H[Challenge Content] --> I[ChallengeChoice.Points]
    I --> D
```

#### 4. **API Integration**

Points are included in progression responses:

```json
{
  "trails": [
    {
      "id": "trail_123",
      "currentPoints": 25,
      "total": 90,
      "challenge": {
        "identifier": "financial_decisions",
        "completed": false,
        "total": 50
      }
    }
  ]
}
```

## Challenge Flow

### 1. **Prerequisites**

Before starting a challenge:
- All lessons in the trail must be completed (`LessonsCompleted = true`)
- User must have completed prerequisite trails (90% completion minimum)

### 2. **Phase Progression**

- Challenges consist of multiple phases that must be completed sequentially
- Each phase has requirements that must be met before access
- Requirements can include completed lessons or previous challenge phases

### 3. **Content Navigation**

- Users progress through content within phases
- Each content item can have multiple choice options
- Choices determine the next content item and points awarded
- Content can end with rewards (diamond/coin) or lead to more content

### 4. **Completion Criteria**

- A challenge phase is completed when the user reaches a reward endpoint
- A challenge is completed when all phases are completed
- Trail completion percentage updates based on challenge progress

## Validation Rules

### Challenge Structure Validation

1. **Required Fields**
   - Challenge: name, identifier, logo, phases
   - Phase: name, identifier, logo, content, requirements
   - Content: description, identifier
   - Choice: name, identifier, type

2. **Content Flow Validation**
   - All content must have either a `next` field or choices
   - All choices must have valid `next` values
   - Next values must reference valid content identifiers or rewards

3. **Points Validation**
   - Points field is optional (defaults to 0)
   - Points must be non-negative integers
   - Points are validated during content creation/update

### Progression Validation

1. **Access Control**
   - Users cannot access challenges until lessons are completed
   - Phase requirements must be met before access
   - Content progression must follow defined paths

2. **Choice Validation**
   - Selected choices must exist in the content structure
   - Users cannot skip required content progression
   - Invalid choices result in validation errors

## Error Handling

### Common Error Types

- `ErrInvalidChallengeProgress`: User attempted invalid challenge progression
- `ErrInvalidChallengePhaseProgress`: User attempted invalid phase progression
- `ErrInvalidContent`: Referenced content does not exist
- `ErrInvalidChallenge`: Challenge structure is invalid

### Error Response Structure

```json
{
  "error": {
    "code": "INVALID_CHALLENGE_PROGRESS",
    "message": "User tried a challenge without finishing all lessons",
    "context": {
      "trail": "trail_123",
      "challenge": "financial_decisions",
      "lessons_completed": false
    }
  }
}
```

## File Structure

```
internal/
├── model/
│   ├── content/
│   │   ├── challenge.go          # Challenge content models
│   │   └── errors.go             # Content validation errors
│   └── progression/
│       ├── challenge.go          # Challenge progression models
│       ├── trail.go              # Trail progression with points
│       └── progression.go        # Main progression logic
├── service/
│   └── progression/
│       └── service.go            # Progression business logic
└── controller/
    └── progression/
        └── controller.go         # API endpoints
```

## JSON Model Structure

```json
{
  "name": "Challenge Name",
  "identifier": "challenge_identifier",
  "description": "Challenge description",
  "logo": "logo_url",
  "color": "#hex_color",
  "locked": false,
  "phases": [
    {
      "name": "Phase Name",
      "identifier": "phase_identifier",
      "description": "Phase description",
      "requirements": ["lesson_id", "previous_phase_id"],
      "order": 1,
      "logo": "phase_logo_url",
      "color": "#phase_color",
      "advertising": {
        "banner": "banner_url",
        "bannerURL": "banner_link_url"
      },
      "content": [
        {
          "image": "content_image_url",
          "identifier": "content_identifier",
          "description": "Content description",
          "next": "next_content_id_or_reward",
          "choices": [
            {
              "name": "Choice Name",
              "identifier": "choice_identifier",
              "type": "TEXT|NUMBER|OPTIONS|DATE",
              "points": 10,
              "next": "next_content_id_or_reward",
              "choices": []
            }
          ]
        }
      ]
    }
  ]
}
```

## Best Practices

### Content Design

1. **Clear Progression Paths**
   - Design logical content flow with clear next steps
   - Avoid circular references in content navigation
   - Provide meaningful choices that impact learning outcomes

2. **Points Assignment**
   - Assign points based on choice difficulty or learning value
   - Consider progressive point increases for advanced choices
   - Balance point distribution across challenge phases

3. **Requirement Management**
   - Set appropriate prerequisites for challenge access
   - Use phase requirements to ensure proper learning sequence
   - Test requirement chains thoroughly

### Development Guidelines

1. **Validation First**
   - Always validate challenge structure before storage
   - Implement comprehensive progression validation
   - Handle edge cases gracefully

2. **Performance Considerations**
   - Cache challenge content for frequently accessed trails
   - Optimize progression queries for large user bases
   - Consider indexing on challenge identifiers

3. **Testing Strategy**
   - Test all progression paths through challenges
   - Validate points calculation accuracy
   - Test requirement enforcement thoroughly

## Migration Considerations

### Adding Points to Existing Challenges

1. **Backward Compatibility**
   - Points field is optional (defaults to 0)
   - Existing challenges continue to work without modification
   - Users' existing progression remains valid

2. **Data Migration**
   - No database migration required for existing progression data
   - `CurrentPoints` field defaults to 0 for existing trails
   - Points can be added to challenge content incrementally

3. **API Compatibility**
   - New points field included in responses but doesn't break existing clients
   - Points endpoints can be added without affecting existing functionality
   - Progressive enhancement approach for point-aware clients
