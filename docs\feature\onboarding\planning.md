Este documento descreve o fluxo padrão para implementação da funcionalidade de **Upload de Fotos no Onboarding**.

---

## 1. Coleta e Análise de Requisitos

### 1.1 Solicitação de Feature
- **Template de Solicitação** (obrigatório):
  Consulte o arquivo `template.md` nesta pasta para o detalhamento da solicitação da feature "Upload de Fotos no Onboarding".
  (Caminho: `docs/feature/onboarding/template.md`)

### 1.2 Priorização
- Método: **MoSCoW** (Must have, Should have, Could have, Won't have).
  - **Must have**: Upload de JPG/PNG, validação de tamanho/tipo, preview, armazenamento S3, associação ao perfil, opção de pular.
  - **Should have**: Feedback claro de erro, interface amigável.
  - **Could have**: Ferramenta de recorte/redimensionamento no frontend.
- Envolvimento do Product Owner e stakeholders técnicos para refinar a priorização.

---

## 2. Planejamento e Design

### 2.1 Especificação Técnica
- Documento de design (**Tech Spec**) para a funcionalidade de Upload de Fotos no Onboarding:
  - **Arquitetura da Solução**:
    - Frontend: Componente React/Vue/Angular para upload de imagem, com chamadas para API backend.
    - Backend (Go): Endpoint para receber `multipart/form-data`, validar arquivo, fazer upload para S3, e atualizar o `User` model com a URL da imagem.
  - **Diagramas**:
    ```mermaid
    sequenceDiagram
        Participante Usuário
        Participante Frontend
        Participante Backend API
        Participante AWS S3

        Usuário->>Frontend: Seleciona imagem
        Frontend->>Frontend: Exibe preview
        Usuário->>Frontend: Confirma upload
        Frontend->>Backend API: POST /onboarding/upload-photo (multipart/form-data)
        Backend API->>Backend API: Valida arquivo (tipo, tamanho)
        alt Arquivo Válido
            Backend API->>AWS S3: Upload da imagem
            AWS S3-->>Backend API: URL da imagem
            Backend API->>Backend API: Salva URL no perfil do usuário (MongoDB)
            Backend API-->>Frontend: Sucesso (HTTP 200)
        else Arquivo Inválido
            Backend API-->>Frontend: Erro (HTTP 400/422)
        end
        Frontend->>Usuário: Exibe mensagem de sucesso/erro
    ```
  - **Model de Dados (User)**:
    - Adicionar campo `PhotoURL string` ao `internal/model/user.go`.
  - **Endpoints da API**:
    - `POST /api/v1/onboarding/photo`: Recebe a imagem, processa e associa ao usuário (requer autenticação parcial ou identificador de sessão do onboarding).
  - **Dependências Técnicas**:
    - AWS SDK para Go.
    - Biblioteca de frontend para manipulação de formulários e uploads.

### 2.2 Definição de Tarefas
- Quebrar a feature em subtarefas no backlog:
  ```markdown
  **Backend**:
  - [ ] Modificar `internal/model/user.go` para incluir `PhotoURL`.
  - [ ] Criar novo endpoint em `internal/controller/onboarding/controller.go` (ou similar) para `POST /api/v1/onboarding/photo`.
  - [ ] Implementar lógica no `internal/service/onboarding/service.go` para:
    - [ ] Validar tipo e tamanho do arquivo.
    - [ ] Fazer upload do arquivo para o S3 (reutilizar/adaptar `internal/service/s3/service.go`).
    - [ ] Atualizar o registro do usuário com a `PhotoURL`.
  - [ ] Adicionar testes unitários para o novo serviço e controller.
  - [ ] Adicionar testes de integração para o endpoint.

  **Frontend**:
  - [ ] Criar componente de UI para upload de foto com preview.
  - [ ] Integrar componente na etapa de onboarding.
  - [ ] Implementar lógica para chamar API backend.
  - [ ] Tratar respostas de sucesso e erro da API.
  - [ ] Implementar opção de "pular" etapa.
  - [ ] Adicionar testes unitários para o componente.
  - [ ] Adicionar testes E2E para o fluxo de upload no onboarding.

  **Infraestrutura**:
  - [ ] Garantir que o bucket S3 e as permissões estejam configurados corretamente.

  **Documentação**:
  - [ ] Atualizar documentação da API (Swagger/OpenAPI).
  - [ ] Documentar a nova funcionalidade para o usuário (se aplicável).
  ```

---

## 3. Desenvolvimento

### 3.1 Versionamento de Código
- Siga uma estratégia de branches:
  ```bash
  git checkout -b feature/onboarding-photo-upload
  ```
- **Fluxo Recomendado**: GitHub Flow.

### 3.2 Padrões de Código
- Atenda às diretrizes do projeto Dinbora:
  - Clean Code.
  - Testes unitários/integração (Go testing package, Jest/React Testing Library no frontend).
  - Documentação interna (comentários/Godoc).
  - Seguir a arquitetura existente (controllers, services, repositories).

### 3.3 Revisão de Código
- Abra um **Pull Request** (PR) para `main` ou `develop` branch.
- Peça revisão de pelo menos um outro desenvolvedor.
  - Use *code review* para validar padrões, lógica, segurança e cobertura de testes.
  - Ferramentas: GitHub.

---

## 4. Testes

### 4.1 Tipos de Testes
| Tipo de Teste       | Ferramentas Exemplo      | Responsável |
|----------------------|--------------------------|-------------|
| Unitários (Backend)  | Go `testing`             | Dev Backend |
| Unitários (Frontend) | Jest, RTL                | Dev Frontend|
| Integração (API)     | Go `httptest`            | Dev Backend |
| E2E (End-to-End)     | Cypress, Playwright      | QA/Dev      |
| Testes Manuais (UX)  | Navegador                | QA/PO       |

### 4.2 Pipeline de CI/CD
- Configuração no `.github/workflows/main.yml` deve incluir:
  ```yaml
  # ... (configurações existentes)
  jobs:
    test:
      # ...
      steps:
        # ...
        - name: Run Backend Unit Tests
          run: make test-unit-backend # Comando exemplo
        - name: Run Frontend Unit Tests
          run: make test-unit-frontend # Comando exemplo
    # ... (outros jobs como build, deploy)
  ```
- Garantir que os testes da nova funcionalidade sejam executados no pipeline.

---

## 5. Deploy e Monitoramento

### 5.1 Estratégias de Deploy
- **Feature Flags**: Considerar para liberação progressiva, se a funcionalidade for complexa ou de alto risco (para esta feature, pode ser opcional).
- **Blue/Green ou Canary Release**: Se aplicável à estratégia geral de deploy do Dinbora.

### 5.2 Monitoramento Pós-Deploy
- Configure alertas para métricas relacionadas ao upload de fotos:
  - Taxa de erro no endpoint de upload.
  - Latência do endpoint de upload.
  - Número de uploads bem-sucedidos vs. tentativas.
  - Uso do armazenamento S3.
- Logs centralizados (ex.: ELK Stack, Datadog) para investigar falhas.

---

## 6. Pós-Implantação

### 6.1 Feedback e Iteração
- Colete dados de uso:
  - Quantos usuários fazem upload de foto vs. pulam.
  - Taxa de sucesso dos uploads.
- Colete feedback qualitativo dos usuários sobre a experiência.
- Ajuste a feature com base no feedback e dados (ex: aumentar limite de tamanho, melhorar UI).

### 6.2 Documentação
- Atualize:
  - Documentação técnica interna (ex: Confluence, READMEs).
  - Documentação da API (Swagger/OpenAPI).
  - Guias do usuário ou FAQs, se a funcionalidade for complexa para o usuário final.

### 6.3 Retrospectiva
- Discuta com a equipe após a liberação:
  - ✅ O que funcionou bem no desenvolvimento e lançamento?
  - ❌ O que pode ser melhorado para futuras features?
  - 🤔 Quais aprendizados foram obtidos?

---

## 7. Manutenção Contínua

- **Refatoração**: Melhore o código da funcionalidade conforme necessário para manter a qualidade.
- **Atualizações**: Mantenha dependências (AWS SDK, bibliotecas de frontend) seguras e atualizadas.
- **Monitoramento de Custos**: Acompanhar custos de S3 relacionados ao armazenamento das fotos.

---

## Ferramentas Recomendadas (Conforme Padrão do Projeto)

| Categoria           | Ferramentas                              |
|---------------------|------------------------------------------|
| Gestão de Projetos  | Trello                                   |
| CI/CD               | GitHub Actions, Dokploy                  |
| Monitoramento       | Prometheus, Grafana                      |
| Armazenamento       | AWS S3                                   |

---

## Fluxo Visual Geral (Para esta Feature)

```plaintext
Ideia (Upload de Foto) → Análise (Requisitos) → Especificação (Tech Spec) → Desenvolvimento (Backend/Frontend) → Testes (Unit, Int, E2E) → Deploy → Monitoramento (Erros, Uso) → Feedback (Iteração)
```

---

**Contribuidores**: [Igor Seixas, Leonardo Humberto]  
**Última Atualização**: 16/05/2025
