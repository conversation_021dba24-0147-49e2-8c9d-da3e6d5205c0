package model

type DeleteReason struct {
	Predefined Predefined `json:"predefined" bson:"predefined"`
	Other      string     `json:"other" bson:"other"`
}

type Predefined byte

const (
	UndefinedPreDefined Predefined = iota
	AlreadyInvest
	InvestInAnotherPlace
	IsNotWhatExpected
	FinancialProblems
	DoNotLikeToInvest
	OtherPredefined
)

func (p Predefined) String() string {
	switch p {
	case AlreadyInvest:
		return "Already invest"
	case InvestInAnotherPlace:
		return "Invest in another place"
	case IsNotWhatExpected:
		return "Isn't not what I expected"
	case FinancialProblems:
		return "Finacial problems"
	case DoNotLikeToInvest:
		return "Do not like to invest"
	case OtherPredefined:
		return "Other"
	}

	return "Undefined predefined reason"
}

func (p Predefined) IsValid() error {
	switch p {
	case AlreadyInvest, InvestInAnotherPlace, IsNotWhatExpected, FinancialProblems, DoNotLikeToInvest, OtherPredefined:
		return nil
	}

	return ErrInvalidDeleteReason
}
