package auth

type LoginRequestDTO struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
	FCMToken string `json:"fcm"`
}

type LoginResponseDTO struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	LastName     string `json:"lastName"`
	Email        string `json:"email"`
	PhotoURL     string `json:"photo"`
	ReferralCode string `json:"referralCode"`
	Access       string `json:"access"`
	Refresh      string `json:"refresh"`
}

type RefreshResponseDTO struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	LastName     string `json:"lastName"`
	Email        string `json:"email"`
	PhotoURL     string `json:"photo"`
	ReferralCode string `json:"referralCode"`
	Access       string `json:"access"`
	Refresh      string `json:"refresh"`
}
