package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Dream Management
func (m mongoDB) CreateDream(ctx context.Context, boardID primitive.ObjectID, dream *dreamboard.Dream) error {
	dream.ObjectID = primitive.NewObjectID()

	update := bson.D{
		{Key: "$push", Value: bson.D{{Key: "dreams", Value: dream}}},
		{Key: "$set", Value: bson.D{{Key: "updatedAt", Value: time.Now()}}},
	}

	_, err := m.collection.UpdateByID(ctx, boardID, update)
	if err != nil {
		return errors.New(errors.Repository, errors.DreamAddFailed, errors.Internal, err)
	}

	// Convert ObjectID to hex string for external use
	dream.ID = dream.ObjectID.Hex()
	return nil
}

func (m mongoDB) FindDream(ctx context.Context, boardID, dreamID primitive.ObjectID) (*dreamboard.Dream, error) {
	var dreamboard dreamboard.Dreamboard
	filter := bson.D{
		{Key: "_id", Value: boardID},
		{Key: "dreams._id", Value: dreamID},
	}
	// Use projection to only return the matching dream
	opts := options.FindOne().SetProjection(bson.D{
		{Key: "dreams.$", Value: 1},
	})

	err := m.collection.FindOne(ctx, filter, opts).Decode(&dreamboard)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.DreamNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.DreamboardFindFailed, errors.Internal, err)
	}

	if len(dreamboard.Dreams) > 0 {
		return dreamboard.Dreams[0], nil
	}

	return nil, errors.New(errors.Repository, errors.DreamNotFound, errors.NotFound, nil)
}

func (m mongoDB) UpdateDream(ctx context.Context, boardID primitive.ObjectID, dream *dreamboard.Dream) error {
	dream.UpdatedAt = time.Now()

	update := bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "dreams.$[elem]", Value: dream},
			{Key: "updatedAt", Value: time.Now()},
		}},
	}

	opts := options.Update().SetArrayFilters(options.ArrayFilters{
		Filters: []interface{}{bson.M{"elem._id": dream.ObjectID}},
	})

	result, err := m.collection.UpdateByID(ctx, boardID, update, opts)
	if err != nil {
		return errors.New(errors.Repository, errors.DreamUpdateFailed, errors.Internal, err)
	}
	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, errors.DreamNotFound, errors.NotFound, nil)
	}
	return nil
}

func (m mongoDB) RemoveDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) error {
	update := bson.D{
		{Key: "$pull", Value: bson.D{{Key: "dreams", Value: bson.D{{Key: "_id", Value: dreamID}}}}},
		{Key: "$set", Value: bson.D{{Key: "updatedAt", Value: time.Now()}}},
	}

	result, err := m.collection.UpdateByID(ctx, boardID, update)
	if err != nil {
		return errors.New(errors.Repository, errors.DreamRemoveFailed, errors.Internal, err)
	}
	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, errors.DreamboardNotFound, errors.NotFound, nil)
	}
	return nil
}
