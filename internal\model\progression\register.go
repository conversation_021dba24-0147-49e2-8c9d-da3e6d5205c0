package progression

import (
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
)

// registerLessonProgress registers progress for a lesson
func (p *Progression) registerLessonProgress(body *ProgressionBody, trailContent *content.Trail) {
	currentContent := trailContent.GetLesson(body.Module).GetNext(body.Content, body.Choice.Identifier)

	if len(p.Trails) == 0 {
		// First progress for this user
		p.initializeFirstLessonProgress(body, currentContent)
	} else {
		trailProgression := p.GetTrailProgression(body.Trail)

		if trailProgression == nil {
			// First progress for this trail
			p.initializeTrailWithLessonProgress(body, currentContent)
		} else {
			lessonProgression := p.GetLessonProgression(body.Trail, body.Module)
			if lessonProgression != nil {
				// Update existing lesson progress
				p.updateExistingLessonProgress(body, currentContent, lessonProgression)
			} else {
				// First progress for this lesson
				p.addNewLessonToTrail(body, currentContent, trailProgression)
			}
		}
	}
}

// registerChallengeProgress registers progress for a challenge
func (p *Progression) registerChallengeProgress(body *ProgressionBody, trailContent *content.Trail) {
	trailProgression := p.GetTrailProgression(body.Trail)
	if trailProgression == nil {
		return
	}

	phase := trailContent.Challenge.GetPhase(body.Module)
	if phase == nil {
		return
	}

	currentContent := phase.GetNext(body.Content, body.Choice.Identifier)
	challengeProgression := trailProgression.Challenge

	if challengeProgression == nil || len(challengeProgression.Phases) == 0 {
		// First progress for this challenge
		p.initializeFirstChallengeProgress(body, currentContent, trailContent, trailProgression)
	} else {
		phaseProgression := p.GetChallengePhaseProgression(body.Trail, body.Module)
		if phaseProgression != nil {
			// Check if this is the first content item of the phase
			phaseContentFromTrail := trailContent.Challenge.GetPhase(body.Module) // Get the phase definition
			if phaseContentFromTrail != nil && len(phaseContentFromTrail.Content) > 0 && phaseContentFromTrail.Content[0].Identifier == body.Content {
				phaseProgression.ResetPoints() // Reset points if it's the first content of a restarted phase
			}
			// Update existing phase progress
			p.updateExistingPhaseProgress(body, currentContent, phaseProgression)
		} else {
			// First progress for this phase
			p.addNewPhaseToChallenge(body, currentContent, challengeProgression)
		}
	}
}

// Helper methods for lesson progress

func (p *Progression) initializeFirstLessonProgress(body *ProgressionBody, currentContent string) {
	lessonProgress := &Lesson{
		Identifier: body.Module,
		Path: []*LessonContent{
			{
				Identifier: body.Content,
				Choice:     body.Choice,
				Timestamp:  body.Timestamp,
			},
		},
		Current:   currentContent,
		Completed: false,
		Available: true,
	}

	p.Trails = []*Trail{
		{
			ID: body.Trail,
			Lessons: []*Lesson{
				lessonProgress,
			},
			Current:   body.Module,
			Total:     0,
			Available: true,
		},
	}
}

func (p *Progression) initializeTrailWithLessonProgress(body *ProgressionBody, currentContent string) {
	lessonProgress := &Lesson{
		Identifier: body.Module,
		Path: []*LessonContent{
			{
				Identifier: body.Content,
				Choice:     body.Choice,
				Timestamp:  body.Timestamp,
			},
		},
		Current:   currentContent,
		Completed: false,
		Available: true,
	}

	trail := &Trail{
		ID: body.Trail,
		Lessons: []*Lesson{
			lessonProgress,
		},
		Current:   body.Module,
		Total:     0,
		Available: true,
	}

	p.Trails = append(p.Trails, trail)
}

func (p *Progression) updateExistingLessonProgress(body *ProgressionBody, currentContent string, lessonProgression *Lesson) {
	lessonProgression.Current = currentContent
	lessonContent := &LessonContent{
		Identifier: body.Content,
		Choice:     body.Choice,
		Timestamp:  body.Timestamp,
	}
	lessonProgression.Path = append(lessonProgression.Path, lessonContent)
}

func (p *Progression) addNewLessonToTrail(body *ProgressionBody, currentContent string, trailProgression *Trail) {
	lessonProgress := &Lesson{
		Identifier: body.Module,
		Path: []*LessonContent{
			{
				Identifier: body.Content,
				Choice:     body.Choice,
				Timestamp:  body.Timestamp,
			},
		},
		Current:   currentContent,
		Completed: false,
		Available: true,
	}

	trailProgression.Current = body.Module
	trailProgression.Lessons = append(trailProgression.Lessons, lessonProgress)
}

// Helper methods for challenge progress

func (p *Progression) initializeFirstChallengeProgress(body *ProgressionBody, currentContent string, trailContent *content.Trail, trailProgression *Trail) {
	challengePhase := &ChallengePhase{
		Identifier: body.Module,
		Path: []*ChallengeContent{
			{
				Identifier: body.Content,
				Choice:     body.Choice,
				Timestamp:  body.Timestamp,
			},
		},
		Current:       currentContent,
		Completed:     false,
		Available:     true,
		CurrentPoints: 0, // Initialize CurrentPoints for the first phase
	}

	challenge := &Challenge{
		Identifier: trailContent.Challenge.Identifier,
		Phases: []*ChallengePhase{
			challengePhase,
		},
		Current:   challengePhase.Identifier,
		Completed: false,
		Available: true,
		Rewarded:  false,
	}

	trailProgression.Current = challenge.Identifier
	trailProgression.Challenge = challenge
}

func (p *Progression) updateExistingPhaseProgress(body *ProgressionBody, currentContent string, phaseProgression *ChallengePhase) {
	phaseProgression.Current = currentContent
	phaseContent := &ChallengeContent{
		Identifier: body.Content,
		Choice:     body.Choice,
		Timestamp:  body.Timestamp,
	}
	phaseProgression.Path = append(phaseProgression.Path, phaseContent)
}

func (p *Progression) addNewPhaseToChallenge(body *ProgressionBody, currentContent string, challengeProgression *Challenge) {
	phaseProgress := &ChallengePhase{
		Identifier: body.Module,
		Path: []*ChallengeContent{
			{
				Identifier: body.Content,
				Choice:     body.Choice,
				Timestamp:  body.Timestamp,
			},
		},
		Current:       currentContent,
		Completed:     false,
		Available:     true,
		CurrentPoints: 0, // Initialize CurrentPoints for a new phase
	}

	challengeProgression.Current = body.Module
	challengeProgression.Phases = append(challengeProgression.Phases, phaseProgress)
}
