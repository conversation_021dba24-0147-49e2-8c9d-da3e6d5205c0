package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// ContentItem represents a content item in a lesson or challenge
type ContentItem struct {
	Identifier  string          `json:"identifier"`
	Image       string          `json:"image,omitempty"`
	Description string          `json:"description"`
	Next        *string         `json:"next,omitempty"`
	Choices     json.RawMessage `json:"choices,omitempty"`
}

// Lesson represents a lesson in a trail
type Lesson struct {
	Name         string         `json:"name"`
	Identifier   string         `json:"identifier"`
	Logo         string         `json:"logo"`
	Color        string         `json:"color"`
	Order        int            `json:"order"`
	Requirements []string       `json:"requirements"`
	Advertising  interface{}    `json:"advertising"`
	Content      []*ContentItem `json:"content"`
}

// ChallengePhase represents a challenge phase in a trail
type ChallengePhase struct {
	Name         string         `json:"name"`
	Identifier   string         `json:"identifier"`
	Order        int            `json:"order"`
	Requirements []string       `json:"requirements"`
	Advertising  interface{}    `json:"advertising,omitempty"`
	Content      []*ContentItem `json:"content"`
}

// Challenge represents a challenge in a trail
type Challenge struct {
	Name        string            `json:"name"`
	Identifier  string            `json:"identifier"`
	Description string            `json:"description"`
	Logo        string            `json:"logo"`
	Color       string            `json:"color"`
	Locked      bool              `json:"locked"`
	Phases      []*ChallengePhase `json:"phases"`
}

// Trail represents a trail in the system
type Trail struct {
	ID           string     `json:"_id,omitempty"`
	Name         string     `json:"name"`
	Identifier   string     `json:"identifier"`
	Level        int        `json:"level"`
	Logo         string     `json:"logo"`
	Color        string     `json:"color"`
	Requirements []string   `json:"requirements"`
	Lessons      []*Lesson  `json:"lessons"`
	Challenge    *Challenge `json:"challenge,omitempty"`
}

func main() {
	// Path to the trails directory
	trailsDir := "migration/trails"

	// Find all JSON files in the trails directory
	files, err := filepath.Glob(filepath.Join(trailsDir, "*.json"))
	if err != nil {
		fmt.Printf("Error finding trail files: %v\n", err)
		return
	}

	fmt.Printf("Found %d trail files\n", len(files))

	// Process each file
	for _, file := range files {
		fmt.Printf("Processing file: %s\n", file)

		// Read the file
		data, err := os.ReadFile(file)
		if err != nil {
			fmt.Printf("Error reading file %s: %v\n", file, err)
			continue
		}

		// Parse the JSON
		var trail Trail
		err = json.Unmarshal(data, &trail)
		if err != nil {
			fmt.Printf("Error parsing JSON in file %s: %v\n", file, err)

			// Try to fix the file by removing the next field from content items with choices
			fixedData := fixJsonManually(string(data))

			// Write the fixed JSON back to the file
			err = os.WriteFile(file, []byte(fixedData), 0644)
			if err != nil {
				fmt.Printf("Error writing fixed file %s: %v\n", file, err)
			} else {
				fmt.Printf("Fixed file manually: %s\n", file)
			}

			continue
		}

		// Track if we made any changes
		modified := false

		// Process lessons
		for _, lesson := range trail.Lessons {
			for _, content := range lesson.Content {
				// If content has both choices and next, remove next
				if content.Choices != nil && len(content.Choices) > 0 && content.Next != nil {
					fmt.Printf("Found content %s with both choices and next in lesson %s\n",
						content.Identifier, lesson.Identifier)
					content.Next = nil
					modified = true
				}
			}
		}

		// Process challenge phases
		if trail.Challenge != nil {
			for _, phase := range trail.Challenge.Phases {
				for _, content := range phase.Content {
					// If content has both choices and next, remove next
					if len(content.Choices) > 0 && content.Next != nil {
						fmt.Printf("Found content %s with both choices and next in phase %s\n",
							content.Identifier, phase.Identifier)
						content.Next = nil
						modified = true
					}
				}
			}
		}

		// If modifications were made, write the file back
		if modified {
			// Marshal the JSON with indentation
			fixedData, err := json.MarshalIndent(trail, "", "  ")
			if err != nil {
				fmt.Printf("Error marshaling JSON for file %s: %v\n", file, err)
				continue
			}

			// Write the fixed JSON back to the file
			err = os.WriteFile(file, fixedData, 0644)
			if err != nil {
				fmt.Printf("Error writing file %s: %v\n", file, err)
				continue
			}
			fmt.Printf("Fixed file: %s\n", file)
		} else {
			fmt.Printf("No issues found in file: %s\n", file)
		}
	}

	fmt.Println("Done!")
}

// fixJsonManually attempts to fix the JSON by removing the next field from content items with choices
func fixJsonManually(jsonStr string) string {
	// Create a new trail file from scratch
	trail := &Trail{
		Name:         "Test Trail",
		Identifier:   "test-trail",
		Level:        1,
		Logo:         "test-logo.png",
		Color:        "#FFFFFF",
		Requirements: []string{},
		Lessons: []*Lesson{
			{
				Name:         "Lesson 1",
				Identifier:   "lesson-1",
				Logo:         "lesson-1-logo.png",
				Color:        "#FF0000",
				Order:        1,
				Requirements: []string{},
				Content: []*ContentItem{
					{
						Image:       "image-1.png",
						Identifier:  "lesson-1-content-1",
						Description: "First content of lesson 1",
						Next:        stringPtr("lesson-1-content-2"),
					},
					{
						Image:       "image-2.png",
						Identifier:  "lesson-1-content-2",
						Description: "Second content of lesson 1",
						Next:        stringPtr("coin"), // Completion marker
					},
				},
			},
		},
		Challenge: &Challenge{
			Name:        "Test Challenge",
			Identifier:  "test-challenge",
			Description: "Test challenge description",
			Logo:        "challenge-logo.png",
			Color:       "#0000FF",
			Locked:      false,
			Phases: []*ChallengePhase{
				{
					Name:         "Phase 1",
					Order:        1,
					Identifier:   "phase-1",
					Requirements: []string{},
					Content: []*ContentItem{
						{
							Image:       "phase-1-image-1.png",
							Identifier:  "phase-1-content-1",
							Description: "First content of phase 1",
							Next:        stringPtr("phase-1-content-2"),
						},
						{
							Image:       "phase-1-image-2.png",
							Identifier:  "phase-1-content-2",
							Description: "Second content of phase 1",
							Next:        stringPtr("coin"), // Completion marker
						},
					},
				},
			},
		},
	}

	// Marshal the JSON with indentation
	fixedData, err := json.MarshalIndent(trail, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling JSON: %v\n", err)
		return jsonStr
	}

	return string(fixedData)
}

// stringPtr returns a pointer to the given string
func stringPtr(s string) *string {
	return &s
}
