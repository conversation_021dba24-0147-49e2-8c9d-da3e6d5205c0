# Cache Headers Improvements

Caption: This document outlines architectural suggestions for improving the usage of cache headers in the codebase.
⏳ - Pending
✅ - Completed
🚧 - In Progress

## Cache Headers - ⏳
Issue: Since some content doesn't change often, we can use cache headers on requests so that the client can cache the response.
- This will reduce the number of requests to the server and improve performance.
- Solution: Use the `Cache-Control` header to specify how long the response can be cached. (Needs more research)
- This can be done in the API layer or in the controller layer.
- Every GET HTTP response is automatically cached but when a new request is made, the server will check if the content has changed.
- If the content has not changed, the server will return a 304 Not Modified response.
- This will reduce the amount of data sent over the network, but the server will still have to process the request.
- Using cache headers will allow the client to cache the response (on disk or memory) and reduce the number of requests to the server.

## Centralized Cache - ✅
Issue: Memory cache was being implemented in multiple places (e.g., `content/trail` service), leading to code duplication and maintenance difficulties.
- Solution: A centralized cache service has been created and implemented.
- Status: Completed (Initial in-memory implementation).

### Implementation Details

1.  **New Package:**
    -   A new package `internal/cache` was created to house the centralized caching logic.

2.  **Cache Interface (`internal/cache/cache.go`):**
    -   A `CacheService` interface defines the contract for cache operations:
        ```go
        type CacheService interface {
            Get(ctx context.Context, key string) (interface{}, bool)
            Set(ctx context.Context, key string, value interface{}, duration time.Duration) error
            Delete(ctx context.Context, key string) error
        }
        ```
    -   This abstraction allows swapping cache implementations (e.g., in-memory, Redis) in the future without changing the services that use it.

3.  **In-Memory Implementation (`internal/cache/memory.go`):**
    -   An `InMemoryCache` struct implements the `CacheService` interface using a thread-safe map (`sync.RWMutex`).
    -   It supports default expiration times for items and periodic cleanup of expired items.
    -   Constructor: `NewInMemoryCache(defaultExpiration, cleanupInterval time.Duration) *InMemoryCache`
        -   Initializes the cache with a default TTL and a background goroutine for cleanup.
    -   Methods:
        -   `Get`: Retrieves an item, checking for existence and expiration (lazy deletion).
        -   `Set`: Adds/updates an item. A `duration` of 0 uses the default expiration. Negative/zero duration (with no default) makes the item permanent.
        -   `Delete`: Removes an item explicitly.

4.  **Service Refactoring (Example: `internal/service/content/trail/service.go`):**
    -   Services that previously managed their own cache now receive the `CacheService` via dependency injection in their constructor (e.g., `trail.New(repository, cacheService)`).
    -   Internal cache fields (maps, mutexes) were removed.
    -   All cache operations (`Get`, `Set`, `Delete`) are now delegated to the injected `CacheService` instance.
    -   Specific cache key generation functions (e.g., `trailIDKey`, `trailIdentifierKey`, `trailAllKey`) were introduced within the service package to ensure consistent key naming.

5.  **Initialization (`internal/api/service/service.go`):**
    -   The `ServiceContainer.Initialize` function now creates an instance of `InMemoryCache` (e.g., `cache.NewInMemoryCache(1*time.Hour, 10*time.Minute)`).
    -   This instance is then passed to the constructors of services requiring caching (like `trailService`).

### Benefits
-   **Reduced Duplication:** Cache logic is centralized, removing redundant code from multiple services.
-   **Improved Maintainability:** Cache behavior is managed in one place.
-   **Flexibility:** Easier to switch to a different cache backend (like Redis) by creating a new implementation of `CacheService` and updating the initialization code.
-   **Consistency:** Ensures uniform cache behavior across different parts of the application.
