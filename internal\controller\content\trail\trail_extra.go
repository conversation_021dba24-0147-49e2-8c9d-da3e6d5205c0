package trail

import (
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/labstack/echo/v4"
)

// Extra Trails CRUD

func (tc *controller) CreateExtraTrail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var trail content.Trail
		if err := c.Bind(&trail); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := tc.Service.CreateExtraTrail(ctx, &trail); err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, trail.Sanitize())
	}
}

func (tc *controller) FindExtraTrail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trail, err := tc.Service.FindExtraTrail(ctx, trailID, userClassification)
		if err != nil {
			return err
		}

		if !trail.HasAccess(userClassification) {
			return errors.New(errors.Controller, "access denied", errors.Forbidden, nil)
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *controller) FindAllExtraTrails() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trails, err := tc.Service.FindAllExtraTrails(ctx, userClassification)
		if err != nil {
			return err
		}

		// Sanitize trails
		sanitizedTrails := make([]*content.Trail, 0, len(trails))
		for _, trail := range trails {
			if !trail.HasAccess(userClassification) {
				continue
			}
			sanitizedTrails = append(sanitizedTrails, trail.Sanitize())
		}

		return c.JSON(http.StatusOK, sanitizedTrails)
	}
}

func (tc *controller) FindExtraTrailByIdentifier() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		identifier := c.Param("identifier")

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trail, err := tc.Service.FindExtraTrailByIdentifier(ctx, identifier, userClassification)
		if err != nil {
			return err
		}

		if !trail.HasAccess(userClassification) {
			return errors.New(errors.Controller, "access denied", errors.Forbidden, nil)
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *controller) UpdateExtraTrail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")

		trail := &content.Trail{
			ID: trailID,
		}
		if err := c.Bind(&trail); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := tc.Service.UpdateExtraTrail(ctx, trail); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *controller) PatchExtraTrail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")

		trail, err := tc.Service.FindExtraTrail(ctx, trailID, "admin")
		if err != nil {
			return err
		}

		var newTrail content.Trail
		if err = c.Bind(&newTrail); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err = trail.PrepareUpdate(&newTrail); err != nil {
			return err
		}

		if err = tc.Service.UpdateExtraTrail(ctx, trail); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *controller) DeleteExtraTrail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")

		if err := tc.Service.DeleteExtraTrail(ctx, trailID); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Card CRUD

func (tc *controller) FindExtraTrailCardData() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		cardData, err := tc.Service.FindExtraTrailCardData(ctx, trailID, userClassification)
		if err != nil {
			return err
		}

		if !cardData.AccessControl.HasAccess(userClassification) {
			return errors.New(errors.Controller, "access denied", errors.Forbidden, nil)
		}

		return c.JSON(http.StatusOK, cardData)
	}
}

func (tc *controller) FindAllExtraTrailsCardData() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trailsCardData, err := tc.Service.FindAllExtraTrailsCardData(ctx, userClassification)
		if err != nil {
			return err
		}

		// Sanitize trails
		sanitizedTrailsCardData := make([]*content.TrailCard, 0, len(trailsCardData))
		for _, trailCardData := range trailsCardData {
			if !trailCardData.AccessControl.HasAccess(userClassification) {
				continue
			}
			sanitizedTrailsCardData = append(sanitizedTrailsCardData, trailCardData)
		}

		return c.JSON(http.StatusOK, sanitizedTrailsCardData)
	}
}

// Lesson CRUD

func (tc *controller) CreateExtraLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")

		var lesson content.Lesson
		if err := c.Bind(&lesson); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := tc.Service.CreateExtraLesson(ctx, trailID, "admin", &lesson); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lesson)
	}
}

func (tc *controller) FindExtraLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")
		lessonID := c.Param("identifier")

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		lesson, err := tc.Service.FindExtraLesson(ctx, trailID, userClassification, lessonID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lesson)
	}
}

func (tc *controller) UpdateExtraLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")
		lessonID := c.Param("identifier")

		var lesson content.Lesson
		if err := c.Bind(&lesson); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := tc.Service.UpdateExtraLesson(ctx, trailID, "admin", lessonID, &lesson); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lesson)
	}
}

func (tc *controller) PatchExtraLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")
		lessonID := c.Param("identifier")

		lesson, err := tc.Service.FindExtraLesson(ctx, trailID, "admin", lessonID)
		if err != nil {
			return err
		}

		var newLesson content.Lesson
		if err = c.Bind(&newLesson); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err = lesson.PrepareUpdate(); err != nil {
			return err
		}

		if err = tc.Service.UpdateExtraLesson(ctx, trailID, "admin", lessonID, lesson); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lesson)
	}
}

func (tc *controller) DeleteExtraLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")
		lessonID := c.Param("identifier")

		if err := tc.Service.DeleteExtraLesson(ctx, trailID, "admin", lessonID); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}
