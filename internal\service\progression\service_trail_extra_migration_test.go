package progression

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Global map to store extra trail metadata for error reporting
var extraTrailMetadata = make(map[string]*ExtraTrailWithMetadata)

// TestTrailExtraMigrationFiles tests all trail extra files in the migration/trails.extra directory
// to ensure they have valid paths and can be executed correctly
// This is a validation test, not a functional test, so we don't expect it to pass
// We're just checking that the trail extra files have the expected structure
func TestTrailExtraMigrationFiles(t *testing.T) {
	// Create mocks
	mockRepo := new(MockProgressionRepo)
	mockTrailSvc := new(MockTrailSvc)
	mockVaultSvc := new(MockVaultSvc)
	mockCacheSvc := new(MockCacheSvc)

	// Create service
	service := New(
		mockRepo,
		mockTrailSvc,
		mockVaultSvc,
		mockCacheSvc,
		nil,
	)
	// Create test data
	ctx := context.Background()
	userId := "test-user-id"
	userClassification := "admin" // Use admin classification to bypass access control

	// Create mock vault
	mockVault := &model.Vault{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Coins:     0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create mock progression
	mockProgression := &progression.Progression{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Trails:    []*progression.Trail{},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Setup mocks
	mockVaultSvc.On("FindByUser", ctx, userId).Return(mockVault, nil)

	// Cache expectations - simulate cache miss for FindByUser
	mockCacheSvc.On("Get", mock.Anything, mock.Anything).Return(nil, false)
	mockCacheSvc.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	// Cache invalidation on update
	mockCacheSvc.On("Delete", mock.Anything, mock.Anything).Return(nil)

	mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil)
	mockVaultSvc.On("Update", ctx, mock.Anything).Return(nil)
	mockRepo.On("Update", ctx, mock.Anything).Return(nil)

	// Setup a default mock for trail requirements
	// This ensures that when we test trails with requirements, we have a valid progression
	// with the required trails already completed
	mockRepo.On("FindByUser", mock.Anything, mock.Anything).Return(mockProgression, nil)

	// Find all trail extra files in the migration/trails.extra directory
	// Try different paths to find the directory
	paths := []string{
		"migration/trails.extra",
		"../../migration/trails.extra",
		"../../../migration/trails.extra",
	}

	var trailExtraFiles []string
	var err error
	for _, path := range paths {
		trailExtraFiles, err = findTrailExtraFiles(path)
		if err == nil && len(trailExtraFiles) > 0 {
			t.Logf("Found trail extra files in %s", path)
			break
		}
	}

	if err != nil {
		t.Fatalf("Failed to find trail extra files: %v", err)
	}

	if len(trailExtraFiles) == 0 {
		t.Skip("No trail extra files found in migration/trails.extra directory")
	}

	t.Logf("Found %d trail extra files", len(trailExtraFiles))

	// Track issues found
	var issuesFound []string
	// Create test-friendly versions of trail files with relaxed access control
	testTrailFiles, cleanupFunc, err := createTestFriendlyTrailFiles(t, trailExtraFiles, userClassification)
	if err != nil {
		t.Fatalf("Failed to create test-friendly trail files: %v", err)
	}
	defer cleanupFunc()

	// Test each trail extra file
	for _, trailFile := range testTrailFiles {
		issues := testTrailExtraFile(t, ctx, service, mockTrailSvc, mockRepo, userId, userClassification, trailFile)
		issuesFound = append(issuesFound, issues...)
	}

	// Report issues found
	if len(issuesFound) > 0 {
		t.Logf("\n\nIssues found in trail extra files:")
		for _, issue := range issuesFound {
			t.Logf("- %s", issue)
		}
		t.Errorf("Test failed due to %d issues found in trail extra files", len(issuesFound))
	} else {
		t.Logf("\n\nNo issues found in trail extra files!")
	}
}

// findTrailExtraFiles finds all JSON files in the specified directory and its subdirectories
func findTrailExtraFiles(rootDir string) ([]string, error) {
	var files []string

	err := filepath.Walk(rootDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && filepath.Ext(path) == ".json" {
			files = append(files, path)
		}
		return nil
	})

	return files, err
}

// testTrailExtraFile tests a single trail extra file and returns a list of issues found
func testTrailExtraFile(t *testing.T, ctx context.Context, service Service,
	mockTrailSvc *MockTrailSvc, mockRepo *MockProgressionRepo,
	userId string, userClassification string, trailFile string) []string {
	var issues []string
	fileName := filepath.Base(trailFile)

	t.Run(fmt.Sprintf("TrailExtra: %s", fileName), func(t *testing.T) {
		// Read and parse the trail extra file
		trail, err := readTrailExtraFile(trailFile)
		if err != nil {
			issues = append(issues, fmt.Sprintf("Failed to read trail extra file %s: %v", fileName, err))
			t.Fatalf("Failed to read trail extra file %s: %v", trailFile, err)
		}

		// Check if we need to use ObjectID instead of ID
		if trail.ID == "" && !trail.ObjectID.IsZero() {
			trail.ID = trail.ObjectID.Hex()
			t.Logf("Using ObjectID %s as ID", trail.ID)
		}

		// If ID is still empty, use the filename without extension as ID
		if trail.ID == "" {
			fileName := filepath.Base(trailFile)
			extension := filepath.Ext(fileName)
			fileID := strings.TrimSuffix(fileName, extension)
			trail.ID = fileID
			t.Logf("Using filename %s as ID", trail.ID)

			// Check if the filename is a valid ObjectID
			isValidObjectID := len(fileID) == 24 && isHexString(fileID)
			if !isValidObjectID {
				issues = append(issues, fmt.Sprintf("Trail Extra %s (%s) has invalid filename as ID: %s (should be a valid ObjectID)",
					trail.Name, trail.Identifier, fileID))
			}
		}

		// Skip if trail ID is still empty
		if trail.ID == "" {
			t.Skip("Trail Extra ID is empty, ObjectID is zero, and filename is invalid, skipping")
			return
		}

		// Validate access control
		if trail.AccessControl == nil {
			issues = append(issues, fmt.Sprintf("Trail Extra %s (%s) is missing required accessControl field",
				trail.Name, trail.Identifier))
		} else {
			// Validate access control type
			validTypes := []content.AccessType{
				content.AccessTypeFree,
				content.AccessTypeSubscription,
				content.AccessTypePremium,
				content.AccessTypeCorporate,
			}
			validType := false
			for _, vt := range validTypes {
				if trail.AccessControl.Type == vt {
					validType = true
					break
				}
			}
			if !validType {
				issues = append(issues, fmt.Sprintf("Trail Extra %s (%s) has invalid accessControl.type: %s",
					trail.Name, trail.Identifier, trail.AccessControl.Type))
			}

			// Validate that corporate trails have contract IDs
			if trail.AccessControl.Type == content.AccessTypeCorporate && len(trail.AccessControl.ContractIDs) == 0 {
				issues = append(issues, fmt.Sprintf("Trail Extra %s (%s) with corporate access type is missing contractIds",
					trail.Name, trail.Identifier))
			}

			// Validate that premium trails have user classifications
			if trail.AccessControl.Type == content.AccessTypePremium && len(trail.AccessControl.UserClassifications) == 0 {
				issues = append(issues, fmt.Sprintf("Trail Extra %s (%s) with premium access type is missing userClassifications",
					trail.Name, trail.Identifier))
			}
		}
		// Setup mock for this trail extra
		mockTrailSvc.On("FindExtraTrail", ctx, trail.ID, userClassification).Return(trail, nil)
		// Also setup the regular Find method which is called by loadUserData in the progression service
		mockTrailSvc.On("Find", ctx, trail.ID).Return(trail, nil)

		// Setup mock trail requirements
		// For each trail requirement, create a mock trail progression with 90% completion
		if len(trail.Requirements) > 0 {
			t.Logf("Trail Extra has %d requirements", len(trail.Requirements))
			for _, reqTrailId := range trail.Requirements {
				if reqTrailId == "" {
					continue // Skip empty requirements
				}

				// Create a mock trail for the requirement
				mockReqTrail := &content.Trail{
					ID:         reqTrailId,
					Name:       "Required Trail " + reqTrailId,
					Identifier: "req-trail-" + reqTrailId,
				}
				mockTrailSvc.On("Find", ctx, reqTrailId).Return(mockReqTrail, nil)

				// Add this required trail to the user's progression with 90% completion
				addRequiredTrailToProgression(ctx, mockRepo, userId, reqTrailId)
			}
		}

		t.Logf("Testing trail extra: %s (%s)", trail.Name, trail.Identifier)
		t.Logf("Access control type: %s", trail.AccessControl.Type)
		t.Logf("Number of lessons: %d", len(trail.Lessons))
		if trail.Challenge != nil {
			t.Logf("Challenge: %s (%s)", trail.Challenge.Name, trail.Challenge.Identifier)
			t.Logf("Number of phases: %d", len(trail.Challenge.Phases))
		}

		// Check for content items with both choices and next fields
		for identifier := range extraTrailMetadata[trailFile].ContentWithNextField {
			lineNum := getExtraContentLineNumberFromMetadata(trail.ID, identifier)
			lineInfo := ""
			if lineNum > 0 {
				lineInfo = fmt.Sprintf(" (line %d)", lineNum)
			}

			// Find the lesson or phase that contains this content item
			lessonOrPhase := ""
			lessonOrPhaseType := ""
			for _, lesson := range trail.Lessons {
				for _, content := range lesson.Content {
					if content.Identifier == identifier {
						lessonOrPhase = lesson.Identifier
						lessonOrPhaseType = "Lesson"
						break
					}
				}
			}

			if lessonOrPhase == "" && trail.Challenge != nil {
				for _, phase := range trail.Challenge.Phases {
					for _, content := range phase.Content {
						if content.Identifier == identifier {
							lessonOrPhase = phase.Identifier
							lessonOrPhaseType = "Phase"
							break
						}
					}
				}
			}

			if lessonOrPhase != "" {
				issues = append(issues, fmt.Sprintf("Trail Extra %s, %s %s, Content %s has both choices and an empty next field%s",
					trail.ID, lessonOrPhaseType, lessonOrPhase, identifier, lineInfo))
			}
		}

		// Check for unreachable content items
		unreachableIssues := checkUnreachableExtraContent(t, trail)
		issues = append(issues, unreachableIssues...)

		// Test each lesson in the trail extra
		for _, lesson := range trail.Lessons {
			lessonIssues := testExtraLesson(t, ctx, service, mockRepo, userId, userClassification, trail.ID, lesson)
			issues = append(issues, lessonIssues...)
		}

		// Test the challenge if it exists
		if trail.Challenge != nil && len(trail.Challenge.Phases) > 0 {
			challengeIssues := testExtraChallenge(t, ctx, service, mockRepo, userId, userClassification, trail)
			issues = append(issues, challengeIssues...)
		}

		// Fail the subtest if issues were found
		if len(issues) > 0 {
			t.Errorf("Trail Extra %s has %d issues", trail.ID, len(issues))
		}
	})

	return issues
}

// ExtraTrailWithMetadata extends content.Trail with metadata for testing
type ExtraTrailWithMetadata struct {
	*content.Trail
	FilePath             string
	LineInfo             map[string]int  // Maps content identifiers to line numbers
	ContentWithNextField map[string]bool // Maps content identifiers to whether they have a next field in the JSON
}

// getExtraContentLineNumberFromMetadata returns the line number for a content identifier
func getExtraContentLineNumberFromMetadata(trailId string, identifier string) int {
	// Find the trail file path from the trail ID
	for _, metadata := range extraTrailMetadata {
		if metadata.Trail.ID == trailId {
			if lineNum, ok := metadata.LineInfo[identifier]; ok {
				return lineNum
			}
			break
		}
	}
	return 0 // Line number not found
}

// hasExtraNextFieldInJSON checks if a content item has a next field in the JSON
func hasExtraNextFieldInJSON(trailId string, identifier string) bool {
	// Find the trail file path from the trail ID
	for _, metadata := range extraTrailMetadata {
		if metadata.Trail.ID == trailId {
			// Check if the content has a next field in the JSON
			if metadata.ContentWithNextField != nil {
				if _, ok := metadata.ContentWithNextField[identifier]; ok {
					return true
				}
			}
			break
		}
	}
	return false // Next field not found
}

// readTrailExtraFile reads and parses a trail extra file
func readTrailExtraFile(filePath string) (*content.Trail, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var trail content.Trail
	err = json.Unmarshal(data, &trail)
	if err != nil {
		return nil, err
	}

	// Store metadata in a global map for reference in error messages
	lineInfo, contentWithNextField := findContentMetadata(string(data))
	extraTrailMetadata[filePath] = &ExtraTrailWithMetadata{
		Trail:                &trail,
		FilePath:             filePath,
		LineInfo:             lineInfo,
		ContentWithNextField: contentWithNextField,
	}

	return &trail, nil
}

// createTestFriendlyTrailFiles creates test-friendly versions of trail files with relaxed access control
func createTestFriendlyTrailFiles(t *testing.T, originalFiles []string, userClassification string) ([]string, func(), error) {
	// Create a temporary directory for test files
	tempDir, err := os.MkdirTemp("", "trail_extra_test_*")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create temp directory: %v", err)
	}

	var testFiles []string

	// Cleanup function to remove temp directory
	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	for _, originalFile := range originalFiles {
		// Read the original file
		data, err := os.ReadFile(originalFile)
		if err != nil {
			cleanup()
			return nil, nil, fmt.Errorf("failed to read original file %s: %v", originalFile, err)
		}

		// Parse the trail
		var trail map[string]interface{}
		err = json.Unmarshal(data, &trail)
		if err != nil {
			cleanup()
			return nil, nil, fmt.Errorf("failed to parse trail file %s: %v", originalFile, err)
		}

		// Modify access control to be test-friendly
		if accessControl, ok := trail["accessControl"].(map[string]interface{}); ok {
			// Set user classifications to include our test user
			accessControl["userClassifications"] = []string{userClassification, "admin", "premium", "subscriber", "vip"}

			// Remove or extend validUntil date to be in the future
			delete(accessControl, "validUntil")

			// Set validFrom to be in the past
			delete(accessControl, "validFrom")

			t.Logf("Modified access control for test-friendly version of %s", filepath.Base(originalFile))
		} else {
			// If no access control, add a test-friendly one
			trail["accessControl"] = map[string]interface{}{
				"type":                "FREE",
				"userClassifications": []string{userClassification, "admin", "premium", "subscriber", "vip"},
			}
			t.Logf("Added test-friendly access control to %s", filepath.Base(originalFile))
		}

		// Marshal back to JSON
		modifiedData, err := json.MarshalIndent(trail, "", "  ")
		if err != nil {
			cleanup()
			return nil, nil, fmt.Errorf("failed to marshal modified trail: %v", err)
		}

		// Create test file in temp directory
		fileName := filepath.Base(originalFile)
		testFilePath := filepath.Join(tempDir, fileName)

		err = os.WriteFile(testFilePath, modifiedData, 0644)
		if err != nil {
			cleanup()
			return nil, nil, fmt.Errorf("failed to write test file %s: %v", testFilePath, err)
		}

		testFiles = append(testFiles, testFilePath)
	}

	return testFiles, cleanup, nil
}

// testExtraLesson tests a single lesson by simulating user progression through it
func testExtraLesson(t *testing.T, ctx context.Context, service Service, mockRepo *MockProgressionRepo,
	userId string, userClassification string, trailId string, lesson *content.Lesson) []string {
	var issues []string
	t.Logf("Testing extra lesson: %s (%s)", lesson.Name, lesson.Identifier)

	// Skip if lesson identifier is empty
	if lesson.Identifier == "" {
		t.Logf("Extra lesson identifier is empty, skipping")
		issues = append(issues, fmt.Sprintf("Trail Extra %s has lesson with empty identifier", trailId))
		return issues
	}

	if len(lesson.Content) == 0 {
		t.Logf("Extra lesson has no content, skipping")
		issues = append(issues, fmt.Sprintf("Trail Extra %s, Lesson %s has no content", trailId, lesson.Identifier))
		return issues
	}

	// Check if lesson has requirements
	if len(lesson.Requirements) > 0 {
		t.Logf("Extra lesson has %d requirements", len(lesson.Requirements))
		// For each lesson requirement, add it to the user's progression as completed
		for _, reqLessonId := range lesson.Requirements {
			if reqLessonId == "" {
				continue // Skip empty requirements
			}
			// Add this required lesson to the user's progression as completed
			addRequiredLessonToProgression(ctx, mockRepo, userId, trailId, reqLessonId)
		}
	} else {
		// If the lesson has no requirements, it's the first lesson in the trail
		// We need to ensure the trail is available to the user
		addTrailToUserProgression(ctx, mockRepo, userId, trailId)
	}

	t.Logf("Extra lesson has %d content items", len(lesson.Content))

	// Track the path through the lesson
	var path []*content.LessonContent
	currentContent := lesson.Content[0]
	path = append(path, currentContent)

	// Follow the path through the lesson
	for {
		t.Logf("Content: %s, Next: %s", currentContent.Identifier, currentContent.Next)

		// Skip if content identifier is empty
		if currentContent.Identifier == "" {
			t.Logf("Content identifier is empty, skipping")
			break
		}

		// Check if content has choices
		hasChoices := len(currentContent.Choices) > 0

		// Get the next value
		nextValue := currentContent.Next

		// If content has choices
		if hasChoices {
			// Get line number for the content
			lineNum := getExtraContentLineNumberFromMetadata(trailId, currentContent.Identifier)
			lineInfo := ""
			if lineNum > 0 {
				lineInfo = fmt.Sprintf(" (line %d)", lineNum)
			}

			// Check if the next field is present in the JSON
			hasNextField := hasExtraNextFieldInJSON(trailId, currentContent.Identifier)
			if hasNextField {
				if currentContent.Next != "" {
					t.Logf("Warning: Content %s has both choices and a non-empty next field%s", currentContent.Identifier, lineInfo)
					issues = append(issues, fmt.Sprintf("Trail Extra %s, Lesson %s, Content %s has both choices and a non-empty next field%s",
						trailId, lesson.Identifier, currentContent.Identifier, lineInfo))
				} else {
					t.Logf("Warning: Content %s has both choices and an empty next field%s", currentContent.Identifier, lineInfo)
					issues = append(issues, fmt.Sprintf("Trail Extra %s, Lesson %s, Content %s has both choices and an empty next field%s",
						trailId, lesson.Identifier, currentContent.Identifier, lineInfo))
				}
			}

			// Use the next value from the first choice
			if len(currentContent.Choices) > 0 && currentContent.Choices[0].Next != "" {
				nextValue = currentContent.Choices[0].Next
				currentContent.Next = nextValue
				t.Logf("Content %s has choices, using first choice's next: %s", currentContent.Identifier, nextValue)
			} else {
				// If no valid next in choices, use coin as fallback
				nextValue = "coin"
				currentContent.Next = nextValue
				t.Logf("Content %s has choices but no valid next values, using 'coin'", currentContent.Identifier)
				issues = append(issues, fmt.Sprintf("Trail Extra %s, Lesson %s, Content %s has choices but no valid next values", trailId, lesson.Identifier, currentContent.Identifier))
			}
		} else if nextValue == "" {
			// No choices and no next value is an error
			nextValue = "coin"
			t.Logf("Setting empty Next to 'coin' for content %s", currentContent.Identifier)
			issues = append(issues, fmt.Sprintf("Trail Extra %s, Lesson %s, Content %s is missing Next value", trailId, lesson.Identifier, currentContent.Identifier))
		}

		// Create progression body for extra lesson
		progressionBody := &progression.ProgressionBody{
			Trail:   trailId,
			Module:  lesson.Identifier,
			Content: currentContent.Identifier,
			Type:    string(progression.ProgressionTypeLesson),
			Choice: &progression.ModuleContentChoice{
				Identifier: currentContent.Identifier,
				Next:       nextValue,
			},
		}

		// If the content has choices, we need to set the choice identifier to the first choice
		if hasChoices && len(currentContent.Choices) > 0 {
			progressionBody.Choice.Identifier = currentContent.Choices[0].Identifier
		}

		// Try to register progression, but don't fail the test if it fails
		err := service.CreateLesson(ctx, userId, userClassification, progressionBody)
		if err != nil {
			// If the content has choices, this is expected to fail
			if hasChoices {
				t.Logf("Expected error: Failed to register extra lesson progression for content with choices: %v", err)
			} else {
				t.Errorf("Error: Failed to register extra lesson progression: %v", err)
				issues = append(issues, fmt.Sprintf("Trail Extra %s, Lesson %s, Content %s failed to register progression: %v",
					trailId, lesson.Identifier, currentContent.Identifier, err))
			}
		}

		// Check if we've reached the end of the lesson
		if currentContent.Next == "coin" {
			t.Logf("Reached end of extra lesson with reward: %s", currentContent.Next)
			break
		}

		// Find the next content
		found := false
		for _, content := range lesson.Content {
			if content.Identifier == currentContent.Next {
				currentContent = content
				path = append(path, currentContent)
				found = true
				break
			}
		}

		if !found {
			if currentContent.Next == "" || currentContent.Next == "coin" {
				t.Logf("Reached end of extra lesson")
			} else {
				t.Errorf("Invalid path: next content '%s' not found in extra lesson", currentContent.Next)
				issues = append(issues, fmt.Sprintf("Trail Extra %s, Lesson %s, Content %s references non-existent next content '%s'",
					trailId, lesson.Identifier, currentContent.Identifier, currentContent.Next))
			}
			break
		}

		// Prevent infinite loops
		if len(path) > 100 {
			t.Errorf("Possible infinite loop detected in extra lesson path")
			break
		}
	}

	// Verify the path is valid
	assert.Greater(t, len(path), 0, "Extra lesson should have at least one content item")

	return issues
}

// testExtraChallenge tests a challenge by simulating user progression through it
func testExtraChallenge(t *testing.T, ctx context.Context, service Service, mockRepo *MockProgressionRepo,
	userId string, userClassification string, trail *content.Trail) []string {
	var issues []string
	challenge := trail.Challenge
	t.Logf("Testing extra challenge: %s (%s)", challenge.Name, challenge.Identifier)

	// Skip if challenge identifier is empty
	if challenge.Identifier == "" {
		t.Logf("Extra challenge identifier is empty, skipping")
		issues = append(issues, fmt.Sprintf("Trail Extra %s has challenge with empty identifier", trail.ID))
		return issues
	}

	// Mark all lessons as completed to satisfy the challenge requirement
	markAllLessonsCompleted(ctx, mockRepo, userId, trail)

	// Test each phase in the challenge
	for _, phase := range challenge.Phases {
		phaseIssues := testExtraChallengePhase(t, ctx, service, mockRepo, userId, userClassification, trail.ID, phase)
		issues = append(issues, phaseIssues...)
	}

	return issues
}

// testExtraChallengePhase tests a single challenge phase
func testExtraChallengePhase(t *testing.T, ctx context.Context, service Service, mockRepo *MockProgressionRepo,
	userId string, userClassification string, trailId string, phase *content.ChallengePhase) []string {
	var issues []string
	t.Logf("Testing extra challenge phase: %s (%s)", phase.Name, phase.Identifier)

	// Skip if phase identifier is empty
	if phase.Identifier == "" {
		t.Logf("Extra phase identifier is empty, skipping")
		issues = append(issues, fmt.Sprintf("Trail Extra %s has phase with empty identifier", trailId))
		return issues
	}

	if len(phase.Content) == 0 {
		t.Logf("Extra phase has no content, skipping")
		issues = append(issues, fmt.Sprintf("Trail Extra %s, Phase %s has no content", trailId, phase.Identifier))
		return issues
	}

	// Check if phase has requirements
	if len(phase.Requirements) > 0 {
		t.Logf("Extra phase has %d requirements", len(phase.Requirements))
		for _, reqPhaseId := range phase.Requirements {
			if reqPhaseId == "" {
				continue // Skip empty requirements
			}
			addRequiredPhaseToProgression(ctx, mockRepo, userId, trailId, reqPhaseId)
		}
	}

	t.Logf("Extra phase has %d content items", len(phase.Content))

	// Check for challenge choices with missing or invalid types
	for _, content := range phase.Content {
		if len(content.Choices) > 0 {
			for _, choice := range content.Choices {
				choiceIssues := validateChallengeChoiceType(t, trailId, phase.Identifier, content.Identifier, choice)
				issues = append(issues, choiceIssues...)
			}
		}
	}

	// Track the path through the phase
	var path []*content.ChallengeContent
	currentContent := phase.Content[0]
	path = append(path, currentContent)

	// Follow the path through the phase
	for {
		t.Logf("Extra phase content: %s, Next: %s", currentContent.Identifier, currentContent.Next)

		// Skip if content identifier is empty
		if currentContent.Identifier == "" {
			t.Logf("Content identifier is empty, skipping")
			break
		}

		// Check if content has choices
		hasChoices := len(currentContent.Choices) > 0

		// Get the next value
		nextValue := currentContent.Next

		// If content has choices
		if hasChoices {
			// Get line number for the content
			lineNum := getExtraContentLineNumberFromMetadata(trailId, currentContent.Identifier)
			lineInfo := ""
			if lineNum > 0 {
				lineInfo = fmt.Sprintf(" (line %d)", lineNum)
			}

			// Check if the next field is present in the JSON
			hasNextField := hasExtraNextFieldInJSON(trailId, currentContent.Identifier)
			if hasNextField {
				if currentContent.Next != "" {
					t.Logf("Warning: Content %s has both choices and a non-empty next field%s", currentContent.Identifier, lineInfo)
					issues = append(issues, fmt.Sprintf("Trail Extra %s, Phase %s, Content %s has both choices and a non-empty next field%s",
						trailId, phase.Identifier, currentContent.Identifier, lineInfo))
				} else {
					t.Logf("Warning: Content %s has both choices and an empty next field%s", currentContent.Identifier, lineInfo)
					issues = append(issues, fmt.Sprintf("Trail Extra %s, Phase %s, Content %s has both choices and an empty next field%s",
						trailId, phase.Identifier, currentContent.Identifier, lineInfo))
				}
			}

			// Use the next value from the first choice
			if len(currentContent.Choices) > 0 && currentContent.Choices[0].Next != "" {
				nextValue = currentContent.Choices[0].Next
				currentContent.Next = nextValue
				t.Logf("Content %s has choices, using first choice's next: %s", currentContent.Identifier, nextValue)
			} else {
				nextValue = "coin"
				currentContent.Next = nextValue
				t.Logf("Content %s has choices but no valid next values, using 'coin'", currentContent.Identifier)
				issues = append(issues, fmt.Sprintf("Trail Extra %s, Phase %s, Content %s has choices but no valid next values", trailId, phase.Identifier, currentContent.Identifier))
			}
		} else if nextValue == "" {
			nextValue = "coin"
			t.Logf("Setting empty Next to 'coin' for content %s", currentContent.Identifier)
			issues = append(issues, fmt.Sprintf("Trail Extra %s, Phase %s, Content %s is missing Next value", trailId, phase.Identifier, currentContent.Identifier))
		}

		// Create progression body for extra challenge
		progressionBody := &progression.ProgressionBody{
			Trail:   trailId,
			Module:  phase.Identifier,
			Content: currentContent.Identifier,
			Type:    string(progression.ProgressionTypeChallenge),
			Choice: &progression.ModuleContentChoice{
				Identifier: currentContent.Identifier,
				Next:       nextValue,
			},
		}

		// If the content has choices, set the choice identifier to the first choice
		if hasChoices && len(currentContent.Choices) > 0 {
			progressionBody.Choice.Identifier = currentContent.Choices[0].Identifier
		}

		// Try to register progression
		err := service.CreateChallenge(ctx, userId, userClassification, progressionBody)
		if err != nil {
			if hasChoices {
				t.Logf("Expected error: Failed to register extra challenge progression for content with choices: %v", err)
			} else {
				t.Errorf("Error: Failed to register extra challenge progression: %v", err)
				issues = append(issues, fmt.Sprintf("Trail Extra %s, Phase %s, Content %s failed to register progression: %v",
					trailId, phase.Identifier, currentContent.Identifier, err))
			}
		}

		// Check if we've reached the end of the phase
		if currentContent.Next == "coin" {
			t.Logf("Reached end of extra phase with reward: %s", currentContent.Next)
			break
		}

		// Find the next content
		found := false
		for _, content := range phase.Content {
			if content.Identifier == currentContent.Next {
				currentContent = content
				path = append(path, currentContent)
				found = true
				break
			}
		}

		if !found {
			if currentContent.Next == "" || currentContent.Next == "coin" {
				t.Logf("Reached end of extra phase")
			} else {
				t.Errorf("Invalid path: next content '%s' not found in extra phase", currentContent.Next)
				issues = append(issues, fmt.Sprintf("Trail Extra %s, Phase %s, Content %s references non-existent next content '%s'",
					trailId, phase.Identifier, currentContent.Identifier, currentContent.Next))
			}
			break
		}

		// Prevent infinite loops
		if len(path) > 100 {
			t.Errorf("Possible infinite loop detected in extra phase path")
			break
		}
	}

	// Verify the path is valid
	assert.Greater(t, len(path), 0, "Extra phase should have at least one content item")

	return issues
}

// checkUnreachableExtraContent checks for content items that are not reachable from any other content item
func checkUnreachableExtraContent(t *testing.T, trail *content.Trail) []string {
	var issues []string

	// Check each lesson for unreachable content
	for _, lesson := range trail.Lessons {
		lessonIssues := checkUnreachableContentInExtraLesson(t, trail.ID, lesson)
		issues = append(issues, lessonIssues...)
	}

	// Check each challenge phase for unreachable content
	if trail.Challenge != nil {
		for _, phase := range trail.Challenge.Phases {
			phaseIssues := checkUnreachableContentInExtraPhase(t, trail.ID, phase)
			issues = append(issues, phaseIssues...)
		}
	}

	return issues
}

// checkUnreachableContentInExtraLesson checks for content items that are not reachable in an extra lesson
func checkUnreachableContentInExtraLesson(t *testing.T, trailId string, lesson *content.Lesson) []string {
	var issues []string

	// Skip if lesson has no content
	if len(lesson.Content) == 0 {
		return issues
	}

	// Build a map of all content items
	contentMap := make(map[string]*content.LessonContent)
	for _, content := range lesson.Content {
		contentMap[content.Identifier] = content
	}

	// Build a map of reachable content items
	reachable := make(map[string]bool)

	// The first content item is always reachable
	firstContent := lesson.Content[0]
	reachable[firstContent.Identifier] = true

	// Mark all content items that are reachable from the first content item
	markReachableContent(firstContent, contentMap, reachable)

	// Check for unreachable content items
	for _, content := range lesson.Content {
		if !reachable[content.Identifier] {
			// Get line number for the content
			lineNum := getExtraContentLineNumberFromMetadata(trailId, content.Identifier)
			lineInfo := ""
			if lineNum > 0 {
				lineInfo = fmt.Sprintf(" (line %d)", lineNum)
			}

			t.Logf("Warning: Content %s is not reachable in extra lesson %s%s", content.Identifier, lesson.Identifier, lineInfo)
			issues = append(issues, fmt.Sprintf("Trail Extra %s, Lesson %s, Content %s is not reachable%s",
				trailId, lesson.Identifier, content.Identifier, lineInfo))
		}
	}

	return issues
}

// checkUnreachableContentInExtraPhase checks for content items that are not reachable in an extra challenge phase
func checkUnreachableContentInExtraPhase(t *testing.T, trailId string, phase *content.ChallengePhase) []string {
	var issues []string

	// Skip if phase has no content
	if len(phase.Content) == 0 {
		return issues
	}

	// Build a map of all content items
	contentMap := make(map[string]*content.ChallengeContent)
	for _, content := range phase.Content {
		contentMap[content.Identifier] = content
	}

	// Build a map of reachable content items
	reachable := make(map[string]bool)

	// The first content item is always reachable
	firstContent := phase.Content[0]
	reachable[firstContent.Identifier] = true

	// Mark all content items that are reachable from the first content item
	markReachableChallengeContent(firstContent, contentMap, reachable)

	// Check for unreachable content items
	for _, content := range phase.Content {
		if !reachable[content.Identifier] {
			// Get line number for the content
			lineNum := getExtraContentLineNumberFromMetadata(trailId, content.Identifier)
			lineInfo := ""
			if lineNum > 0 {
				lineInfo = fmt.Sprintf(" (line %d)", lineNum)
			}

			t.Logf("Warning: Content %s is not reachable in extra phase %s%s", content.Identifier, phase.Identifier, lineInfo)
			issues = append(issues, fmt.Sprintf("Trail Extra %s, Phase %s, Content %s is not reachable%s",
				trailId, phase.Identifier, content.Identifier, lineInfo))
		}
	}

	return issues
}
