package investmentcategory

import (
	"context"
	"net/http"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/investmentcategory"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	Create() echo.HandlerFunc
	Find() echo.HandlerFunc
	FindByIdentifier() echo.HandlerFunc
	FindAll() echo.HandlerFunc
	Update() echo.HandlerFunc
	Delete() echo.HandlerFunc
}

type controller struct {
	Service investmentcategory.Service
}

func New(service investmentcategory.Service) Controller {
	return &controller{
		Service: service,
	}
}

// Routes
func (tc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	investmentCategoryContentGroup := legacyGroup.Group("content/investmentCategory/")

	// CRUD
	investmentCategoryContentGroup.POST("", tc.Create(), middlewares.AuthGuard(), middlewares.AdminGuard())
	investmentCategoryContentGroup.GET(":id/", tc.Find())
	investmentCategoryContentGroup.POST("findByIdentifier/", tc.FindByIdentifier())
	investmentCategoryContentGroup.GET("findAll/", tc.FindAll())
	investmentCategoryContentGroup.PUT(":id/", tc.Update(), middlewares.AuthGuard(), middlewares.AdminGuard())
	investmentCategoryContentGroup.DELETE(":id/", tc.Delete(), middlewares.AuthGuard(), middlewares.AdminGuard())
}

// CRUD
func (tc *controller) Create() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var investmentCategory content.InvestmentCategory
		if err := c.Bind(&investmentCategory); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := investmentCategory.PrepareCreate(); err != nil {
			return err
		}

		if err := tc.Service.Create(ctx, &investmentCategory); err != nil {
			return err
		}

		createdInvestmentCategory, err := tc.Service.FindByIdentifier(ctx, investmentCategory.Identifier)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, createdInvestmentCategory.Sanitize())
	}
}

func (tc *controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		investmentCategory, err := tc.Service.Find(ctx, c.Param("id"))

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, investmentCategory.Sanitize())
	}
}

func (tc *controller) FindByIdentifier() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var body map[string]interface{}

		if err := c.Bind(&body); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		identifierParam := strings.ToLower(strings.TrimSpace(body["identifier"].(string)))
		investmentCategory, err := tc.Service.FindByIdentifier(ctx, identifierParam)

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, investmentCategory.Sanitize())
	}
}

func (tc *controller) FindAll() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		result, err := tc.Service.FindAll(ctx)
		if err != nil {
			return err
		}

		if result == nil {
			return c.JSON(http.StatusNoContent, nil)
		}

		for _, investmentCategory := range result {
			investmentCategory.Sanitize()
		}

		return c.JSON(http.StatusOK, result)
	}
}

func (tc *controller) Update() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		investmentCategory, err := tc.Service.Find(ctx, sanitizeString(c.Param("id")))
		if err != nil {
			return err
		}

		var newInvestmentCategory content.InvestmentCategory
		if err = c.Bind(&newInvestmentCategory); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err = investmentCategory.PrepareUpdate(&newInvestmentCategory); err != nil {
			return err
		}

		if err = tc.Service.Update(ctx, investmentCategory); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, investmentCategory.Sanitize())
	}
}

func (tc *controller) Delete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		if err := tc.Service.Delete(ctx, sanitizeString(c.Param("id"))); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Helper
func sanitizeString(str string) string {
	return strings.ToLower(strings.TrimSpace(str))
}
