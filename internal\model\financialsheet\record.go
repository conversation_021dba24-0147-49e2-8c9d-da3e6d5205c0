package financialsheet

import (
	"encoding/json"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Record represents the complete financial tracking system
type Record struct {
	ObjectID primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID       string             `json:"id,omitempty" bson:"-"`
	UserID   string             `json:"userID" bson:"userID"`
	UserName string             `json:"userName" bson:"userName"`
	// League             League             `json:"league" bson:"league"` // Removed: League is now a separate system
	Points             Points           `json:"points" bson:"points"` // Points for overall user streak, Best Points recorded here
	YearData           map[int]YearData `json:"yearData,omitempty" bson:"yearData"`
	Categories         []CategoryCard   `json:"categories,omitempty" bson:"-"`                // For flattened response
	TotalIncome        monetary.Amount  `bson:"totalIncome" json:"totalIncome"`               // Total income for the user
	TotalCostsOfLiving monetary.Amount  `bson:"totalCostsOfLiving" json:"totalCostsOfLiving"` // Total costs of living for the user
	TotalExpenses      monetary.Amount  `bson:"totalExpenses" json:"totalExpenses"`           // Total expenses for the user
	Balance            monetary.Amount  `bson:"balance" json:"balance"`                       // Balance (typically TotalIncome - TotalExpenses)
}

// Points tracks user's overall activity consistency (Daily Streak, Best Streak)
type Points struct {
	Current               int         `json:"current" bson:"current"`
	Best                  int         `json:"best" bson:"best"`
	LastTransactionDate   time.Time   `json:"lastTransactionDate,omitempty" bson:"lastTransactionDate,omitempty"`
	RecoveryAvailable     bool        `json:"recoveryAvailable" bson:"recoveryAvailable"`
	MissedDays            []time.Time `json:"missedDays" bson:"missedDays"`
	LastNoTransactionDate time.Time   `json:"lastNoTransactionDate,omitempty" bson:"lastNoTransactionDate,omitempty"`
	NoTransactionDates    []time.Time `json:"noTransactionDates,omitempty" bson:"noTransactionDates,omitempty"`
}

// CategoryCard represents a category summary with its total value
type CategoryCard struct {
	ID         string             `json:"id" bson:"id"`
	Identifier string             `json:"identifier" bson:"identifier"`
	Type       CategoryType       `json:"type" bson:"type"`
	Name       string             `json:"name" bson:"name"`
	Icon       CategoryIcon       `json:"icon" bson:"icon"`
	Background CategoryBackground `json:"background" bson:"background"`
	Value      monetary.Amount    `json:"value" bson:"value"`
}

// YearData represents all months in a year
type YearData map[string]MonthData

// MonthData represents category totals and transactions for a month
type MonthData struct {
	Categories   []CategoryCard `json:"categories" bson:"categories"`     // Summary by category
	Transactions []Transaction  `json:"transactions" bson:"transactions"` // Individual transactions
}

// Transaction represents a single financial transaction
type Transaction struct {
	ObjectID          primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID                string             `json:"id,omitempty" bson:"-"`
	MoneySource       MoneySource        `json:"moneySource" bson:"moneySource"`
	PaymentMethod     PaymentMethod      `json:"paymentMethod" bson:"paymentMethod"`
	Icon              CategoryIcon       `json:"icon" bson:"icon"`
	Category          CategoryIdentifier `json:"category" bson:"category"`
	Value             monetary.Amount    `json:"value" bson:"value"`
	Date              time.Time          `json:"date" bson:"date"`
	Type              CategoryType       `json:"type" bson:"type"`
	AttachedDreamID   string             `json:"attachedDreamID" bson:"attachedDreamID"`
	AttachedDreamName string             `json:"attachedDreamName" bson:"attachedDreamName"`
	Hidden            bool               `json:"hidden,omitempty" bson:"hidden,omitempty"` // Hidden transactions for networth tracking
}

func (t Transaction) MarshalJSON() ([]byte, error) {
	type Alias Transaction

	// Create a Category instance to get the MoneySourceInfo
	cat := Category{
		Identifier: t.Category,
		Type:       t.Type,
	}
	moneySourceInfo := cat.GetMoneySourceInfo(t.MoneySource)
	paymentMethodInfo := cat.GetPaymentMethodInfo(t.PaymentMethod)

	return json.Marshal(&struct {
		ID            string       `json:"id"`
		MoneySource   string       `json:"moneySource"`
		PaymentMethod string       `json:"paymentMethod"`
		Icon          CategoryIcon `json:"icon"`
		*Alias
	}{
		ID:            t.ID,
		MoneySource:   moneySourceInfo.Name,
		PaymentMethod: paymentMethodInfo.Name,
		Icon:          moneySourceInfo.Icon,
		Alias:         (*Alias)(&t),
	})
}
