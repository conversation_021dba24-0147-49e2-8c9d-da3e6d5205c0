package user

import (
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/labstack/echo/v4"
)

// UploadPhoto handles profile picture uploads
func (uc *controller) UploadPhoto() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user token from request
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get current user
		currentUser, err := uc.Service.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get file from request
		file, err := c.FormFile("photo")
		if err != nil {
			return errors.New(errors.Controller, "invalid file", errors.Validation, err)
		}

		// Validate file type
		ext := strings.ToLower(filepath.Ext(file.Filename))
		allowedExts := map[string]bool{
			".jpg":  true,
			".jpeg": true,
			".png":  true,
		}
		if !allowedExts[ext] {
			return errors.New(errors.Controller, "invalid file type, only JPG and PNG are allowed", errors.Validation, nil)
		}

		// Validate file size (max 5MB)
		if file.Size > 5*1024*1024 {
			return errors.New(errors.Controller, "file too large, max size is 5MB", errors.Validation, nil)
		}

		// Upload file to S3
		fileURL, err := uc.S3Service.UploadFile(ctx, file, os.Getenv("AWS_S3_USER_PHOTOS_FOLDER"))
		if err != nil {
			return err
		}

		// Update user's avatar
		if currentUser.PhotoURL != "" && currentUser.PhotoURL != fileURL {
			// Delete old avatar if it exists and is different from the new one
			// Ignore errors as this is not critical
			_ = uc.S3Service.DeleteFile(ctx, currentUser.PhotoURL)
		}

		// Update user's avatar URL
		currentUser.PhotoURL = fileURL

		// Save user
		if err := uc.Service.Patch(ctx, currentUser, &model.User{PhotoURL: fileURL}); err != nil {
			return err
		}

		// Return success response
		return c.JSON(http.StatusOK, map[string]string{
			"url": fileURL,
		})
	}
}
