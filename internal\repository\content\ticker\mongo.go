package ticker

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.TICKERS_COLLECTION),
	}

	// Create unique index on user field
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "identifier", Value: 1}},
			Options: options.Index().SetUnique(true).SetName("identifier"),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on ticker.identifier field")
		db.Client().Disconnect(context.Background())
	}

	return repo
}

func (m mongoDB) Create(ctx context.Context, ticker *content.Ticker) error {
	_, err := m.collection.InsertOne(ctx, ticker)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "ticker already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create ticker", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) Find(ctx context.Context, id string) (*content.Ticker, error) {
	objectId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid ticker ID format", errors.Validation, err)
	}

	var ticker content.Ticker
	if err = m.collection.FindOne(ctx, bson.D{primitive.E{Key: "_id", Value: objectId}}).Decode(&ticker); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "ticker not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "find ticker failed", errors.Internal, err)
	}

	ticker.ID = ticker.ObjectID.Hex()
	return &ticker, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*content.Ticker, error) {
	cursor, err := m.collection.Find(ctx, bson.D{})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query tickers", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var tickers []*content.Ticker
	for cursor.Next(ctx) {
		var ticker content.Ticker
		if err = cursor.Decode(&ticker); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode ticker", errors.Internal, err)
		}
		ticker.ID = ticker.ObjectID.Hex()
		tickers = append(tickers, &ticker)
	}

	return tickers, nil
}

func (m mongoDB) FindByIdentifier(ctx context.Context, identifier string) (*content.Ticker, error) {
	var ticker content.Ticker
	if err := m.collection.FindOne(ctx, bson.D{primitive.E{Key: "identifier", Value: identifier}}).Decode(&ticker); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "ticker not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "find ticker by identifier failed", errors.Internal, err)
	}

	ticker.ID = ticker.ObjectID.Hex()
	return &ticker, nil
}

func (m mongoDB) FindByCategory(ctx context.Context, category string) ([]*content.Ticker, error) {
	cursor, err := m.collection.Find(ctx, bson.D{primitive.E{Key: "category", Value: category}})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query tickers by category", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var tickers []*content.Ticker
	for cursor.Next(ctx) {
		var ticker content.Ticker
		if err = cursor.Decode(&ticker); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode ticker", errors.Internal, err)
		}
		ticker.ID = ticker.ObjectID.Hex()
		tickers = append(tickers, &ticker)
	}

	return tickers, nil
}

func (m mongoDB) Update(ctx context.Context, ticker *content.Ticker) error {
	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: ticker.ObjectID}},
		primitive.M{"$set": ticker})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "ticker update would cause conflict", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update ticker", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "ticker not found for update", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) Delete(ctx context.Context, id string) error {
	if id == "" {
		return errors.New(errors.Repository, "ID cannot be empty", errors.Validation, nil)
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid ticker ID format", errors.Validation, err)
	}

	result, err := m.collection.DeleteOne(ctx, bson.D{primitive.E{Key: "_id", Value: objectID}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete ticker", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "ticker not found for deletion", errors.NotFound, nil)
	}

	return nil
}
