package ticker

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/ticker"
)

type Service interface {
	// Ticker CRUD
	Create(ctx context.Context, ticker *content.Ticker) error
	Find(ctx context.Context, id string) (*content.Ticker, error)
	FindAll(ctx context.Context) ([]*content.Ticker, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.Ticker, error)
	Update(ctx context.Context, ticker *content.Ticker) error
	Delete(ctx context.Context, id string) error

	// Utility
	FindByCategory(ctx context.Context, category string) ([]*content.Ticker, error)
}

type service struct {
	Repository ticker.Repository
}

func New(repository ticker.Repository) Service {
	return &service{
		Repository: repository,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, ticker *content.Ticker) error {
	if err := ticker.PrepareCreate(); err != nil {
		return err
	}

	foundTicker, err := s.Repository.FindByIdentifier(ctx, ticker.Identifier)
	if err == nil && foundTicker != nil {
		return errors.New(errors.Service, "ticker already exists", errors.Conflict, err)
	}

	if err = s.Repository.Create(ctx, ticker); err != nil {
		return err
	}

	return nil
}

func (s *service) Find(ctx context.Context, id string) (*content.Ticker, error) {
	foundTicker, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}
	foundTicker.ID = foundTicker.ObjectID.Hex()
	return foundTicker, nil
}

func (s *service) FindAll(ctx context.Context) ([]*content.Ticker, error) {
	foundTickers, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}
	for _, foundTicker := range foundTickers {
		foundTicker.ID = foundTicker.ObjectID.Hex()
	}

	return foundTickers, nil
}

func (s *service) FindByIdentifier(ctx context.Context, identifier string) (*content.Ticker, error) {
	foundTicker, err := s.Repository.FindByIdentifier(ctx, identifier)
	if err != nil {
		return nil, err
	}
	foundTicker.ID = foundTicker.ObjectID.Hex()
	return foundTicker, nil
}

func (s *service) FindByCategory(ctx context.Context, category string) ([]*content.Ticker, error) {
	foundCategory, err := s.Repository.FindByCategory(ctx, category)
	if err != nil {
		return nil, err
	}
	return foundCategory, nil
}

func (s *service) Update(ctx context.Context, ticker *content.Ticker) error {
	return s.Repository.Update(ctx, ticker)
}

func (s *service) Delete(ctx context.Context, id string) error {
	return s.Repository.Delete(ctx, id)
}
