package wallet

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	Find(ctx context.Context, id string) (*content.Wallet, error)
	FindMany(ctx context.Context, ids []primitive.ObjectID) ([]*content.Wallet, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.Wallet, error)
	FindAll(ctx context.Context) ([]*content.Wallet, error)
}

type Writer interface {
	Create(ctx context.Context, wallet *content.Wallet) error
	Update(ctx context.Context, wallet *content.Wallet) error
	Delete(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
