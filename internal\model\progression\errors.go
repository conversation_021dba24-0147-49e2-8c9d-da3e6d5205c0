package progression

import (
	"fmt"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

var (
	ErrRequiredTrailValue            = errors.New(errors.Model, errors.ProgressionRequiredTrailValue, errors.Validation, nil)
	ErrRequiredLessonValue           = errors.New(errors.Model, errors.ProgressionRequiredLessonValue, errors.Validation, nil)
	ErrRequiredTypeValue             = errors.New(errors.Model, errors.ProgressionRequiredTypeValue, errors.Validation, nil)
	ErrTypeMustBeLessonOrChallenge   = errors.New(errors.Model, errors.ProgressionTypeMustBeLessonOrChallenge, errors.Validation, nil)
	ErrRequiredChoiceValue           = errors.New(errors.Model, errors.ProgressionRequiredChoiceValue, errors.Validation, nil)
	ErrRequiredChoiceIdentifierValue = errors.New(errors.Model, errors.ProgressionRequiredChoiceIdentifierValue, errors.Validation, nil)
	ErrRequiredChoiceNextValue       = errors.New(errors.Model, errors.ProgressionRequiredChoiceNextValue, errors.Validation, nil)
	ErrInvalidLessonProgress         = errors.New(errors.Model, errors.ProgressionInvalidLessonProgress, errors.Validation, nil)
	ErrInvalidTrailProgress          = errors.New(errors.Model, errors.ProgressionInvalidTrailProgress, errors.Validation, nil)
	ErrInvalidLesson                 = errors.New(errors.Model, errors.ProgressionInvalidLesson, errors.Validation, nil)
	ErrInvalidContent                = errors.New(errors.Model, errors.ProgressionInvalidContent, errors.Validation, nil)
	ErrInvalidContentProgress        = errors.New(errors.Model, errors.ProgressionInvalidContentProgress, errors.Validation, nil)
	ErrInvalidChallengeProgress      = errors.New(errors.Model, errors.ProgressionInvalidChallengeProgress, errors.Validation, nil)
	ErrInvalidChallenge              = errors.New(errors.Model, errors.ProgressionInvalidChallenge, errors.Validation, nil)
	ErrInvalidChallengePhaseProgress = errors.New(errors.Model, errors.ProgressionInvalidChallengePhaseProgress, errors.Validation, nil)
)

// logAndReturnError logs an error message and returns the corresponding error
func logAndReturnError(msg string, err error) error {
	log.Println(msg)
	return err
}

// logAndReturnErrorWithContext logs an error message with context and returns a wrapped error
func logAndReturnErrorWithContext(msg string, err error, context map[string]interface{}) error {
	contextStr := ""
	for k, v := range context {
		contextStr += fmt.Sprintf("%s=%v ", k, v)
	}
	log.Printf("%s - Context: %s", msg, contextStr)
	return fmt.Errorf("%w: %s", err, msg)
}
