package middlewares

import (
	"context"
	"log/slog"
	"net/http"
	"os"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

// GetLogLevel returns the log level based on environment variable
func GetLogLevel() slog.Level {
	logLevel := os.Getenv("LOG_LEVEL")
	switch strings.ToUpper(logLevel) {
	case "DEBUG":
		return slog.LevelDebug
	case "INFO":
		return slog.LevelInfo
	case "WARN":
		return slog.LevelWarn
	case "ERROR":
		return slog.LevelError
	default:
		return slog.LevelInfo // Default to INFO if not specified
	}
}

// CustomLogger returns a middleware that logs HTTP requests using slog.
// It logs successful health check requests (GET /v2/health with status 200) at DEBUG level
// and all other requests at the configured log level (default: INFO).
//
// The log level can be configured using the LOG_LEVEL environment variable:
// - DEBUG: Logs everything including debug information
// - INFO: Logs info, warnings, and errors (default)
// - WARN: Logs only warnings and errors
// - ERROR: Logs only errors
func CustomLogger() echo.MiddlewareFunc {
	// Create a structured logger with JSON output
	logger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: GetLogLevel(),
	}))

	return middleware.RequestLoggerWithConfig(middleware.RequestLoggerConfig{
		LogStatus:    true,
		LogURI:       true,
		LogMethod:    true,
		LogRemoteIP:  true,
		LogUserAgent: true,
		LogLatency:   true,
		LogError:     true,
		// Note: HandleError is not available in this version of Echo
		LogValuesFunc: func(c echo.Context, v middleware.RequestLoggerValues) error {
			// Check if this is a health check request
			isHealthCheck := v.Method == http.MethodGet &&
				(strings.HasSuffix(v.URI, "/v2/health") ||
					strings.HasSuffix(v.URI, "/v2/health/"))
			isSuccessfulHealthCheck := isHealthCheck && v.Status == http.StatusOK

			if isSuccessfulHealthCheck {
				// Log successful health checks at DEBUG level
				logger.LogAttrs(context.Background(), slog.LevelDebug, "HEALTH_CHECK",
					slog.String("uri", v.URI),
					slog.String("method", v.Method),
					slog.Int("status", v.Status),
					slog.Duration("latency", v.Latency),
				)
			} else if v.Error == nil {
				// Log successful requests at INFO level
				logger.LogAttrs(context.Background(), slog.LevelInfo, "REQUEST",
					slog.String("uri", v.URI),
					slog.String("method", v.Method),
					slog.Int("status", v.Status),
					slog.String("remote_ip", v.RemoteIP),
					slog.String("user_agent", v.UserAgent),
					slog.Duration("latency", v.Latency),
				)
			} else {
				// Log errors at ERROR level
				logger.LogAttrs(context.Background(), slog.LevelError, "REQUEST_ERROR",
					slog.String("uri", v.URI),
					slog.String("method", v.Method),
					slog.Int("status", v.Status),
					slog.String("remote_ip", v.RemoteIP),
					slog.String("user_agent", v.UserAgent),
					slog.Duration("latency", v.Latency),
					slog.String("error", v.Error.Error()),
				)
			}
			return nil
		},
	})
}

// GetSlogger returns a configured slog.Logger instance that can be used
// throughout the application for consistent logging.
//
// The log level is determined by the LOG_LEVEL environment variable:
// - DEBUG: Logs everything including debug information
// - INFO: Logs info, warnings, and errors (default)
// - WARN: Logs only warnings and errors
// - ERROR: Logs only errors
func GetSlogger() *slog.Logger {
	// Create a structured logger with JSON output
	return slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: GetLogLevel(),
	}))
}
