package investmentcategory

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/content"
)

type Reader interface {
	Find(ctx context.Context, id string) (*content.InvestmentCategory, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.InvestmentCategory, error)
	FindAll(ctx context.Context) ([]*content.InvestmentCategory, error)
}

type Writer interface {
	Create(ctx context.Context, investmentCategory *content.InvestmentCategory) error
	Update(ctx context.Context, investmentCategory *content.InvestmentCategory) error
	Delete(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
