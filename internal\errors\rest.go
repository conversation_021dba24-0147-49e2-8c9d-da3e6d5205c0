package errors

import (
	"fmt"
	"net/http"
)

type RestError struct {
	code    int
	message Message
}

func Rest(statusCode int, message Message) *RestError {
	return &RestError{
		code:    statusCode,
		message: message,
	}
}

func (r *RestError) Error() string {
	return fmt.Sprintf("%d: %s", r.code, r.message)
}

func (r *RestError) Code() int {
	return r.code
}

func (r *RestError) Message() Message {
	return r.message
}

// HTTPStatus converts a domain error to a REST error
func HTTPStatus(err error) *RestError {
	domainErr, ok := err.(*DomainError)
	if !ok {
		return Rest(http.StatusInternalServerError, "unexpected error")
	}

	switch domainErr.Kind() {
	// 4xx Client Errors
	case BadRequest, Validation:
		return Rest(http.StatusBadRequest, domainErr.message)
	case Unauthorized:
		return Rest(http.StatusUnauthorized, domainErr.message)
	case Forbidden:
		return Rest(http.StatusForbidden, domainErr.message)
	case NotFound:
		return Rest(http.StatusNotFound, domainErr.message)
	case RequestTimeout:
		return Rest(http.StatusRequestTimeout, domainErr.message)
	case Conflict:
		return Rest(http.StatusConflict, domainErr.message)
	case Gone:
		return Rest(http.StatusGone, domainErr.message)
	case LengthRequired:
		return Rest(http.StatusLengthRequired, domainErr.message)
	case PreconditionFailed:
		return Rest(http.StatusPreconditionFailed, domainErr.message)
	case PayloadTooLarge:
		return Rest(http.StatusRequestEntityTooLarge, domainErr.message)
	case URITooLong:
		return Rest(http.StatusRequestURITooLong, domainErr.message)
	case UnsupportedMediaType:
		return Rest(http.StatusUnsupportedMediaType, domainErr.message)
	case RangeNotSatisfiable:
		return Rest(http.StatusRequestedRangeNotSatisfiable, domainErr.message)
	case ExpectationFailed:
		return Rest(http.StatusExpectationFailed, domainErr.message)
	case Teapot:
		return Rest(http.StatusTeapot, domainErr.message)
	case MisdirectedRequest:
		return Rest(http.StatusMisdirectedRequest, domainErr.message)
	case UnprocessableEntity:
		return Rest(http.StatusUnprocessableEntity, domainErr.message)
	case Locked:
		return Rest(http.StatusLocked, domainErr.message)
	case FailedDependency:
		return Rest(http.StatusFailedDependency, domainErr.message)
	case TooEarly:
		return Rest(http.StatusTooEarly, domainErr.message)
	case UpgradeRequired:
		return Rest(http.StatusUpgradeRequired, domainErr.message)
	case PreconditionRequired:
		return Rest(http.StatusPreconditionRequired, domainErr.message)
	case TooManyRequests:
		return Rest(http.StatusTooManyRequests, domainErr.message)

	// 5xx Server Errors
	case Internal:
		return Rest(http.StatusInternalServerError, "internal server error")
	case NotImplemented:
		return Rest(http.StatusNotImplemented, domainErr.message)
	case BadGateway:
		return Rest(http.StatusBadGateway, domainErr.message)
	case ServiceUnavailable:
		return Rest(http.StatusServiceUnavailable, domainErr.message)
	case GatewayTimeout:
		return Rest(http.StatusGatewayTimeout, domainErr.message)
	case VersionNotSupported:
		return Rest(http.StatusHTTPVersionNotSupported, domainErr.message)
	case InsufficientStorage:
		return Rest(http.StatusInsufficientStorage, domainErr.message)
	case LoopDetected:
		return Rest(http.StatusLoopDetected, domainErr.message)
	case NotExtended:
		return Rest(http.StatusNotExtended, domainErr.message)
	case NetworkAuthRequired:
		return Rest(http.StatusNetworkAuthenticationRequired, domainErr.message)
	default:
		return Rest(http.StatusInternalServerError, "unexpected error")
	}
}
