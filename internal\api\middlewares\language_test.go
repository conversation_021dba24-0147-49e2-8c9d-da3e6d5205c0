package middlewares

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/service/i18n"
	"github.com/labstack/echo/v4"
)

func TestLanguageDetection(t *testing.T) {
	// Create a mock i18n service
	mockService := &i18n.Service{}
	
	// Create Echo instance
	e := echo.New()
	
	// Create test handler
	handler := func(c echo.Context) error {
		lang := GetLanguageFromEchoContext(c)
		return c.String(http.StatusOK, lang)
	}
	
	// Apply middleware
	e.Use(LanguageDetection(mockService))
	e.GET("/test", handler)
	
	tests := []struct {
		name           string
		acceptLanguage string
		expectedLang   string
	}{
		{
			name:           "Portuguese header",
			acceptLanguage: "pt-BR,pt;q=0.9,en;q=0.8",
			expectedLang:   "pt",
		},
		{
			name:           "English header",
			acceptLanguage: "en-US,en;q=0.9",
			expectedLang:   "en",
		},
		{
			name:           "Spanish header",
			acceptLanguage: "es-ES,es;q=0.9",
			expectedLang:   "es",
		},
		{
			name:           "No header",
			acceptLanguage: "",
			expectedLang:   i18n.DefaultLanguage,
		},
		{
			name:           "Unsupported language",
			acceptLanguage: "fr-FR,fr;q=0.9",
			expectedLang:   i18n.DefaultLanguage,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(http.MethodGet, "/test", nil)
			if tt.acceptLanguage != "" {
				req.Header.Set("Accept-Language", tt.acceptLanguage)
			}
			rec := httptest.NewRecorder()
			
			e.ServeHTTP(rec, req)
			
			if rec.Code != http.StatusOK {
				t.Errorf("Expected status %d, got %d", http.StatusOK, rec.Code)
			}
			
			if rec.Body.String() != tt.expectedLang {
				t.Errorf("Expected language %s, got %s", tt.expectedLang, rec.Body.String())
			}
		})
	}
}

func TestLanguageDetectionWithConfig(t *testing.T) {
	mockService := &i18n.Service{}
	
	// Test with skipper
	config := LanguageDetectionConfig{
		Skipper: func(c echo.Context) bool {
			return c.Path() == "/skip"
		},
		I18nService: mockService,
	}
	
	e := echo.New()
	
	handler := func(c echo.Context) error {
		lang := GetLanguageFromEchoContext(c)
		return c.String(http.StatusOK, lang)
	}
	
	e.Use(LanguageDetectionWithConfig(config))
	e.GET("/test", handler)
	e.GET("/skip", handler)
	
	// Test normal path
	req := httptest.NewRequest(http.MethodGet, "/test", nil)
	req.Header.Set("Accept-Language", "en-US")
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	
	if rec.Body.String() != "en" {
		t.Errorf("Expected 'en', got %s", rec.Body.String())
	}
	
	// Test skipped path
	req = httptest.NewRequest(http.MethodGet, "/skip", nil)
	req.Header.Set("Accept-Language", "en-US")
	rec = httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	
	// Should return default language since middleware was skipped
	if rec.Body.String() != i18n.DefaultLanguage {
		t.Errorf("Expected default language %s, got %s", i18n.DefaultLanguage, rec.Body.String())
	}
}

func TestLanguageDetectionPanic(t *testing.T) {
	defer func() {
		if r := recover(); r == nil {
			t.Error("Expected panic when i18n service is nil")
		}
	}()
	
	// This should panic
	LanguageDetection(nil)
}

func TestGetLanguageFromEchoContext(t *testing.T) {
	e := echo.New()
	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)
	
	// Test without language set
	lang := GetLanguageFromEchoContext(c)
	if lang != i18n.DefaultLanguage {
		t.Errorf("Expected default language %s, got %s", i18n.DefaultLanguage, lang)
	}
	
	// Test with language set
	c.Set(i18n.LanguageContextKey, "es")
	lang = GetLanguageFromEchoContext(c)
	if lang != "es" {
		t.Errorf("Expected 'es', got %s", lang)
	}
	
	// Test with wrong type
	c.Set(i18n.LanguageContextKey, 123)
	lang = GetLanguageFromEchoContext(c)
	if lang != i18n.DefaultLanguage {
		t.Errorf("Expected default language %s, got %s", i18n.DefaultLanguage, lang)
	}
}
