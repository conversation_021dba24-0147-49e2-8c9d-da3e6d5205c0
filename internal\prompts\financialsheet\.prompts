Files:
@/internal/model/financialsheet/record.go
@/internal/model/financialsheet/category.go
@/internal/controller/financialsheet/controller.go
@/internal/service/financialsheet/service.go
@/internal/repository/financialsheet/mongo.go 

# League System
I need to implement a league systemfor the finanancialsheet. Following the business logic will be implemented in the internal/service/financialsheet. 

League System

About the Season
Each season lasts 3 months, during which you can progress through the leagues and earn special rewards. Stay consistent to reach the top!

Raid Rules
Log your transactions daily to accumulate Raids.

Maintain your streak to climb to higher leagues.

If you miss a day, you'll drop back to the Bronze League.

League Categories

🥉 Bronze
The start of the journey
    29 days or less streak

🥈 Silver
Consistent Investor
    30 to 59 days streak

🥇 Gold
Dedicated Investor
    60 to 89 days streak

💎 Diamond
Elite Investor
    90 or more days streak

Business logic
Create a daily habit tracker for maintaining consistent investment activity to climb through leagues (Bronze, Silver, Gold, Diamond). Include reminders to log transactions, monitor progress, and aim for special rewards by staying active and consistent throughout the season.