package content

import (
	"strings"
	"time"

	"github.com/imdario/mergo"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Wallet struct {
	ObjectID          primitive.ObjectID       `json:"-" bson:"_id,omitempty"`
	ID                string                   `json:"_id,omitempty" bson:"-"`
	Name              string                   `json:"name" bson:"name"`
	Logo              string                   `json:"logo" bson:"logo"`
	Color             string                   `json:"color" bson:"color"`
	Identifier        string                   `json:"identifier" bson:"identifier"`
	Groups            []*WalletGroup           `json:"groups" bson:"groups"`
	Chart             Chart                    `json:"chart" bson:"chart"`
	LastOpportunities []*InvestmentOpportunity `json:"lastOpportunities" bson:"lastOpportunities"`
	CreatedAt         time.Time                `json:"createdAt" bson:"createdAt"`
	UpdatedAt         time.Time                `json:"updatedAt" bson:"updatedAt"`
}

type Chart struct {
	Type       string `json:"type" bson:"type"`
	WalletX    []*int `json:"walletx" bson:"walletx"`
	WalletY    []*int `json:"wallety" bson:"wallety"`
	ReferenceX []*int `json:"referencex" bson:"referencex"`
	ReferenceY []*int `json:"referencey" bson:"referencey"`
}

type InvestmentOpportunity struct {
	Name        string    `json:"name" bson:"name"`
	Category    string    `json:"category" bson:"category"`
	Instruction string    `json:"instruction" bson:"instruction"`
	Reason      string    `json:"reason" bson:"reason"`
	Date        time.Time `json:"date" bson:"date"`
	Logo        string    `json:"logo" bson:"logo"`
	Detail      string    `json:"detail" bson:"detail"`
}

func (t *Wallet) Sanitize() *Wallet {
	t.ID = t.ObjectID.Hex()

	return t
}

func (t *Wallet) PrepareCreate() error {
	t.Name = strings.TrimSpace(t.Name)
	t.Identifier = strings.TrimSpace(strings.ToLower(t.Identifier))

	t.CreatedAt = time.Now()
	t.UpdatedAt = t.CreatedAt

	if err := t.ValidateCreate(); err != nil {
		return err
	}

	if t.Groups != nil && len(t.Groups) > 0 {
		for _, group := range t.Groups {
			if err := group.PrepareCreate(); err != nil {
				return err
			}
		}
	}

	return nil
}

func (t *Wallet) ValidateCreate() error {
	if t.Name == "" {
		return ErrWalletRequiredName
	}

	if t.Identifier == "" {
		return ErrWalletRequiredIdentifier
	}

	if t.Groups == nil || len(t.Groups) <= 0 {
		return ErrWalletRequiredGroups
	}

	return nil
}

func (t *Wallet) PrepareUpdate(newWallet *Wallet) error {

	if err := mergo.Merge(t, newWallet, mergo.WithOverride); err != nil {
		return err
	}
	t.Name = strings.TrimSpace(t.Name)
	t.Identifier = strings.TrimSpace(strings.ToLower(t.Identifier))

	t.UpdatedAt = time.Now()

	if t.ID == "" {
		if !t.ObjectID.IsZero() {
			t.ID = t.ObjectID.Hex()
		} else {
			return ErrWalletInvalidId
		}
	} else {
		tickerObjectId, err := primitive.ObjectIDFromHex(t.ID)
		if err != nil {
			return err
		}
		t.ObjectID = tickerObjectId
	}

	if err := t.ValidateUpdate(); err != nil {
		return err
	}

	if t.Groups != nil && len(t.Groups) > 0 {
		for _, group := range t.Groups {
			if err := group.PrepareCreate(); err != nil {
				return err
			}
		}
	}

	return nil
}

func (t *Wallet) ValidateUpdate() error {
	return t.PrepareCreate()
}
