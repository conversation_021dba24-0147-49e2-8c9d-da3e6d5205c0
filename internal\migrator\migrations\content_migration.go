package migrations

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/migrator/content"
	"go.mongodb.org/mongo-driver/mongo"
)

// ContentMigration implements the Migration interface for migrating content from JSON files
type ContentMigration struct {
	db          *mongo.Database
	basePath    string
	mode        content.Mode
	collections []string // list of collections to process; if empty, process all
}

// NewContentMigration creates a new content migration with the specified mode and collections filter.
// Pass an empty slice for collections to process all.
func NewContentMigration(db *mongo.Database, mode content.Mode, collections []string) *ContentMigration {
	return &ContentMigration{
		db:          db,
		basePath:    os.Getenv("MIGRATION_BASE_PATH"),
		mode:        mode,
		collections: collections,
	}
}

func (m *ContentMigration) Name() string {
	return "content_migration_2025_08_12_update_fix001"
}

func (m *ContentMigration) Up(ctx context.Context) error {
	if m.basePath == "" {
		log.Printf("Ignoring content migration due to invalid basePath")
		return nil
	}

	dir, err := os.ReadDir(m.basePath)
	if err != nil {
		return fmt.Errorf("failed to read migration directory: %w", err)
	}

	// Create a content migrator instance and set its mode
	contentMigrator := content.NewMigrator(m.db)
	contentMigrator.SetMode(m.mode)

	// Build lookup map for collections to process if provided
	allowed := make(map[string]bool)
	if len(m.collections) > 0 {
		for _, coll := range m.collections {
			allowed[strings.TrimSpace(coll)] = true
		}
	}

	for _, dirEntry := range dir {
		// Process only directories
		if !dirEntry.IsDir() {
			continue
		}

		// If a collection list is provided, only process those collections
		if len(allowed) > 0 {
			if _, ok := allowed[dirEntry.Name()]; !ok {
				log.Printf("Skipping collection '%s' as it's not in the allowed list", dirEntry.Name())
				continue
			}
		}

		err = m.handleCollection(ctx, contentMigrator, dirEntry.Name())
		if err != nil {
			return fmt.Errorf("failed to migrate collection %s: %w", dirEntry.Name(), err)
		}
	}

	return nil
}

func (m *ContentMigration) handleCollection(ctx context.Context, contentMigrator content.Migrator, collection string) error {
	path := fmt.Sprintf("%s/%s", m.basePath, collection)
	log.Printf("Starting content migration for collection: %s", collection)

	if err := contentMigrator.MigrateContent(ctx, collection, path); err != nil {
		return err
	}

	log.Printf("Successfully completed content migration for collection: %s", collection)
	return nil
}
