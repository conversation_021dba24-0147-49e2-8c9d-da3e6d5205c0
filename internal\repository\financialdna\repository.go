package financialdna

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialdna"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	// Read operations
	Find(ctx context.Context, id primitive.ObjectID) (*financialdna.FinancialDNATree, error)
	FindByUser(ctx context.Context, userID primitive.ObjectID) (*financialdna.FinancialDNATree, error)
}

type Writer interface {
	// Tree operations
	Create(ctx context.Context, tree *financialdna.FinancialDNATree) (string, error)

	// Update operations
	Update(ctx context.Context, tree *financialdna.FinancialDNATree) error

	// Delete operations
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type Repository interface {
	Reader
	Writer
}
