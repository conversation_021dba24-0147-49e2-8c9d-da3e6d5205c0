package s3

import (
	"context"
	"mime/multipart"
	"os"
)

// Service defines the interface for S3 operations
type Service interface {
	// UploadFile uploads a file to S3 and returns the URL
	UploadFile(ctx context.Context, file *multipart.FileHeader, folder string) (string, error)

	// DeleteFile deletes a file from S3
	DeleteFile(ctx context.Context, fileURL string) error
}

type service struct {
	bucketName string
	region     string
	endpoint   string
}

// New creates a new S3 service, initializing configuration from environment variables.
func New() Service {
	bucketName := os.Getenv("AWS_S3_BUCKET_NAME")
	region := os.Getenv("AWS_REGION")
	endpoint := os.Getenv("AWS_S3_ENDPOINT") // Optional custom endpoint, can be empty

	return &service{
		bucketName: bucketName,
		region:     region,
		endpoint:   endpoint,
	}
}
