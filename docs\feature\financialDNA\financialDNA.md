## Visão Geral

O DNA Financeiro é uma aplicação que permite mapear e visualizar o perfil financeiro de uma família através de uma árvore genealógica interativa. O objetivo é identificar padrões financeiros familiares e ajudar a quebrar ciclos negativos, promovendo uma melhor educação financeira entre gerações.

## Funcionalidades Principais

### 1. Árvore Genealógica Financeira

- Visualização de até 4 gerações (bisavós até netos)
- Mapeamento bidirecional (ancestrais e descendentes)
- Edição interativa de membros da família
- Adição dinâmica de filhos e netos

### 2. Perfis Financeiros

Cada membro da família pode ser classificado em uma das seguintes categorias:

1. **Investidor** (✨)

    - Possui investimentos diversificados
    - Mantém reserva de emergência
    - Planeja financeiramente o futuro
2. **Equilibrado** (⚖️)

    - Paga contas em dia
    - Possui alguma reserva
    - Sem dívidas significativas
3. **Endividado** (⚠️)

    - Possui dívidas ativas
    - Dificuldade em manter contas em dia
    - Sem reserva financeira
4. **Superendividado** (🚨)

    - Dívidas em atraso
    - Nome negativado
    - Comprometimento significativo da renda

### 3. Análises e Métricas

#### Progresso da Árvore

- Acompanhamento do preenchimento da árvore genealógica
- Cálculo de porcentagem de membros com status definido
- Visualização clara do progresso total

#### Quebra de Ciclos

- Análise probabilística baseada no histórico familiar
- Cálculo da probabilidade de se tornar investidor com base em pesos e multiplicadores
- Fórmula de cálculo: Probabilidade (%) = 50% + (Pontuação Total × 10%)
- Exemplo de cálculo:
    - Pai (Endividado): -1 × 2,0 = -2,0
    - Mãe (Equilibrado): +1 × 2,0 = +2,0
    - Avô paterno (Superendividado): -2 × 1,0 = -2,0
    - Avó materna (Investidor): +2 × 1,0 = +2,0
    - Bisavô paterno (Endividado): -1 × 0,5 = -0,5
    - Pontuação Total = -0,5
    - Probabilidade = 50% + (-0,5 × 10%) = 45%
- Categorias de probabilidade enviadas para o frontend:
    - ≥ 70%: "high" (alta)
    - 40% a 69%: "medium" (média)
    - < 40%: "low" (baixa)
- Limites aplicados: mínimo de 10% e máximo de 95% de probabilidade

#### Distribuição Financeira

- Visualização percentual dos perfis financeiros na família
- Análise da distribuição dos status financeiros
- Identificação de padrões familiares

## Aspectos Técnicos

### Interface do Usuário

- Design responsivo (mobile e desktop)
- Animações suaves para melhor experiência do usuário
- Sistema de cores intuitivo para status financeiros:
    - Verde: Investidor
    - Azul: Equilibrado
    - Amarelo: Endividado
    - Vermelho: Superendividado

### Persistência de Dados

- Todos os dados da árvore devem ser persistidos
- Alterações devem ser salvas automaticamente
- Histórico de mudanças deve ser mantido

### Performance

- Renderização eficiente da árvore genealógica
- Cálculos de métricas em tempo real
- Otimização para árvores grandes

## Regras de Negócio

1. **Edição de Membros**

    - Cada membro deve ter um nome único
    - O status financeiro é opcional inicialmente
    - É permitido remover membros cadastrados desde que sejam filhos
2. **Cálculo de Probabilidade**

    - Baseado apenas em membros com status definido
    - Pesos por perfil financeiro:
        - Investidor: +2
        - Equilibrado: +1
        - Endividado: -1
        - Superendividado: -2
    - Multiplicadores por grau de parentesco:
        - Pais: ×2,0
        - Avós: ×1,0
        - Bisavós: ×0,5
    - Atualização em tempo real
3. **Limitações da Árvore**

    - Máximo de 4 gerações
    - Bisavós não podem ter pais
    - Netos não podem ter filhos

## Métricas de Sucesso

4. **Engajamento**

    - Número de árvores completadas
    - Tempo médio de preenchimento
    - Taxa de retorno dos usuários
5. **Impacto**

    - Mudança no perfil financeiro ao longo do tempo
    - Quebra de ciclos negativos
    - Aumento no número de investidores por família

## Próximas Etapas

### Fase 1 (Atual)

- ✅ Implementação básica da árvore
- ✅ Sistema de status financeiro
- ✅ Métricas iniciais

### Fase 2 (Planejada)

- 🔄 Sistema de contas de usuário
- 🔄 Persistência de dados
- 🔄 Compartilhamento de árvores

### Fase 3 (Futura)

- 📋 Recomendações personalizadas
- 📋 Integração com educação financeira
- 📋 Comunidade e comparativos

## Considerações de Segurança

- Proteção de dados pessoais
- Acesso restrito à árvore familiar
- Backups regulares dos dados
- Conformidade com LGPD

Esta documentação serve como guia principal para o desenvolvimento e evolução do DNA Financeiro, devendo ser atualizada conforme novas funcionalidades e requisitos surjam.