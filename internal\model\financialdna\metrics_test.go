package financialdna

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestCalculateBreakCycles(t *testing.T) {
	// Create a test tree
	tree := &FinancialDNATree{
		ObjectID: primitive.NewObjectID(),
		UserID:   "test-user",
		Members:  []*FamilyMember{},
	}

	// Add "me" as the central node
	me, _ := tree.AddMember("Eu", FamilyMemberIconMe, FinancialStatusBalanced)

	// Add parents
	father, mother, _ := tree.AddParents(me, "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", FamilyMemberIconFather, FamilyMemberIconMother, FinancialStatusUndefined)

	// Set financial statuses for testing
	father.FinancialStatus = FinancialStatusIndebted
	mother.FinancialStatus = FinancialStatusBalanced

	// Add grandparents
	paternalGrandfather, paternalGrandmother, _ := tree.AddParents(father, "<PERSON><PERSON><PERSON> Pat<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Pat<PERSON><PERSON>", FamilyMember<PERSON><PERSON><PERSON><PERSON><PERSON>, Family<PERSON>ember<PERSON>conGrandmother, FinancialStatusUndefined)
	maternalGrandfather, maternal<PERSON>randmother, _ := tree.AddParents(mother, "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", FamilyMemberIconGrandfather, FamilyMemberIconGrandmother, FinancialStatusUndefined)

	// Set financial statuses for grandparents
	paternalGrandfather.FinancialStatus = FinancialStatusOverindebted
	paternalGrandmother.FinancialStatus = FinancialStatusBalanced
	maternalGrandfather.FinancialStatus = FinancialStatusInvestor
	maternalGrandmother.FinancialStatus = FinancialStatusBalanced

	// Add great-grandparents
	bisavoPaterno1, bisavoPaterna1, _ := tree.AddParents(paternalGrandfather, "Bisavô Paterno 1", "Bisavó Paterna 1", FamilyMemberIconGreatgrandfather, FamilyMemberIconGreatgrandmother, FinancialStatusUndefined)

	// Set financial status for great-grandparents
	bisavoPaterno1.FinancialStatus = FinancialStatusIndebted
	bisavoPaterna1.FinancialStatus = FinancialStatusBalanced

	// Calculate break cycles
	err := tree.CalculateBreakCycles()
	assert.NoError(t, err)

	// Verify the calculation
	// Expected calculation:
	// Father (Indebted): -1.0 * 2.0 = -2.0
	// Mother (Balanced): 1.0 * 2.0 = 2.0
	// Paternal Grandfather (Overindebted): -2.0 * 1.0 = -2.0
	// Paternal Grandmother (Balanced): 1.0 * 1.0 = 1.0
	// Maternal Grandfather (Investor): 2.0 * 1.0 = 2.0
	// Maternal Grandmother (Balanced): 1.0 * 1.0 = 1.0
	// Bisavô Paterno 1 (Indebted): -1.0 * 0.5 = -0.5
	// Bisavó Paterna 1 (Balanced): 1.0 * 0.5 = 0.5
	// Total score: -2.0 + 2.0 + (-2.0) + 1.0 + 2.0 + 1.0 + (-0.5) + 0.5 = 2.0
	// Probability: 50 + (2.0 * 10) = 70%

	assert.Equal(t, uint8(70), tree.BreakCycles.Probability)
	assert.Equal(t, ProbabilityCategoryHigh, tree.BreakCycles.Category)

	// Test with a different scenario (low probability)
	// Reset financial statuses to create a negative scenario
	father.FinancialStatus = FinancialStatusOverindebted
	mother.FinancialStatus = FinancialStatusOverindebted
	paternalGrandfather.FinancialStatus = FinancialStatusOverindebted
	paternalGrandmother.FinancialStatus = FinancialStatusOverindebted
	maternalGrandfather.FinancialStatus = FinancialStatusIndebted
	maternalGrandmother.FinancialStatus = FinancialStatusIndebted

	// Calculate break cycles again
	err = tree.CalculateBreakCycles()
	assert.NoError(t, err)

	// Expected calculation:
	// Father (Overindebted): -2.0 * 2.0 = -4.0
	// Mother (Overindebted): -2.0 * 2.0 = -4.0
	// Paternal Grandfather (Overindebted): -2.0 * 1.0 = -2.0
	// Paternal Grandmother (Overindebted): -2.0 * 1.0 = -2.0
	// Maternal Grandfather (Indebted): -1.0 * 1.0 = -1.0
	// Maternal Grandmother (Indebted): -1.0 * 1.0 = -1.0
	// Bisavô Paterno 1 (Indebted): -1.0 * 0.5 = -0.5
	// Bisavó Paterna 1 (Balanced): 1.0 * 0.5 = 0.5
	// Total score: -4.0 + (-4.0) + (-2.0) + (-2.0) + (-1.0) + (-1.0) + (-0.5) + 0.5 = -14.0
	// Probability: 50 + (-14.0 * 10) = -90%, but limited to 10%

	assert.Equal(t, uint8(10), tree.BreakCycles.Probability)
	assert.Equal(t, ProbabilityCategoryLow, tree.BreakCycles.Category)

	// Test with a medium probability scenario
	father.FinancialStatus = FinancialStatusBalanced
	mother.FinancialStatus = FinancialStatusBalanced
	paternalGrandfather.FinancialStatus = FinancialStatusIndebted
	paternalGrandmother.FinancialStatus = FinancialStatusBalanced
	maternalGrandfather.FinancialStatus = FinancialStatusBalanced
	maternalGrandmother.FinancialStatus = FinancialStatusBalanced

	// Calculate break cycles again
	err = tree.CalculateBreakCycles()
	assert.NoError(t, err)

	// Expected calculation:
	// Father (Balanced): 1.0 * 2.0 = 2.0
	// Mother (Balanced): 1.0 * 2.0 = 2.0
	// Paternal Grandfather (Indebted): -1.0 * 1.0 = -1.0
	// Paternal Grandmother (Balanced): 1.0 * 1.0 = 1.0
	// Maternal Grandfather (Balanced): 1.0 * 1.0 = 1.0
	// Maternal Grandmother (Balanced): 1.0 * 1.0 = 1.0
	// Bisavô Paterno 1 (Indebted): -1.0 * 0.5 = -0.5
	// Bisavó Paterna 1 (Balanced): 1.0 * 0.5 = 0.5
	// Total score: 2.0 + 2.0 + (-1.0) + 1.0 + 1.0 + 1.0 + (-0.5) + 0.5 = 6.0
	// Probability: 50 + (6.0 * 10) = 110%, but limited to 95%

	assert.Equal(t, uint8(95), tree.BreakCycles.Probability)
	assert.Equal(t, ProbabilityCategoryHigh, tree.BreakCycles.Category)

	// Test with a medium probability scenario
	father.FinancialStatus = FinancialStatusIndebted
	mother.FinancialStatus = FinancialStatusIndebted
	paternalGrandfather.FinancialStatus = FinancialStatusBalanced
	paternalGrandmother.FinancialStatus = FinancialStatusBalanced
	maternalGrandfather.FinancialStatus = FinancialStatusBalanced
	maternalGrandmother.FinancialStatus = FinancialStatusBalanced

	// Calculate break cycles again
	err = tree.CalculateBreakCycles()
	assert.NoError(t, err)

	// Expected calculation:
	// Father (Indebted): -1.0 * 2.0 = -2.0
	// Mother (Indebted): -1.0 * 2.0 = -2.0
	// Paternal Grandfather (Balanced): 1.0 * 1.0 = 1.0
	// Paternal Grandmother (Balanced): 1.0 * 1.0 = 1.0
	// Maternal Grandfather (Balanced): 1.0 * 1.0 = 1.0
	// Maternal Grandmother (Balanced): 1.0 * 1.0 = 1.0
	// Bisavô Paterno 1 (Indebted): -1.0 * 0.5 = -0.5
	// Bisavó Paterna 1 (Balanced): 1.0 * 0.5 = 0.5
	// Total score: -2.0 + (-2.0) + 1.0 + 1.0 + 1.0 + 1.0 + (-0.5) + 0.5 = 0.0
	// Probability: 50 + (0.0 * 10) = 50%

	assert.Equal(t, uint8(50), tree.BreakCycles.Probability)
	assert.Equal(t, ProbabilityCategoryMedium, tree.BreakCycles.Category)
}

func TestCalculateTreeProgress(t *testing.T) {
	// Create a test tree
	tree := &FinancialDNATree{
		ObjectID: primitive.NewObjectID(),
		UserID:   "test-user",
		Members:  []*FamilyMember{},
	}

	// Add members with different statuses
	// Using FamilyMemberIconChild as a placeholder icon for these test members
	tree.AddMember("Member 1", FamilyMemberIconChild, FinancialStatusInvestor)
	tree.AddMember("Member 2", FamilyMemberIconChild, FinancialStatusBalanced)
	tree.AddMember("Member 3", FamilyMemberIconChild, FinancialStatusIndebted)
	tree.AddMember("Member 4", FamilyMemberIconChild, FinancialStatusUndefined)
	tree.AddMember("Member 5", FamilyMemberIconChild, FinancialStatusUndefined)

	// Calculate tree progress
	tree.CalculateTreeProgress()

	// Verify the calculation
	assert.Equal(t, uint8(5), tree.TreeProgress.Members)
	assert.Equal(t, uint8(3), tree.TreeProgress.RegisteredMembers)
	assert.Equal(t, uint8(60), tree.TreeProgress.Progress) // 3/5 * 100 = 60%
}

func TestCalculateFinancialDistribution(t *testing.T) {
	// Create a test tree
	tree := &FinancialDNATree{
		ObjectID: primitive.NewObjectID(),
		UserID:   "test-user",
		Members:  []*FamilyMember{},
	}

	// Add members with different statuses
	// Using FamilyMemberIconChild as a placeholder icon for these test members
	tree.AddMember("Member 1", FamilyMemberIconChild, FinancialStatusInvestor)
	tree.AddMember("Member 2", FamilyMemberIconChild, FinancialStatusInvestor)
	tree.AddMember("Member 3", FamilyMemberIconChild, FinancialStatusBalanced)
	tree.AddMember("Member 4", FamilyMemberIconChild, FinancialStatusIndebted)
	tree.AddMember("Member 5", FamilyMemberIconChild, FinancialStatusOverindebted)
	tree.AddMember("Member 6", FamilyMemberIconChild, FinancialStatusOverindebted)
	tree.AddMember("Member 7", FamilyMemberIconChild, FinancialStatusOverindebted)
	tree.AddMember("Member 8", FamilyMemberIconChild, FinancialStatusUndefined) // This should not count in the distribution

	// Calculate financial distribution
	tree.CalculateFinancialDistribution()

	// Verify the calculation
	assert.Equal(t, uint8(25), tree.FinancialDistribution.Investor)  // 2/8 * 100 = 25%
	assert.Equal(t, uint8(13), tree.FinancialDistribution.Balanced)  // 1/8 * 100 = 12.5% rounded to 13%
	assert.Equal(t, uint8(13), tree.FinancialDistribution.Indebted)  // 1/8 * 100 = 12.5% rounded to 13%
	assert.Equal(t, uint8(38), tree.FinancialDistribution.Overindeb) // 3/8 * 100 = 37.5% rounded to 38%
}
