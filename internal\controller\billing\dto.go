package billing

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// Plan DTOs
type CreatePlanRequest struct {
	Name             string          `json:"name" validate:"required,min=3,max=100"`
	Description      string          `json:"description" validate:"max=500"`
	Price            monetary.Amount `json:"price" validate:"required,min=0"`
	Currency         string          `json:"currency" validate:"required,len=3"`
	Features         []string        `json:"features"`
	HotmartProductID string          `json:"hotmartProductId,omitempty"`
	AppleProductID   string          `json:"appleProductId,omitempty"`
	DurationMonths   int             `json:"durationMonths" validate:"required,min=1,max=120"`
	TrialDays        int             `json:"trialDays" validate:"min=0,max=30"`
	AutoRenew        bool            `json:"autoRenew"`
	Status           string          `json:"status" validate:"required,oneof=active inactive"`
}

type UpdatePlanRequest struct {
	Name             string          `json:"name" validate:"required,min=3,max=100"`
	Description      string          `json:"description" validate:"max=500"`
	Price            monetary.Amount `json:"price" validate:"required,min=0"`
	Currency         string          `json:"currency" validate:"required,len=3"`
	Features         []string        `json:"features"`
	HotmartProductID string          `json:"hotmartProductId,omitempty"`
	AppleProductID   string          `json:"appleProductId,omitempty"`
	DurationMonths   int             `json:"durationMonths" validate:"required,min=1,max=120"`
	TrialDays        int             `json:"trialDays" validate:"min=0,max=30"`
	AutoRenew        bool            `json:"autoRenew"`
	Status           string          `json:"status" validate:"required,oneof=active inactive archived"`
}

type PlanResponse struct {
	ID               string          `json:"id"`
	Name             string          `json:"name"`
	Description      string          `json:"description"`
	Price            monetary.Amount `json:"price"`
	Currency         string          `json:"currency"`
	Features         []string        `json:"features"`
	HotmartProductID string          `json:"hotmartProductId,omitempty"`
	AppleProductID   string          `json:"appleProductId,omitempty"`
	DurationMonths   int             `json:"durationMonths"`
	TrialDays        int             `json:"trialDays"`
	AutoRenew        bool            `json:"autoRenew"`
	Status           string          `json:"status"`
	CreatedAt        time.Time       `json:"createdAt"`
	UpdatedAt        time.Time       `json:"updatedAt"`
}

// Subscription DTOs
type CreateSubscriptionRequest struct {
	PlanID   string `json:"planId" validate:"required"`
	Provider string `json:"provider" validate:"required,oneof=hotmart applepay"`
}

type SubscriptionResponse struct {
	ID                     string     `json:"id"`
	UserID                 string     `json:"userId"`
	PlanID                 string     `json:"planId"`
	Status                 string     `json:"status"`
	Provider               string     `json:"provider"`
	ProviderSubscriptionID string     `json:"providerSubscriptionId,omitempty"`
	StartDate              time.Time  `json:"startDate"`
	EndDate                time.Time  `json:"endDate"`
	TrialEndDate           *time.Time `json:"trialEndDate,omitempty"`
	AutoRenew              bool       `json:"autoRenew"`
	CancelledAt            *time.Time `json:"cancelledAt,omitempty"`
	CancellationReason     string     `json:"cancellationReason,omitempty"`
	CreatedAt              time.Time  `json:"createdAt"`
	UpdatedAt              time.Time  `json:"updatedAt"`
}

type CancelSubscriptionRequest struct {
	Reason string `json:"reason" validate:"required,min=3,max=200"`
}

// Payment DTOs
type PaymentResponse struct {
	ID                    string          `json:"id"`
	UserID                string          `json:"userId"`
	SubscriptionID        string          `json:"subscriptionId,omitempty"`
	Provider              string          `json:"provider"`
	ProviderTransactionID string          `json:"providerTransactionId"`
	Amount                monetary.Amount `json:"amount"`
	Currency              string          `json:"currency"`
	Status                string          `json:"status"`
	Type                  string          `json:"type"`
	ProcessedAt           *time.Time      `json:"processedAt,omitempty"`
	FailureReason         string          `json:"failureReason,omitempty"`
	CreatedAt             time.Time       `json:"createdAt"`
	UpdatedAt             time.Time       `json:"updatedAt"`
}

// Access Control DTOs
type UserAccessResponse struct {
	HasActiveSubscription bool     `json:"hasActiveSubscription"`
	Features              []string `json:"features"`
}

// Webhook DTOs
type HotmartWebhookRequest struct {
	Event        string                 `json:"event"`
	OrderID      string                 `json:"order_id"`
	OrderStatus  string                 `json:"order_status"`
	Product      map[string]interface{} `json:"Product"`
	Customer     map[string]interface{} `json:"Customer"`
	Subscription map[string]interface{} `json:"Subscription"`
	Amount       int                    `json:"amount"`
	Currency     string                 `json:"currency"`
	Data         map[string]interface{} `json:"-"` // Store full webhook data
}

type ApplePayWebhookRequest struct {
	NotificationType      string                 `json:"notificationType"`
	TransactionID         string                 `json:"transactionId"`
	OriginalTransactionID string                 `json:"originalTransactionId"`
	ProductID             string                 `json:"productId"`
	PurchaseDate          string                 `json:"purchaseDate"`
	ExpiresDate           string                 `json:"expiresDate"`
	Environment           string                 `json:"environment"`
	Status                string                 `json:"status"`
	Amount                int                    `json:"amount"`
	Currency              string                 `json:"currency"`
	Data                  map[string]interface{} `json:"-"` // Store full webhook data
}

// Response DTOs
type CreateResponse struct {
	ID string `json:"id"`
}

type MessageResponse struct {
	Message string `json:"message"`
}

// Helper functions to convert between models and DTOs

func PlanToResponse(plan *billing.Plan) *PlanResponse {
	return &PlanResponse{
		ID:               plan.ID,
		Name:             plan.Name,
		Description:      plan.Description,
		Price:            plan.Price,
		Currency:         plan.Currency,
		Features:         plan.Features,
		HotmartProductID: plan.HotmartProductID,
		AppleProductID:   plan.AppleProductID,
		DurationMonths:   plan.DurationMonths,
		TrialDays:        plan.TrialDays,
		AutoRenew:        plan.AutoRenew,
		Status:           string(plan.Status),
		CreatedAt:        plan.CreatedAt,
		UpdatedAt:        plan.UpdatedAt,
	}
}

func SubscriptionToResponse(subscription *billing.Subscription) *SubscriptionResponse {
	return &SubscriptionResponse{
		ID:                     subscription.ID,
		UserID:                 subscription.UserID.Hex(),
		PlanID:                 subscription.PlanID.Hex(),
		Status:                 string(subscription.Status),
		Provider:               string(subscription.Provider),
		ProviderSubscriptionID: subscription.ProviderSubscriptionID,
		StartDate:              subscription.StartDate,
		EndDate:                subscription.EndDate,
		TrialEndDate:           subscription.TrialEndDate,
		AutoRenew:              subscription.AutoRenew,
		CancelledAt:            subscription.CancelledAt,
		CancellationReason:     subscription.CancellationReason,
		CreatedAt:              subscription.CreatedAt,
		UpdatedAt:              subscription.UpdatedAt,
	}
}

func PaymentToResponse(payment *billing.Payment) *PaymentResponse {
	response := &PaymentResponse{
		ID:                    payment.ID,
		UserID:                payment.UserID.Hex(),
		Provider:              string(payment.Provider),
		ProviderTransactionID: payment.ProviderTransactionID,
		Amount:                payment.Amount,
		Currency:              payment.Currency,
		Status:                string(payment.Status),
		Type:                  string(payment.Type),
		ProcessedAt:           payment.ProcessedAt,
		FailureReason:         payment.FailureReason,
		CreatedAt:             payment.CreatedAt,
		UpdatedAt:             payment.UpdatedAt,
	}

	if payment.SubscriptionID != nil {
		response.SubscriptionID = payment.SubscriptionID.Hex()
	}

	return response
}
