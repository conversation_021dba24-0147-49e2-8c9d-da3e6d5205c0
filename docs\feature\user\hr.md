# HR Employees Feature

This document describes the HR employee management feature that allows Human Resources personnel to retrieve and manage employees under their referral hierarchy through the RBAC system.

## Overview

The HR employees feature provides a comprehensive way for HR users to access employees that were referred by them through the referral system. This implementation follows the RBAC (Role-Based Access Control) pattern and integrates seamlessly with the authentication and authorization infrastructure.

### Key Features

- **Role-based access control**: Only HR users can access employee data
- **Referral-based relationships**: HR users see only employees they personally referred
- **Company-specific access**: Support for multi-tenant HR roles (`hr_company_{companyId}`)
- **Clean architecture**: Follows Controller → Service → Repository pattern
- **RBAC service integration**: Leverages centralized access control logic

## Endpoint Details

### GET /v2/users/hr/employees

**Description**: Retrieves all employees that were referred by the requesting HR user.

**Authentication**: Required (JWT token)

**Authorization**: HR role required (Level 3 in role hierarchy)
- Base HR role: `human_resources`
- Company-specific HR roles: `hr_company_{companyId}` (e.g., `hr_company_123`)

**Request Headers**:
```
Authorization: Bearer <JWT_TOKEN>
```

**Response Format**:
```json
[
  {
    "id": "user_id_1",
    "name": "Employee Name",
    "email": "<EMAIL>",
    "avatar": "avatar_url",
    "roles": ["user"],
    "referringUserId": "hr_user_id",
    "usedReferralCode": "HR_REFERRAL_CODE",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
    // Other sanitized user fields...
  }
]
```

**Response Codes**:
- `200 OK`: Successfully retrieved employees list
- `401 Unauthorized`: Invalid or missing JWT token
- `403 Forbidden`: User does not have HR role permissions
- `500 Internal Server Error`: Server error

## Implementation Details

### Architecture

The implementation follows the clean architecture pattern with three layers:

1. **Controller Layer** (`internal/controller/user/hr.go`)
   - Handles HTTP request/response
   - Validates JWT token and user context
   - Calls service layer for business logic

2. **Service Layer** (`internal/service/user/hr.go`)
   - Uses RBAC service for role validation and filter building
   - Implements business logic for employee retrieval
   - Calls repository layer with RBAC-generated filters

3. **Repository Layer** (`internal/repository/user/mongo.go`)
   - Implements generic `FindWithFilter` method for flexible querying
   - Accepts MongoDB filters from RBAC service
   - Returns raw user data from database

4. **RBAC Service** (`internal/service/rbac/service.go`)
   - Provides `BuildHREmployeeFilter` method for HR-specific filtering
   - Validates HR permissions and builds appropriate MongoDB filters
   - Ensures consistent access control patterns across the application

### Middleware Chain

The endpoint uses the standard RBAC middleware chain:

1. **`AuthGuard()`** - Validates JWT token and ensures authentication
2. **`UserContextMiddleware()`** - Extracts user information from JWT and stores in context
3. **`HROnly()`** - Ensures user has HR role permissions (Level 3)

```go
// Route registration example
group.GET("/hr/employees", controller.FindEmployeesByHR(),
    middlewares.AuthGuard(),
    middlewares.UserContextMiddleware(),
    middlewares.HROnly())
```

### HROnly Middleware Behavior

The `HROnly()` middleware has specific characteristics:

- **Excludes Admin users**: Intentionally designed to only allow HR roles, not admin
- **Includes company-specific HR**: Recognizes both base and company-specific HR roles
- **Uses `auth.IsHumanResources()`**: Leverages centralized role checking logic

```go
// HROnly implementation
func HROnly() echo.MiddlewareFunc {
    return func(next echo.HandlerFunc) echo.HandlerFunc {
        return func(c echo.Context) error {
            userCtx, err := GetUserContext(c)
            if err != nil {
                return err
            }

            if auth.IsHumanResources(userCtx.Roles) {
                return next(c)
            }

            return errors.New(errors.Middleware, "HR access required", errors.Forbidden, nil)
        }
    }
}
```

**Note**: If admin access is also needed, use `HROrAdmin()` middleware instead.

### Data Relationships

The referral system works as follows:
- HR users have a `referralCode` field that they share with employees
- When an employee registers, they provide the HR's referral code
- The system stores:
  - `usedReferralCode`: The referral code the employee used (HR's code)
  - `referringUserId`: The ID of the HR user who referred the employee

### RBAC Service Integration

The implementation leverages the centralized RBAC service for consistent access control:

```go
// Service method using RBAC service
func (s *service) FindEmployeesByHR(ctx context.Context, hrUserID string, hrUserRoles []string) ([]*model.User, error) {
    // Use RBAC service to build appropriate filter for HR employee access
    filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)

    // Apply filter in repository query
    return s.Repository.FindWithFilter(ctx, filter)
}
```

The `BuildHREmployeeFilter` method:
- **Validates HR permissions**: Checks if user has HR role access
- **Builds MongoDB filter**: Creates filter for referral relationships
- **Handles admin access**: Admin users get unrestricted access
- **Returns impossible condition**: Non-HR users get filter that returns no results

```go
// RBAC service filter building
func (s *service) BuildHREmployeeFilter(ctx context.Context, userID string, userRoles []string) bson.M {
    permissions := auth.GetPermissions(userRoles)

    // Admin can access all employee data
    if permissions.CanAccessAllData {
        return bson.M{}
    }

    // Only HR users can use this filter
    if !permissions.CanAccessCompanyData {
        // Return impossible condition for non-HR users
        return bson.M{"_id": primitive.NewObjectID()}
    }

    // HR users can access employees they referred
    return bson.M{"referringUserId": userID}
}
```

### Security Considerations

- **Role-based access**: Only users with HR roles (Level 3) can access this endpoint
- **Referral-based filtering**: HR users can only see employees they personally referred
- **Data sanitization**: User data is sanitized before being returned (sensitive fields removed)
- **Centralized access control**: Leverages RBAC service for consistent security patterns
- **MongoDB filtering**: Database-level filtering ensures data isolation
- **Admin exclusion**: `HROnly()` middleware intentionally excludes admin users for HR-specific data
- **Company scope**: Company-specific HR roles limit access to their company's employees

## Usage Examples

### Successful Request

```bash
curl -X GET "https://api.dinbora.com.br/v2/users/hr/employees" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### Response

```json
[
  {
    "id": "60f7b3b3b3b3b3b3b3b3b3b3",
    "name": "João Silva",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "roles": ["user"],
    "referringUserId": "60f7b3b3b3b3b3b3b3b3b3b4",
    "usedReferralCode": "HR_JOAO_2023",
    "createdAt": "2023-06-15T10:30:00Z",
    "updatedAt": "2023-06-15T10:30:00Z"
  },
  {
    "id": "60f7b3b3b3b3b3b3b3b3b3b5",
    "name": "Maria Santos",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar2.jpg",
    "roles": ["user"],
    "referringUserId": "60f7b3b3b3b3b3b3b3b3b3b4",
    "usedReferralCode": "HR_JOAO_2023",
    "createdAt": "2023-06-20T14:15:00Z",
    "updatedAt": "2023-06-20T14:15:00Z"
  }
]
```

## Testing

The implementation includes comprehensive tests:

- **Repository Tests**: Verify MongoDB queries work correctly
- **Service Tests**: Validate business logic and role checking
- **Integration Tests**: End-to-end testing of the complete flow

To run tests:
```bash
go test ./internal/repository/user -v
go test ./internal/service/user -v
go test ./internal/controller/user -v
```

## RBAC Integration Patterns

### Role Hierarchy Context

The HR feature operates within the 5-level role hierarchy:

```
Level 5: Admin          → Full system access (can access all HR data via different endpoints)
Level 4: Director       → Department-wide access (not HR-specific)
Level 3: HR             → Company/referral-based access ← THIS FEATURE
Level 2: Teacher        → Student access (not HR-related)
Level 1: Student/User   → Own data only (not HR-related)
```

### Alternative Middleware Options

Depending on business requirements, different middleware can be used:

```go
// HR only (current implementation) - excludes admin
hrOnly := middlewares.HROnly()

// HR or Admin - includes admin access
hrOrAdmin := middlewares.HROrAdmin()

// Custom role guard for specific business logic
customHR := middlewares.RoleGuard(middlewares.RoleGuardConfig{
    RequiredRoles: []auth.Role{auth.RoleHumanResources},
    // Could add company-specific logic here
})
```

### Company-Specific HR Implementation

For multi-tenant scenarios:

```go
// Create company-specific HR role
companyHRRole := auth.CreateHRCompanyRole("123") // "hr_company_123"

// Check company access
companyIDs := auth.GetHRCompanyIDs(userRoles) // ["123"]

// Filter employees by company
if len(permissions.CompanyIDs) > 0 {
    filter = bson.M{
        "$and": []bson.M{
            {"referringUserId": userID},
            {"companyId": bson.M{"$in": permissions.CompanyIDs}},
        },
    }
}
```

## Future Enhancements

Potential improvements for future versions:

### Functional Enhancements
- **Pagination support**: Handle large employee lists efficiently
- **Filtering and sorting**: Advanced query options for employee data
- **Employee statistics**: Analytics and reporting for HR users
- **Bulk operations**: Mass updates or actions on employee data

### RBAC Enhancements
- **Department-level HR**: HR roles scoped to specific departments
- **Time-based access**: Temporary HR permissions with expiration
- **Audit logging**: Track HR access patterns and data usage
- **Cross-company access**: HR users managing multiple companies

### Integration Enhancements
- **Real-time updates**: WebSocket notifications for employee changes
- **External HR systems**: Integration with third-party HR platforms
- **Advanced referral tracking**: Multi-level referral hierarchies
