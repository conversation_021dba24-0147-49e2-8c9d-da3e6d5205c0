package progression

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/content"
)

type Lesson struct {
	Identifier string           `json:"identifier,omitempty" bson:"identifier"`
	Path       []*LessonContent `json:"path,omitempty" bson:"path"`
	Current    string           `json:"current,omitempty" bson:"current"`
	Completed  bool             `json:"completed,omitempty" bson:"completed"`
	Available  bool             `json:"available"`
	Rewarded   bool             `json:"rewarded" bson:"rewarded"`
}

type LessonContent struct {
	Identifier string               `json:"identifier,omitempty" bson:"identifier,omitempty"`
	Choice     *ModuleContentChoice `json:"choice,omitempty" bson:"choice,omitempty"`
	Timestamp  time.Time            `json:"timestamp" bson:"timestamp"`
}

type LessonCard struct {
	Name       string `json:"name" bson:"name"`
	Identifier string `json:"identifier" bson:"identifier"`
	Type       string `json:"type" bson:"type"`
	Logo       string `json:"logo" bson:"logo"`
	Color      string `json:"color" bson:"color"`
	Order      uint8  `json:"order,omitempty" bson:"order,omitempty"`
	Completed  bool   `json:"completed" bson:"completed"`
	Available  bool   `json:"available"`
	Rewarded   bool   `json:"rewarded"`
	Current    string `json:"current,omitempty" bson:"current,omitempty"`
}

type ModuleContentChoice struct {
	Identifier string `json:"identifier" bson:"identifier"`
	Next       string `json:"next" bson:"next"`
}

func (l *Lesson) ToCard(content *content.Lesson) *LessonCard {
	return &LessonCard{
		Name:       content.Name,
		Identifier: content.Identifier,
		Type:       "LESSON",
		Logo:       content.Logo,
		Color:      content.Color,
		Order:      content.Order,
		Completed:  l.Completed,
		Available:  l.Available,
		Current:    l.Current,
		Rewarded:   l.Rewarded,
	}
}

func (l *Lesson) ExistsContent(identifier string) bool {
	for _, content := range l.Path {
		if content.Identifier == identifier {
			return true
		}
	}

	return false
}

func (l *Lesson) Register(body *ProgressionBody, lessonContent *content.Lesson) {

}
