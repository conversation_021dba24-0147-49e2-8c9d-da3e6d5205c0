import os
import sys
from PIL import Image, ImageSequence
import argparse

# --- Configuration ---
# Define "white" baseline
WHITE_RGB = (255, 255, 255)

# Tolerance for considering a pixel "light" or "white-ish" border
# Start relatively high based on previous feedback, but maybe lower than max.
BORDER_TOLERANCE = 100 # Adjust based on results (try 80, 100, 150...)

# Number of removal passes to run per frame
NUM_PASSES = 2 # Try 2 or 3. More passes are more aggressive.

# Minimum alpha for a pixel to be considered *not* transparent initially
MIN_OPAQUE_ALPHA = 1
# --- End Configuration ---

def is_close_to_white(rgb, tolerance):
    """Checks if an RGB tuple is close to white within tolerance."""
    if not isinstance(rgb, tuple) or len(rgb) != 3: return False
    if tolerance == 0: return rgb == WHITE_RGB
    # Clamp tolerance to max meaningful value
    effective_tolerance = min(tolerance, 765)
    diff = sum(abs(p - w) for p, w in zip(rgb, WHITE_RGB))
    return diff <= effective_tolerance

def has_transparent_neighbor(x, y, width, height, pixel_data):
    """Checks if pixel (x,y) has a neighbor with alpha < MIN_OPAQUE_ALPHA."""
    for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]: # Check N, S, E, W
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            idx = ny * width + nx
            if isinstance(pixel_data[idx], tuple) and len(pixel_data[idx]) >= 4:
                if pixel_data[idx][3] < MIN_OPAQUE_ALPHA: # Check if neighbor is transparent
                    return True
    return False # No fully transparent neighbors found

def multi_pass_remove(input_path, output_path, tolerance, passes):
    """
    Opens GIF, finds light-colored pixels adjacent to transparency,
    makes them fully transparent, and repeats for multiple passes.
    """
    try:
        original_gif = Image.open(input_path)
        print(f"Multi-pass ({passes}) edge removal for {os.path.basename(input_path)} (Tol: {tolerance})...")

        processed_frames = []
        durations = []
        disposals = []
        is_animated = getattr(original_gif, "is_animated", False)

        for i, frame_image in enumerate(ImageSequence.Iterator(original_gif)):
            duration = frame_image.info.get('duration', 100)
            durations.append(duration)
            disposal = frame_image.info.get('disposal', 2)
            disposals.append(disposal)

            # Work on RGBA copy
            if frame_image.mode != 'RGBA':
                frame_rgba = frame_image.convert("RGBA")
            else:
                frame_rgba = frame_image.copy()

            width, height = frame_rgba.size
            # Start with the current frame's pixel data for the first pass
            current_pixels = list(frame_rgba.getdata())
            total_pixels_changed_in_frame = 0

            # --- Multi-Pass Loop ---
            for p in range(passes):
                pixels_to_change_this_pass = []
                pixels_changed_this_pass = 0
                pass_pixel_data = list(current_pixels) # Use data from end of *previous* pass

                # Iterate through pixels for the current pass
                for y in range(height):
                    for x in range(width):
                        index = y * width + x
                        if not isinstance(pass_pixel_data[index], tuple) or len(pass_pixel_data[index]) < 4: continue

                        r, g, b, a = pass_pixel_data[index]

                        # Check conditions (same as remove_edge_border.py)
                        if a >= MIN_OPAQUE_ALPHA and \
                           is_close_to_white((r, g, b), tolerance) and \
                           has_transparent_neighbor(x, y, width, height, pass_pixel_data):

                            # Mark for transparency change *after* scanning this pass
                            pixels_to_change_this_pass.append(index)

                # Apply changes for this pass if any pixels were marked
                if pixels_to_change_this_pass:
                    pixels_changed_this_pass = len(pixels_to_change_this_pass)
                    total_pixels_changed_in_frame += pixels_changed_this_pass
                    print(f"  Frame {i}, Pass {p+1}: Clearing {pixels_changed_this_pass} border pixels.")
                    temp_pixel_list = list(pass_pixel_data) # Create mutable list from current pass data
                    for index in pixels_to_change_this_pass:
                        # Read original RGB just before setting alpha=0
                        r_orig, g_orig, b_orig, a_orig = temp_pixel_list[index]
                        temp_pixel_list[index] = (r_orig, g_orig, b_orig, 0) # Make transparent
                    # Update current_pixels for the *next* pass
                    current_pixels = tuple(temp_pixel_list) # Make it a tuple again for the next iteration check
                else:
                    print(f"  Frame {i}, Pass {p+1}: No border pixels detected/changed.")
                    # If no changes in a pass, no point doing more passes for this frame
                    break
            # --- End Multi-Pass Loop ---

            # Update the frame image data with the final result after all passes
            if total_pixels_changed_in_frame > 0:
                frame_rgba.putdata(current_pixels)

            processed_frames.append(frame_rgba)

        # --- Saving Logic ---
        if not processed_frames:
            print(f"Warning: No frames processed for {input_path}")
            original_gif.close()
            return False

        loop_count = original_gif.info.get('loop', 0) if is_animated else 0
        processed_frames[0].save(
            output_path, save_all=True, append_images=processed_frames[1:],
            duration=durations if is_animated else 0, loop=loop_count,
            disposal=disposals if is_animated else 2, optimize=False, transparency=0
        )
        print(f"Saved multi-pass cleaned GIF to {output_path}")
        return True

    except FileNotFoundError:
        print(f"Error: Input file not found: {input_path}")
        return False
    except Exception as e:
        print(f"Error processing {input_path}: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'original_gif' in locals() and hasattr(original_gif, 'close'):
            original_gif.close()

def main():
    # Declare globals before use in defaults
    global BORDER_TOLERANCE, NUM_PASSES

    parser = argparse.ArgumentParser(description="Remove light borders adjacent to transparency over multiple passes.")
    parser.add_argument("input_dir", help="Path to folder containing GIFs with borders.")
    parser.add_argument("output_dir", help="Path to folder where cleaned GIFs will be saved.")
    parser.add_argument("-t", "--tolerance", type=int, default=BORDER_TOLERANCE,
                        help=f"Color tolerance (closeness to white). Default: {BORDER_TOLERANCE}")
    parser.add_argument("-p", "--passes", type=int, default=NUM_PASSES,
                        help=f"Number of removal passes per frame. Default: {NUM_PASSES}")

    args = parser.parse_args()
    input_folder = args.input_dir
    output_folder = args.output_dir
    # Update global config from args
    BORDER_TOLERANCE = args.tolerance
    NUM_PASSES = args.passes

    if NUM_PASSES <= 0:
        print("Error: Number of passes must be positive.")
        sys.exit(1)

    if not os.path.isdir(input_folder):
        print(f"Error: Input directory not found: {input_folder}")
        sys.exit(1)

    os.makedirs(output_folder, exist_ok=True)
    print(f"Processing GIFs from: {input_folder}")
    print(f"Saving results to:  {output_folder}")
    print(f"Using border tolerance: {BORDER_TOLERANCE}, Passes: {NUM_PASSES}")

    processed_count = 0
    skipped_count = 0
    error_count = 0

    for filename in os.listdir(input_folder):
        if filename.lower().endswith(".gif"):
            input_path = os.path.join(input_folder, filename)
            output_path = os.path.join(output_folder, filename) # Keep original name

            if multi_pass_remove(input_path, output_path, BORDER_TOLERANCE, NUM_PASSES):
                processed_count += 1
            else:
                error_count += 1
        else:
            skipped_count += 1

    print("\n--- Multi-Pass Edge Removal Summary ---")
    print(f"Successfully processed: {processed_count}")
    print(f"Skipped (non-GIF):     {skipped_count}")
    print(f"Errors encountered:   {error_count}")
    print("-------------------------------------")

if __name__ == "__main__":
    main()