package migrations

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// CreateDefaultDreamboardsMigration implements the Migration interface for creating default dreamboards
type CreateDefaultDreamboardsMigration struct {
	db *mongo.Database
}

func NewCreateDefaultDreamboardsMigration(db *mongo.Database) *CreateDefaultDreamboardsMigration {
	return &CreateDefaultDreamboardsMigration{db: db}
}

func (m *CreateDefaultDreamboardsMigration) Name() string {
	return "create_default_dreamboards"
}

func (m *CreateDefaultDreamboardsMigration) Up(ctx context.Context) error {
	// Get users without dreamboards
	userCollection := m.db.Collection(repository.USERS_COLLECTION)
	dreamboardCollection := m.db.Collection(repository.DREAMBOARDS_COLLECTION)

	cursor, err := userCollection.Find(ctx, bson.D{})
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var user model.User
		if err := cursor.Decode(&user); err != nil {
			return err
		}

		// Check if user already has a dreamboard by looking up their ID in the dreamboard collection
		err = dreamboardCollection.FindOne(ctx, bson.D{{Key: "user", Value: user.ObjectID.Hex()}}).Err()
		if err == mongo.ErrNoDocuments {
			// Create default dreamboard for user
			objID := primitive.NewObjectID()
			dreamboard := dreamboard.Dreamboard{
				ObjectID:        objID,
				ID:              objID.Hex(),
				User:            user.ObjectID.Hex(),
				Dreams:          make([]*dreamboard.Dream, 0),
				TotalDreamsCost: 0,
				SavedAmount:     0,
				MonthlyNeeded:   0,
				RemainingAmount: 0,
				CreatedAt:       time.Now().UTC(),
				UpdatedAt:       time.Now().UTC(),
			}

			_, err := dreamboardCollection.InsertOne(ctx, dreamboard)
			if err != nil {
				if mongo.IsDuplicateKeyError(err) {
					return errors.New(errors.Migrator, "dreamboard already exists", errors.Conflict, err)
				}
				return err
			}
		}
	}

	return cursor.Err()
}
