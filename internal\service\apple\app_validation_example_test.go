package apple

import (
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"testing"

	"github.com/Timothylock/go-signin-with-apple/apple"
	"github.com/joho/godotenv"
)

/*
This example shows you how to validate an iOS app token for the first time
*/

func TestValidatingAppTokenAndObtainingID(t *testing.T) {
	if err := godotenv.Load("../../../.env"); err != nil {
		log.Println(err)
	}
	log.Println("Config Mode: Development")

	// Your Team ID
	teamID := os.Getenv("APPLE_LOGIN_TEAM_ID")

	// Your Service ID
	clientID := os.Getenv("APPLE_LOGIN_CLIENT_ID")

	// Your Key ID
	keyID := os.Getenv("APPLE_LOGIN_KEY_ID")

	enc_secret, _ := base64.StdEncoding.DecodeString(os.Getenv("APPLE_LOGIN_SECRET_KEY"))
	secret := string(enc_secret)

	fmt.Println(teamID)
	fmt.Println(keyID)
	fmt.Println(secret)
	fmt.Println(enc_secret)

	// Generate the client secret used to authenticate with Apple's validation servers
	secret, err := apple.GenerateClientSecret(secret, teamID, clientID, keyID)
	if err != nil {
		fmt.Println("error generating secret: " + err.Error())
		return
	}

	// Generate a new validation client
	client := apple.New()

	vReq := apple.AppValidationTokenRequest{
		ClientID:     clientID,
		ClientSecret: secret,
		Code:         "the_authorization_code_to_validate",
	}

	var resp apple.ValidationResponse

	// Do the verification
	err = client.VerifyAppToken(context.Background(), vReq, &resp)
	if err != nil {
		fmt.Println("error verifying: " + err.Error())
		return
	}

	if resp.Error != "" {
		fmt.Printf("apple returned an error: %s - %s\n", resp.Error, resp.ErrorDescription)
		return
	}

	// Get the unique user ID
	unique, err := apple.GetUniqueID(resp.IDToken)
	if err != nil {
		fmt.Println("failed to get unique ID: " + err.Error())
		return
	}

	// Get the email
	claim, err := apple.GetClaims(resp.IDToken)
	if err != nil {
		fmt.Println("failed to get claims: " + err.Error())
		return
	}

	email := (*claim)["email"]
	emailVerified := (*claim)["email_verified"]
	isPrivateEmail := (*claim)["is_private_email"]

	// Voila!
	fmt.Println(unique)
	fmt.Println(email)
	fmt.Println(emailVerified)
	fmt.Println(isPrivateEmail)
}
