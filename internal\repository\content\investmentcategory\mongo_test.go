package investmentcategory

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/config"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type MongoRepositoryTestSuite struct {
	suite.Suite
	client     *mongo.Client
	db         *mongo.Database
	repository Repository
	testIDs    []string
}

func (suite *MongoRepositoryTestSuite) SetupTest() {
	ctx := context.Background()

	// Initialize environment configuration
	err := config.EnvFromFile("../../../../.env")
	if err != nil {
		suite.T().Skipf("Failed to initialize environment configuration: %v - skipping integration tests", err)
		return
	}

	// Get database configuration
	dbURL := os.Getenv("DATABASE_URL")
	dbName := os.Getenv("DATABASE_NAME")

	if dbURL == "" || dbName == "" {
		suite.T().Skip("DATABASE_URL and DATABASE_NAME configuration not set - skipping integration tests")
		return
	}

	// Connect to MongoDB
	clientOptions := options.Client().ApplyURI(dbURL)
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		suite.T().Skipf("Failed to connect to MongoDB: %v - skipping integration tests", err)
		return
	}
	suite.client = client

	// Verify MongoDB connection
	err = suite.client.Ping(ctx, nil)
	if err != nil {
		suite.T().Skipf("Failed to ping MongoDB: %v - skipping integration tests", err)
		return
	}

	suite.db = suite.client.Database(dbName)
	suite.repository = New(suite.db)

	// Verify test collection exists
	collections, err := suite.db.ListCollectionNames(ctx, bson.M{})
	if err != nil {
		suite.T().Skipf("Failed to list collections: %v - skipping integration tests", err)
		return
	}

	var collectionExists bool
	for _, col := range collections {
		if col == repository.INVESTMENTS_CATEGORIES_COLLECTION {
			collectionExists = true
			break
		}
	}

	if !collectionExists {
		suite.T().Skipf("Collection %s does not exist - skipping integration tests", repository.INVESTMENTS_CATEGORIES_COLLECTION)
		return
	}
}

func (suite *MongoRepositoryTestSuite) TearDownTest() {
	ctx := context.Background()

	// Clean up test data
	if suite.db != nil && len(suite.testIDs) > 0 {
		for _, id := range suite.testIDs {
			objID, err := primitive.ObjectIDFromHex(id)
			if err == nil {
				suite.T().Logf("Deleting test investment category with ID: %s", id)
				res, err := suite.db.Collection(repository.INVESTMENTS_CATEGORIES_COLLECTION).DeleteOne(ctx, bson.M{"_id": objID})
				suite.NoError(err)
				suite.T().Logf("Delete result: %d document(s) deleted", res.DeletedCount)
			}
		}
	}

	// Disconnect client
	if suite.client != nil {
		err := suite.client.Disconnect(ctx)
		if err != nil {
			suite.T().Errorf("Error disconnecting client: %v", err)
		}
	}
}

func TestMongoRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(MongoRepositoryTestSuite))
}

func (suite *MongoRepositoryTestSuite) createTestInvestmentCategory(name, identifier, logo, color string) *content.InvestmentCategory {
	now := time.Now()
	ic := &content.InvestmentCategory{
		ObjectID:   primitive.NewObjectID(),
		Name:       name,
		Identifier: identifier,
		Logo:       logo,
		Color:      color,
		CreatedAt:  now,
		UpdatedAt:  now,
	}
	return ic
}

func (suite *MongoRepositoryTestSuite) TestIndexCreation() {
	ctx := context.Background()

	// Get indexes
	cursor, err := suite.db.Collection(repository.INVESTMENTS_CATEGORIES_COLLECTION).Indexes().List(ctx)
	suite.Require().NoError(err)
	defer cursor.Close(ctx)

	var indexes []bson.M
	err = cursor.All(ctx, &indexes)
	suite.Require().NoError(err)

	// Find our specific index
	var found bool
	for _, index := range indexes {
		if index["name"] == "identifier" {
			found = true
			// Verify index properties
			key := index["key"].(bson.M)
			suite.Equal(int32(1), key["identifier"]) // MongoDB returns int32 for index value
			suite.Equal(true, index["unique"])
			break
		}
	}
	suite.True(found, "Identifier index not found")
}

func (suite *MongoRepositoryTestSuite) TestCRUDOperations() {
	ctx := context.Background()

	// Create
	ic := suite.createTestInvestmentCategory(
		"Test Category",
		"test_category",
		"http://example.com/logo.png",
		"#FF0000",
	)

	err := ic.PrepareCreate()
	suite.Require().NoError(err)

	err = suite.repository.Create(ctx, ic)
	suite.Require().NoError(err)
	suite.testIDs = append(suite.testIDs, ic.ObjectID.Hex())

	// Find by ID
	found, err := suite.repository.Find(ctx, ic.ObjectID.Hex())
	suite.Require().NoError(err)
	suite.Equal(ic.Name, found.Name)
	suite.Equal(ic.Identifier, found.Identifier)
	suite.Equal(ic.Logo, found.Logo)
	suite.Equal(ic.Color, found.Color)

	// Find by Identifier
	found, err = suite.repository.FindByIdentifier(ctx, ic.Identifier)
	suite.Require().NoError(err)
	suite.Equal(ic.ObjectID, found.ObjectID)

	// Update
	ic.Name = "Updated Test Category"
	err = suite.repository.Update(ctx, ic)
	suite.Require().NoError(err)

	// Verify update
	updated, err := suite.repository.Find(ctx, ic.ObjectID.Hex())
	suite.Require().NoError(err)
	suite.Equal("Updated Test Category", updated.Name)

	// Delete
	err = suite.repository.Delete(ctx, ic.ObjectID.Hex())
	suite.Require().NoError(err)

	// Verify deletion
	_, err = suite.repository.Find(ctx, ic.ObjectID.Hex())
	suite.Error(err)
}

func (suite *MongoRepositoryTestSuite) TestUniqueIdentifierConstraint() {
	ctx := context.Background()

	// Create first category
	ic1 := suite.createTestInvestmentCategory(
		"Test Category 1",
		"test_category",
		"http://example.com/logo1.png",
		"#FF0000",
	)

	err := ic1.PrepareCreate()
	suite.Require().NoError(err)

	err = suite.repository.Create(ctx, ic1)
	suite.Require().NoError(err)
	suite.testIDs = append(suite.testIDs, ic1.ObjectID.Hex())

	// Try to create another category with same identifier
	ic2 := suite.createTestInvestmentCategory(
		"Test Category 2",
		"test_category", // Same identifier
		"http://example.com/logo2.png",
		"#00FF00",
	)

	err = ic2.PrepareCreate()
	suite.Require().NoError(err)

	err = suite.repository.Create(ctx, ic2)
	suite.Error(err) // Should fail due to unique constraint
}

func (suite *MongoRepositoryTestSuite) TestFindAll() {
	ctx := context.Background()

	// Initially should be empty (after cleanup)
	categories, err := suite.repository.FindAll(ctx)
	suite.Require().NoError(err)
	initialCount := len(categories)

	// Create multiple categories
	for i := 0; i < 3; i++ {
		ic := suite.createTestInvestmentCategory(
			"Test Category "+string(rune(i+'1')),
			"test_category_"+string(rune(i+'1')),
			"http://example.com/logo"+string(rune(i+'1'))+".png",
			"#FF000"+string(rune(i+'0')),
		)

		err := ic.PrepareCreate()
		suite.Require().NoError(err)

		err = suite.repository.Create(ctx, ic)
		suite.Require().NoError(err)
		suite.testIDs = append(suite.testIDs, ic.ObjectID.Hex())
	}

	// Find all
	categories, err = suite.repository.FindAll(ctx)
	suite.Require().NoError(err)
	suite.Len(categories, initialCount+3)
}

func (suite *MongoRepositoryTestSuite) TestErrorCases() {
	ctx := context.Background()

	// Test invalid ID format
	_, err := suite.repository.Find(ctx, "invalid-id")
	suite.Error(err)

	// Test not found
	_, err = suite.repository.Find(ctx, primitive.NewObjectID().Hex())
	suite.Error(err)

	// Test find by non-existent identifier
	_, err = suite.repository.FindByIdentifier(ctx, "non_existent")
	suite.Error(err)

	// Test update non-existent category
	ic := &content.InvestmentCategory{
		ObjectID: primitive.NewObjectID(),
	}
	ic = suite.createTestInvestmentCategory(
		"Test Category",
		"test_category_update",
		"http://example.com/logo.png",
		"#FF0000",
	)
	ic.ObjectID = primitive.NewObjectID() // Ensure non-existent ID
	err = suite.repository.Update(ctx, ic)
	suite.Error(err)

	// Test delete with empty ID
	err = suite.repository.Delete(ctx, "")
	suite.Error(err)
}
