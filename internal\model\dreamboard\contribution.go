package dreamboard

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ContributionStatus represents the status of a user's contribution to a shared dream
type ContributionStatus string

const (
	ContributionStatusActive              ContributionStatus = "ACTIVE"
	ContributionStatusCancelledByUser     ContributionStatus = "CANCELLED_BY_USER"
	ContributionStatusEndedDreamCancelled ContributionStatus = "ENDED_DREAM_CANCELLED"
)

// IsValid validates the contribution status value
func (cs ContributionStatus) IsValid() bool {
	switch cs {
	case ContributionStatusActive, ContributionStatusCancelledByUser, ContributionStatusEndedDreamCancelled:
		return true
	default:
		return false
	}
}

// Contribution represents a user's participation in a shared dream
type Contribution struct {
	ObjectID             primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID                   string             `json:"id,omitempty" bson:"-"`
	DreamID              string             `json:"dreamId" bson:"dreamId"`
	ContributorUserID    string             `json:"contributorUserId" bson:"contributorUserId"`
	IsCreator            bool               `json:"isCreator" bson:"isCreator"`
	MonthlyPledgedAmount monetary.Amount    `json:"monthlyPledgedAmount" bson:"monthlyPledgedAmount"`
	Status               ContributionStatus `json:"status" bson:"status"`
	JoinedAt             time.Time          `json:"joinedAt" bson:"joinedAt"`
	UpdatedAt            time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// Validate validates the Contribution
func (c *Contribution) Validate() error {
	if c.DreamID == "" {
		return errors.New(errors.Model, "dream ID must be specified", errors.Validation, nil)
	}
	if c.ContributorUserID == "" {
		return errors.New(errors.Model, "contributor user ID must be specified", errors.Validation, nil)
	}
	if c.MonthlyPledgedAmount < 0 {
		return errors.New(errors.Model, "monthly pledged amount cannot be negative", errors.Validation, nil)
	}
	if !c.Status.IsValid() {
		return errors.New(errors.Model, "invalid contribution status", errors.Validation, nil)
	}
	return nil
}
