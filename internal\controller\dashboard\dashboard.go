package dashboard

import (
	"math"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/labstack/echo/v4"
)

func (dc *controller) FindDashboard() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		period := "30d" // Default to 30 days
		financialStress, err := dc.Service.FindFinancialStress(ctx, userToken.Uid, period)
		if err != nil {
			return err
		}

		financialIndependence, err := dc.Service.FindFinancialIndependence(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Build the response
		var stressScore float64
		if financialStress.StressScore != nil {
			stressScore = math.Round(financialStress.StressScore.Score*100) / 100 // rounds to 2 decimal places
		} else {
			stressScore = 0.0 // Default value for new users with no income
		}

		response := &DashboardDTO{
			CurrentNetWorth:        financialIndependence.CurrentNetWorth,
			StressScore:            stressScore,
			RetirementTargetAmount: financialIndependence.RetirementTargetAmount,
		}

		return c.JSON(http.StatusOK, response)
	}
}
