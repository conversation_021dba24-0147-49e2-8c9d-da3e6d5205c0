package trail

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
)

type AdminController interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// All Trails CRUD - Regular and Extra
	Find() echo.HandlerFunc
	FindAll() echo.HandlerFunc
	FindByIdentifier() echo.HandlerFunc

	FindCardData() echo.HandlerFunc
	FindAllCardData() echo.HandlerFunc

	// TODO Move all admin functions from the controller to the admin controller
}

type adminController struct {
	Service     trail.Service
	UserService user.Service
}

func NewAdmin(service trail.Service, userService user.Service) AdminController {
	return &adminController{
		Service:     service,
		UserService: userService,
	}
}

// Routes
func (tc *adminController) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	// V2 API
	trailsGroup := currentGroup.Group("/contents/trails", middlewares.AuthGuard(), middlewares.AdminGuard())

	// CRUD operations
	trailsGroup.GET("/:id", tc.Find())
	trailsGroup.GET("", tc.FindAll())
	trailsGroup.GET("/identifier/:identifier", tc.FindByIdentifier())

	trailsGroup.GET("/cards/:id", tc.FindCardData())
	trailsGroup.GET("/cards", tc.FindAllCardData())
}

// CRUD
func (tc *adminController) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		trail, err := tc.Service.Find(ctx, c.Param("id"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *adminController) FindAll() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		result, err := tc.Service.FindAll(ctx)
		if err != nil {
			return err
		}

		if result == nil {
			return c.JSON(http.StatusNoContent, nil)
		}

		for _, trail := range result {
			trail.Sanitize()
		}

		return c.JSON(http.StatusOK, result)
	}
}

func (tc *adminController) FindByIdentifier() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		trail, err := tc.Service.FindByIdentifier(ctx, c.Param("identifier"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *adminController) FindCardData() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		cardData, err := tc.Service.FindCardData(ctx, c.Param("id"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, cardData)
	}
}

func (tc *adminController) FindAllCardData() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		result, err := tc.Service.FindAllCardData(ctx)
		if err != nil {
			return err
		}

		if result == nil {
			return c.JSON(http.StatusNoContent, nil)
		}

		return c.JSON(http.StatusOK, result)
	}
}
