# Active Context

## Current Focus Areas

### 1. Financial DNA System ✅
Core functionality implemented. Minor task remaining:
- Add icons to family members.
- Family member management ✅
- Relationship tracking ✅
- Financial status analysis ✅
- Generation-based queries ✅

Key files:
- `internal/model/financialdna/family_member.go`
- `internal/model/financialdna/financial_status.go`
- `internal/model/financialdna/metrics.go`
- `internal/service/financialdna/service.go`
- `internal/repository/financialdna/repository.go`
- `internal/controller/financialdna/controller.go`

### 2. Financial Sheet System
Core functionality complete, may need future refinements:
- Money source management ✅
- Category organization system ✅
  - Predefined categories ✅
  - Custom category support ✅
- Record tracking implementation ✅
- Repository and service layer implementation ✅

Key files:
- `internal/model/financialsheet/money_source.go`
- `internal/model/financialsheet/category_predefined.go`
- `internal/model/financialsheet/record.go`
- `internal/model/financialsheet/category.go`
- `internal/service/financialsheet/service.go`
- `internal/repository/financialsheet/repository.go`
- `internal/controller/financialsheet/controller.go`

### 3. League System Controller Refinements ✅
- League System User Info Handling:
    - `internal/model/league/league.go`: Renamed `LeagueMember.AvatarURL` to `PhotoURL` (JSON tag `photo`). ✅
    - `internal/service/league/service.go`:
        - `CreateLeague` signature now `(ctx, ownerUserID, ownerUserName, ownerPhotoURL, leagueName, imageURL)`. ✅
        - `JoinLeague` signature now `(ctx, userID, userName, photoURL, inviteCode)`. ✅
        - Removed `UserService` dependency from `league.Service`. ✅
    - `internal/service/league/league.go` (`CreateLeague`): Updated to use `ownerUserName`, `ownerPhotoURL`. ✅
    - `internal/service/league/membership.go` (`JoinLeague`): Updated to use `userName`, `photoURL`. ✅
    - `internal/controller/league/controller.go`:
        - Added `UserService` dependency. ✅
        - `CreateLeagueRequest` DTO: Removed `UserName`. ✅
        - `JoinLeagueRequest` DTO: Removed `UserName`, `AvatarURL`. ✅
        - `CreateLeague` handler: Fetches user details (`Name`, `PhotoURL`) via `UserService.Find` and passes to `LeagueService.CreateLeague`. ✅
        - `JoinLeague` handler: Fetches user details (`Name`, `PhotoURL`) via `UserService.Find` and passes to `LeagueService.JoinLeague`. ✅
- Removed `Description` field from League model, DTOs, service, and controller logic. ✅
- Refactored `internal/service/league/service_impl.go` by splitting methods into `league.go`, `membership.go`, and `gameplay.go`. ✅
- Merged `service_impl.go` content (struct definition and `New` function) into `service.go` and deleted `service_impl.go`. ✅
- Renamed "Investidas" and related terms to English equivalents (e.g., "TransactionStreak") in League model and service files. ✅
- Confirmed League `ImageURL` is handled as optional in model, DTOs, and service logic. ✅

Key files:
- `internal/model/league/league.go`
- `internal/controller/league/controller.go`
- `internal/service/league/service.go`
- `internal/service/league/league.go` (League CRUD, Invites, Seasons)
- `internal/service/league/membership.go` (Membership CRUD)
- `internal/service/league/gameplay.go` (Gameplay logic)
- `internal/api/token/jwt.go` (referenced for claims structure)

### 4. Dreamboard Features
Core functionality complete, may need future improvements:
- Core dreamboard functionality ✅
- Service layer implementation ✅
- Controller endpoints ✅
- Fixed PATCH request issue with financial fields ✅

Key files:
- `internal/model/dreamboard.go`
- `internal/service/dreamboard/service.go`
- `internal/controller/dreamboard/controller.go`

Recent fix:
- Fixed bug where `estimatedCost` and `monthlySavings` were being reset to zero during PATCH operations
- Modified `mergeDreams` function to only update financial fields when explicitly provided (> 0)
- Changed condition from `>= 0` to `> 0` to prevent zero values from overwriting existing data

### 5. Content Trail System Modularization ✅
Complete modularization of the content trail service layer:
- **service.go**: Contains interface definition, service struct, and "all trails" functionality (Find, FindAll, FindByIdentifier, FindCardData, FindAllCardData) with corresponding cache helpers and invalidation functions. ✅
- **trail.go**: Contains regular trail CRUD operations, regular trail lesson management, with dedicated cache helpers and invalidation functions for regular trails. ✅
- **extra_trail.go**: Contains extra trail CRUD operations, extra trail lesson management (with access control limitations), with dedicated cache helpers and invalidation functions for extra trails. ✅
- **tutorial.go**: Contains tutorial CRUD operations, tutorial lesson management, with dedicated cache helpers and invalidation functions for tutorials. ✅

Key improvements:
- Proper separation of concerns across different trail types
- Dedicated cache management for each trail type
- Consistent error handling and logging patterns
- Maintained interface compatibility while improving internal organization

Key files:
- `internal/service/content/trail/service.go` (Main interface and all-trails operations)
- `internal/service/content/trail/trail.go` (Regular trails)
- `internal/service/content/trail/extra_trail.go` (Extra trails)
- `internal/service/content/trail/tutorial.go` (Tutorials)

### 6. Code Organization
Current refactoring initiatives:
- File naming conventions review
- Prompt template organization
- Documentation updates
- Updated `docs/feature/leaguesystem/leaguesystem.md` with translations.
- Adjusted League system: Removed `Description` field from `League` model and related DTOs/services. ✅
- Refactored League service implementation into multiple files (`league.go`, `membership.go`, `gameplay.go`). ✅
- Consolidated League service struct and `New` function into `service.go`, removing `service_impl.go`. ✅
- Standardized terminology in League system from Portuguese "Investidas" to English "TransactionStreak". ✅
    - Verified optional handling of League `ImageURL`. ✅
    - Modified League creation/joining to fetch user's name/photoURL via `UserService` in the controller, instead of client-provided. ✅
    - Added `StartDate` and `EndDate` to League creation process, impacting DTO, controller, service interface, and implementation. ✅
- Completed modularization of Content Trail System service layer. ✅

### 7. User Authentication (V2 Register) ✅
- Completed V2 Register endpoint (`/auth/register`).
- Handles multipart form data including user details and onboarding information.
- Supports optional photo upload with validation (type: JPG/JPEG/PNG, size: max 5MB).
- Photo uploaded to S3 via `s3.Service`.
- `s3.Service` refactored to load AWS config (bucket, region, endpoint, credentials) from environment variables.
- Processes referral codes.
- Returns JWT token upon successful registration.

Key files:
- `internal/controller/auth/controller.go` (Register function)
- `internal/service/auth/service.go` (Register function, S3 integration)
- `internal/service/s3/service.go` (New function for env var config)
- `internal/service/s3/s3.go` (UploadFile, DeleteFile, getAWSConfig)
- `internal/api/service/service.go` (S3 and Auth service initialization)

### 8. Billing System Subscription Cancellation ✅
- **Complete simplification**: Single `CancelSubscription` method handles all cancellation scenarios.
- **Single source of truth**: Trial period determines access revocation behavior:
  - **Within trial period**: Immediate access revocation 
  - **After trial period**: Access maintained until subscription end date
- **Eliminated code duplication**: Removed redundant `RefundSubscription` and `CancelSubscriptionAfterRefundPeriod` methods.
- **Simplified API**: Only one endpoint (`POST /:id/cancel`) needed for all cancellation types.
- **Clean controller logic**: Single handler method with appropriate response messages based on trial status.
- **Enhanced cancellation tracking**: Clear descriptive reason messages distinguish trial vs. post-trial cancellations.

Key changes:
- Removed `RefundSubscription` and `CancelSubscriptionAfterRefundPeriod` from service interface and implementation
- Removed `/refund` endpoint from controller routes 
- Simplified `ShouldRevokeAccessImmediately` method in subscription model
- Single `CancelSubscription` method handles all logic internally

Key files:
- `internal/service/billing/subscription.go` (Simplified CancelSubscription method)
- `internal/service/billing/service.go` (Updated service interface)
- `internal/model/billing/subscription.go` (Simplified access control methods)
- `internal/controller/billing/controller.go` (Single CancelSubscription handler)

Active prompt templates:
- `internal/prompts/refactor/file_naming/.prompts`
- `internal/prompts/dreamboard/.prompts`
- `internal/prompts/financialsheet/.prompts`
- `internal/prompts/auth/.prompts`
- `internal/prompts/guidelines/.prompts`

## Recent Changes

### OAuth Integration Enhancements ✅

#### Google OAuth Registration Enhancement ✅
1. **Adapted Google Registration to Follow Auth Pattern**
   - Modified `CallBackFromGoogleRegister` function in Google controller
   - Added support for onboarding data as form fields (following auth controller pattern)
   - Added photo upload support with validation (file type, size limits)
   - Implemented `RegisterWithPhoto` method in Google service
   - Enhanced form data parsing for onboarding fields (ageRange, financialSituation, financialGoal, personalInterests)
   - Added `StringToByte` helper function for form data conversion
   - Maintains backward compatibility with existing Google OAuth flows

2. **Key Enhancements Made**
   - Form data parsing for complex onboarding structure
   - Photo validation (JPG/JPEG/PNG, max 5MB)
   - Proper error handling and validation messages
   - Integration with existing Google user details flow
   - Support for referral codes in registration
   - Returns HTTP 201 status for successful registration (following REST conventions)

#### Apple OAuth Enhancement ✅
1. **Implemented Apple Package Changes Following Google Pattern**
   - Updated Apple controller to use `currentGroup` v2 routing (trailing slash modification)
   - Enhanced `HandleAppleRegister` to support form data and photo uploads (following Google pattern)
   - Updated `HandleAppleLogin` to return proper LoginResponseDTO structure
   - Added S3 service integration for photo upload support
   - Implemented `StringToByte` helper function for form data conversion

2. **Service Layer Updates**
   - Updated Apple service interface to support photo uploads (`Register` method signature)
   - Modified Login method to return both user and token (matching Google pattern)
   - Added S3Service dependency to Apple service
   - Enhanced Register method to handle photo uploads via S3
   - Maintained existing Apple ID token validation logic

3. **Key Changes Made**
   - Routes now use `currentGroup.Group("/auth")` instead of `legacyGroup.Group("auth/")`
   - Register endpoint supports multipart form data with photo validation
   - Login endpoint returns structured LoginResponseDTO with user details
   - Both endpoints follow REST conventions (201 for registration, 200 for login)
   - Added DTO file for response structures (LoginResponseDTO, RefreshResponseDTO)
   - Updated API controller initialization to pass S3 service to Apple controller

### Content Trail System Modularization ✅
1. Service Layer Reorganization Complete
   - Split monolithic service file into focused modules
   - Each trail type (regular, extra, tutorial) has dedicated file
   - Proper cache key management per module
   - Maintained interface compatibility
   - Improved code maintainability and readability

### Financial DNA Module ✅
1. Core Implementation Complete
   - Family member model with relationships
   - Financial status tracking
   - Analytics and metrics calculation
   - Integration with registries

### Financial Sheet Module
1. Category System Implementation
   - Predefined categories structure
   - Category management
   - Money source tracking
   - Record keeping system

### Dreamboard Module
1. Core Functionality
   - Model definition
   - Service layer implementation
   - Controller endpoints
   - Integration with other modules

### System Architecture & Infrastructure
1. Code Organization
   - File naming standardization
   - Documentation improvements
   - Prompt template structure
   - Completed Content Trail System modularization ✅
2. Performance & Reliability
   - Added cache memory check for Content Trail system.
   - Improved MongoDB collection structures/indexing.
   - Optimized cache management with modular approach ✅
3. User Authentication
   - V2 Register endpoint implemented with photo upload and referral handling. ✅
   - S3 service configuration refactored to use environment variables. ✅
   - **Google OAuth registration enhanced with onboarding and photo support** ✅
4. Testing
   - Added tests for Progression system.
5. Deployment
   - Made improvements to CI/CD pipeline.

## Active Decisions

### 1. Financial DNA System (Completed)
- Family relationship modeling finalized.
- Analytics calculation approach implemented.
- Tree visualization strategy defined (implementation pending if separate).
- Investment probability metrics implemented.

### 2. Financial Management
- Category hierarchy structure
- Money source tracking approach
- Record keeping methodology
- Data validation rules

### 3. Dreamboard System
- Goal tracking implementation
- Progress visualization
- Integration with financial data
- User experience flow

### 4. Architecture
- File organization standards
- Documentation requirements
- Code structure patterns
- Development guidelines
- Service layer modularization patterns (completed for Content Trail System) ✅

## Next Steps

### Immediate Tasks
1. Financial DNA Enhancements
   - Implement icon addition for family members.
   - Further testing and refinement based on feedback.
2. System Improvements & Maintenance
   - Continue standardizing file naming.
   - Update documentation across modules (including `docs/feature/leaguesystem/leaguesystem.md`).
   - Review and potentially optimize cache strategy.
   - Monitor performance post-MongoDB improvements.
   - Verify impact of modularized Content Trail System on related services/tests. ✅
3. Feature Refinements (Financial Sheet, Dreamboard)
   - Address any outstanding low-priority enhancements.
   - Incorporate user feedback.

### Upcoming Work
1. Testing
   - Increase unit/integration test coverage, especially for recent changes (including modularized Content Trail System). ✅
   - Performance validation under load.
   - Security testing review.

2. Documentation
   - API documentation
   - Code comments
   - Development guides
   - User documentation

3. Optimization
   - Performance improvements
   - Code refactoring (Content Trail System completed) ✅
   - Database optimization
   - Cache implementation optimization

## Current Considerations

### Technical
1. Code Structure
   - Maintain clean architecture
   - Ensure proper separation of concerns ✅
   - Follow established patterns ✅
   - Support scalability

2. Performance
   - Query optimization
   - Cache utilization (improved with modular approach) ✅
   - Response time
   - Resource usage

### Product
1. User Experience
   - Feature completeness
   - Interface consistency
   - Error handling
   - Feedback mechanisms

2. Integration
   - Module interconnection
   - Data flow
   - State management
   - Event handling
