# Project Brief: <PERSON><PERSON>a Backend

## Overview
Dinbora Backend is a Go-based REST API service that provides the backend infrastructure for a financial education and management application. The system is built with a focus on scalability, maintainability, and security.

## Core Features

1. Content Management
   - Lesson content storage and delivery
   - Trail-based learning paths
   - Tutorial content organization
   - Content migration capabilities
   - Achievement system

2. User Progression System
   - Track user progress through trails
   - Lesson completion tracking
   - Achievement unlocking
   - Tutorial progress management
   - Learning path progression

3. Financial Sheet Management
   - Track financial records
   - Manage money sources
   - Handle category-based organization
   - Predefined categories support
   - Investment tracking

4. Dreamboard System
   - Goal tracking and visualization
   - User progress monitoring
   - Vision board functionality

5. User Management
   - Authentication and authorization
   - Profile management
   - Onboarding flows
   - User vault for secure data

6. Billing & Subscriptions
   - Plan management
   - Subscription management
   - Payment processing
   - Webhook handling

7. Financial DNA System
   - Family relationship tracking
   - Financial status analysis
   - Multi-generational insights
   - Investment probability analytics

8. External Integrations
   - Apple authentication
   - Google authentication
   - Facebook authentication
   - Notification services

## Technical Requirements
- REST API architecture
- MongoDB for data persistence
- Secure authentication and authorization
- Environment-based configuration
- Robust error handling
- Data migration support
- Content versioning and management

## Project Goals
1. Deliver engaging financial education through structured content
2. Track and encourage user progress through learning paths
3. Provide a secure and scalable backend for financial management
4. Support user engagement through dreamboard features
5. Enable efficient financial data organization and tracking
6. Ensure robust integration with third-party services
7. Maintain high performance and reliability
8. Track and analyze financial relationships across generations

## Constraints
- Must follow REST API best practices
- Sensitive data must be properly secured
- Must maintain backward compatibility
- Must handle errors gracefully with standardized responses
- Content must be version-controlled and easily updatable

## Success Criteria
1. All API endpoints function as specified
2. Content delivery system works efficiently
3. User progression tracking is accurate and reliable
4. Authentication system works securely
5. Financial data is accurately tracked and managed
6. Third-party integrations work reliably
7. System maintains high performance under load
