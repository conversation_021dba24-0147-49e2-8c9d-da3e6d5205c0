package dashboard

import (
	"context"
	"sort"
	"strconv"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// FindFinancialStress calculates and returns comprehensive financial stress analysis
func (s *service) FindFinancialStress(ctx context.Context, userID string, period string) (*dashboard.FinancialStress, error) {
	// Parse period and calculate date ranges
	currentStart, currentEnd, previousStart, previousEnd, err := s.parsePeriodDates(period)
	if err != nil {
		return nil, err
	}

	// Get current period transactions
	currentTransactions, err := s.getTransactionsInDateRange(ctx, userID, currentStart, currentEnd)
	if err != nil {
		return nil, err
	}

	// Get previous period transactions for comparison
	previousTransactions, err := s.getTransactionsInDateRange(ctx, userID, previousStart, previousEnd)
	if err != nil {
		return nil, err
	}

	// Calculate monthly income from dashboard
	financialMap, err := s.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	monthlyIncome := financialMap.MonthlyIncome

	// Analyze current period expenses
	expenseAnalysis := s.analyzeExpenses(currentTransactions, monthlyIncome)

	// Calculate stress score
	stressScore := s.calculateStressScore(expenseAnalysis, monthlyIncome)

	// Calculate commitment analysis
	commitmentAnalysis := s.calculateCommitmentAnalysis(expenseAnalysis, monthlyIncome)

	// Analyze payment methods
	paymentMethods := s.analyzePaymentMethods(currentTransactions)

	// Calculate spending variation
	spendingVariation := s.calculateSpendingVariation(currentTransactions, previousTransactions)

	// If monthly income is 0, return nil to StressScore, CommitmentAnalysis, and ExpenseAnalysis
	if monthlyIncome == 0 {
		return &dashboard.FinancialStress{
			StressScore:        nil,
			CommitmentAnalysis: nil,
			ExpenseAnalysis:    nil,
			PaymentMethods:     paymentMethods,
			SpendingVariation:  spendingVariation,
			Period:             period,
			AnalysisDate:       time.Now(),
		}, nil
	}

	return &dashboard.FinancialStress{
		StressScore:        stressScore,
		CommitmentAnalysis: commitmentAnalysis,
		ExpenseAnalysis:    expenseAnalysis,
		PaymentMethods:     paymentMethods,
		SpendingVariation:  spendingVariation,
		Period:             period,
		AnalysisDate:       time.Now(),
	}, nil
}

// FindFixedExpenses returns detailed fixed expenses analysis
func (s *service) FindFixedExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error) {
	currentStart, currentEnd, _, _, err := s.parsePeriodDates(period)
	if err != nil {
		return nil, err
	}

	transactions, err := s.getTransactionsInDateRange(ctx, userID, currentStart, currentEnd)
	if err != nil {
		return nil, err
	}

	// Get monthly income for percentage calculation
	financialMap, err := s.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	fixedExpenses := s.categorizeExpenses(transactions, financialMap.MonthlyIncome).FixedExpenses

	// Set color on each money source breakdown category
	for i, category := range fixedExpenses.MoneySourceBreakdown {
		category.Color = s.getCategoryColor(i)
	}

	return &fixedExpenses, nil
}

// FindVariableExpenses returns detailed variable expenses analysis
func (s *service) FindVariableExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error) {
	currentStart, currentEnd, _, _, err := s.parsePeriodDates(period)
	if err != nil {
		return nil, err
	}

	transactions, err := s.getTransactionsInDateRange(ctx, userID, currentStart, currentEnd)
	if err != nil {
		return nil, err
	}

	// Get monthly income for percentage calculation
	financialMap, err := s.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	variableExpenses := s.categorizeExpenses(transactions, financialMap.MonthlyIncome).VariableExpenses

	// Set color on each money source breakdown category
	for i, category := range variableExpenses.MoneySourceBreakdown {
		category.Color = s.getCategoryColor(i)
	}

	return &variableExpenses, nil
}

// FindDebtExpenses returns detailed debt expenses analysis
func (s *service) FindDebtExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error) {
	currentStart, currentEnd, _, _, err := s.parsePeriodDates(period)
	if err != nil {
		return nil, err
	}

	transactions, err := s.getTransactionsInDateRange(ctx, userID, currentStart, currentEnd)
	if err != nil {
		return nil, err
	}

	// Get monthly income for percentage calculation
	financialMap, err := s.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	debtPayments := s.categorizeExpenses(transactions, financialMap.MonthlyIncome).Debts

	// Set color on each money source breakdown category
	for i, category := range debtPayments.MoneySourceBreakdown {
		category.Color = s.getCategoryColor(i)
	}

	return &debtPayments, nil
}

// FindExpenseAnalysisDetails returns detailed analysis for a specific category with monthly evolution
func (s *service) FindExpenseAnalysisDetails(ctx context.Context, userID string, categoryID string, period string) (*dashboard.CategoryBreakdown, error) {
	// Get monthly income for percentage calculation
	financialMap, err := s.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Calculate monthly evolution for the last 3 months
	monthlyEvolution, monthlyVariation, err := s.calculateMonthlyEvolution(ctx, userID, categoryID)
	if err != nil {
		return nil, err
	}

	// Get transactions for the specified period for overall breakdown
	currentStart, currentEnd, _, _, err := s.parsePeriodDates(period)
	if err != nil {
		return nil, err
	}

	transactions, err := s.getTransactionsInDateRange(ctx, userID, currentStart, currentEnd)
	if err != nil {
		return nil, err
	}

	// Find transactions for the specific category
	var categoryTransactions []*financialsheet.Transaction
	for _, transaction := range transactions {
		if transaction.Category.String() == categoryID {
			categoryTransactions = append(categoryTransactions, transaction)
		}
	}

	if len(categoryTransactions) == 0 {
		return nil, errors.New(errors.Service, "no transactions found for category", errors.NotFound, nil)
	}

	// Calculate basic category breakdown
	categoryBreakdown := s.calculateCategoryBreakdown(categoryTransactions, financialMap.MonthlyIncome)

	// Add monthly evolution and variation data
	categoryBreakdown.MonthlyEvolution = monthlyEvolution
	categoryBreakdown.MonthlyVariation = monthlyVariation

	return &categoryBreakdown, nil
}

// Helper function to calculate monthly evolution for the last 3 months
func (s *service) calculateMonthlyEvolution(ctx context.Context, userID string, categoryID string) ([]*dashboard.MonthlyData, *dashboard.MonthlyVariation, error) {
	now := time.Now()
	var monthlyData []*dashboard.MonthlyData
	var monthlyAmounts []monetary.Amount

	// Get data for the last 3 months
	for i := 2; i >= 0; i-- {
		monthDate := now.AddDate(0, -i, 0)
		year := monthDate.Year()
		month := int(monthDate.Month())

		// Get transactions for this specific month
		expenseTransactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, financialsheet.CategoryTypeExpense, year, month, false)
		if err != nil {
			return nil, nil, err
		}

		costTransactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, financialsheet.CategoryTypeCostsOfLiving, year, month, false)
		if err != nil {
			return nil, nil, err
		}

		// Calculate total for this category in this month
		var monthTotal monetary.Amount
		for _, transaction := range append(expenseTransactions, costTransactions...) {
			if transaction.Category.String() == categoryID {
				monthTotal += transaction.Value
			}
		}

		// Add to monthly data
		monthlyData = append(monthlyData, &dashboard.MonthlyData{
			Label: s.GetPortugueseMonthName(monthDate.Month()),
			Value: monthTotal,
		})
		monthlyAmounts = append(monthlyAmounts, monthTotal)
	}

	// Calculate month-over-month variation (current month vs previous month)
	var monthlyVariation *dashboard.MonthlyVariation
	if len(monthlyAmounts) >= 2 {
		currentMonth := monthlyAmounts[len(monthlyAmounts)-1]  // Last month (most recent)
		previousMonth := monthlyAmounts[len(monthlyAmounts)-2] // Second to last month

		variation := currentMonth - previousMonth
		var percentage float64
		var direction string

		if previousMonth > 0 {
			percentage = float64(variation) / float64(previousMonth) * 100
		}

		switch {
		case variation > 0:
			direction = "increase"
		case variation < 0:
			direction = "decrease"
			variation = -variation   // Make positive for display
			percentage = -percentage // Keep negative for calculation
		default:
			direction = "stable"
		}

		monthlyVariation = &dashboard.MonthlyVariation{
			Percentage: percentage,
			Direction:  direction,
			Amount:     variation,
		}
	}

	return monthlyData, monthlyVariation, nil
}

// Helper function to parse period string and return date ranges
func (s *service) parsePeriodDates(period string) (currentStart, currentEnd, previousStart, previousEnd time.Time, err error) {
	now := time.Now()

	switch period {
	case "7d":
		currentEnd = now
		currentStart = now.AddDate(0, 0, -7)
		previousEnd = currentStart
		previousStart = currentStart.AddDate(0, 0, -7)
	case "30d":
		currentEnd = now
		currentStart = now.AddDate(0, 0, -30)
		previousEnd = currentStart
		previousStart = currentStart.AddDate(0, 0, -30)
	case "90d":
		currentEnd = now
		currentStart = now.AddDate(0, 0, -90)
		previousEnd = currentStart
		previousStart = currentStart.AddDate(0, 0, -90)
	case "1y":
		currentEnd = now
		currentStart = now.AddDate(-1, 0, 0)
		previousEnd = currentStart
		previousStart = currentStart.AddDate(-1, 0, 0)
	default:
		err = errors.New(errors.Service, "invalid period format", errors.BadRequest, nil)
	}

	return
}

// Helper function to get transactions within a date range
func (s *service) getTransactionsInDateRange(ctx context.Context, userID string, startDate, endDate time.Time) ([]*financialsheet.Transaction, error) {
	var allTransactions []*financialsheet.Transaction

	// Get transactions for each month in the date range
	// Start from the beginning of the start month and go through the end month
	current := time.Date(startDate.Year(), startDate.Month(), 1, 0, 0, 0, 0, startDate.Location())
	endMonth := time.Date(endDate.Year(), endDate.Month(), 1, 0, 0, 0, 0, endDate.Location())

	for current.Before(endMonth) || current.Equal(endMonth) {
		year := current.Year()
		month := int(current.Month())

		// Get expense transactions
		expenseTransactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, financialsheet.CategoryTypeExpense, year, month, false)
		if err != nil {
			return nil, err
		}

		// Get cost of living transactions
		costTransactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, financialsheet.CategoryTypeCostsOfLiving, year, month, false)
		if err != nil {
			return nil, err
		}

		// Combine expense and cost of living transactions
		monthTransactions := append(expenseTransactions, costTransactions...)

		var filteredCount int
		// Filter transactions by date range
		for _, transaction := range monthTransactions {
			// Include transactions from startDate (inclusive) to endDate (inclusive)
			if (transaction.Date.After(startDate) || transaction.Date.Equal(startDate)) &&
				(transaction.Date.Before(endDate) || transaction.Date.Equal(endDate)) {
				allTransactions = append(allTransactions, transaction)
				filteredCount++
			}
		}

		// Move to next month (first day of next month)
		current = current.AddDate(0, 1, 0)
	}

	// Remove dreams from transactions
	var filteredTransactions []*financialsheet.Transaction
	for _, transaction := range allTransactions {
		if transaction.Category != financialsheet.CategoryIdentifierDreams {
			filteredTransactions = append(filteredTransactions, transaction)
		}
	}
	allTransactions = filteredTransactions

	return allTransactions, nil
}

// Helper function to analyze expenses and categorize them
func (s *service) analyzeExpenses(transactions []*financialsheet.Transaction, monthlyIncome monetary.Amount) *dashboard.ExpenseAnalysis {
	return s.categorizeExpenses(transactions, monthlyIncome)
}

// Helper function to categorize expenses into fixed, variable, and debt
func (s *service) categorizeExpenses(transactions []*financialsheet.Transaction, monthlyIncome monetary.Amount) *dashboard.ExpenseAnalysis {
	var fixedTotal, variableTotal, debtTotal monetary.Amount
	var fixedCount, variableCount, debtCount int

	categoryMap := make(map[string]*dashboard.CategoryBreakdown)
	// Map to track money source breakdown within each category
	categoryMoneySourceMap := make(map[string]map[financialsheet.MoneySource]*dashboard.MoneySourceDetail)

	// Maps to track money source breakdown for expense types
	fixedMoneySourceMap := make(map[financialsheet.MoneySource]*dashboard.MoneySourceDetail)
	variableMoneySourceMap := make(map[financialsheet.MoneySource]*dashboard.MoneySourceDetail)
	debtMoneySourceMap := make(map[financialsheet.MoneySource]*dashboard.MoneySourceDetail)

	for _, transaction := range transactions {
		categoryID := transaction.Category.String()

		// Initialize category if not exists
		if _, exists := categoryMap[categoryID]; !exists {
			// Create a category instance to get money source info
			category := financialsheet.Category{
				Identifier: transaction.Category,
				Type:       transaction.Type,
			}
			categoryInfo := category.GetCategoryInfo()

			categoryMap[categoryID] = &dashboard.CategoryBreakdown{
				CategoryIdentifier:   categoryID,
				CategoryName:         categoryInfo.Name,
				Icon:                 string(categoryInfo.Icon),
				Amount:               0,
				Count:                0,
				IsFixed:              s.isFixedExpense(transaction.Category),
				IsDebt:               s.isDebtExpense(transaction.Category),
				MoneySourceBreakdown: []*dashboard.MoneySourceDetail{},
			}
			categoryMoneySourceMap[categoryID] = make(map[financialsheet.MoneySource]*dashboard.MoneySourceDetail)
		}

		// Initialize money source within category if not exists
		if _, exists := categoryMoneySourceMap[categoryID][transaction.MoneySource]; !exists {
			// Create a category instance to get money source info
			category := financialsheet.Category{
				Identifier: transaction.Category,
				Type:       transaction.Type,
			}
			moneySourceInfo := category.GetMoneySourceInfo(transaction.MoneySource)

			categoryMoneySourceMap[categoryID][transaction.MoneySource] = &dashboard.MoneySourceDetail{
				MoneySourceIdentifier: moneySourceInfo.Identifier,
				MoneySourceName:       moneySourceInfo.Name,
				Icon:                  string(moneySourceInfo.Icon),
				Amount:                0,
				Count:                 0,
			}
		}

		// Add to category totals
		categoryMap[categoryID].Amount += transaction.Value
		categoryMap[categoryID].Count++

		// Add to money source totals within category
		categoryMoneySourceMap[categoryID][transaction.MoneySource].Amount += transaction.Value
		categoryMoneySourceMap[categoryID][transaction.MoneySource].Count++

		// Categorize expense type and track money source breakdown
		var targetMoneySourceMap map[financialsheet.MoneySource]*dashboard.MoneySourceDetail

		if s.isDebtExpense(transaction.Category) {
			debtTotal += transaction.Value
			debtCount++
			targetMoneySourceMap = debtMoneySourceMap
		} else if s.isFixedExpense(transaction.Category) {
			fixedTotal += transaction.Value
			fixedCount++
			targetMoneySourceMap = fixedMoneySourceMap
		} else {
			variableTotal += transaction.Value
			variableCount++
			targetMoneySourceMap = variableMoneySourceMap
		}

		// Initialize money source for expense type if not exists
		if _, exists := targetMoneySourceMap[transaction.MoneySource]; !exists {
			// Create a category instance to get money source info
			category := financialsheet.Category{
				Identifier: transaction.Category,
				Type:       transaction.Type,
			}
			moneySourceInfo := category.GetMoneySourceInfo(transaction.MoneySource)

			targetMoneySourceMap[transaction.MoneySource] = &dashboard.MoneySourceDetail{
				MoneySourceIdentifier: moneySourceInfo.Identifier,
				MoneySourceName:       moneySourceInfo.Name,
				Icon:                  string(moneySourceInfo.Icon),
				Amount:                0,
				Count:                 0,
			}
		}

		// Add to money source totals for expense type
		targetMoneySourceMap[transaction.MoneySource].Amount += transaction.Value
		targetMoneySourceMap[transaction.MoneySource].Count++
	}

	// Calculate percentages for categories and build money source breakdown
	totalExpenses := fixedTotal + variableTotal + debtTotal
	categories := make([]*dashboard.CategoryBreakdown, 0, len(categoryMap))

	for categoryID, category := range categoryMap {
		if totalExpenses > 0 {
			category.Percentage = float64(category.Amount) / float64(totalExpenses) * 100
		}

		// Build money source breakdown for this category
		moneySourceBreakdown := make([]*dashboard.MoneySourceDetail, 0, len(categoryMoneySourceMap[categoryID]))
		for _, moneySourceDetail := range categoryMoneySourceMap[categoryID] {
			// Calculate percentage within this category
			if category.Amount > 0 {
				moneySourceDetail.Percentage = float64(moneySourceDetail.Amount) / float64(category.Amount) * 100
			}
			moneySourceBreakdown = append(moneySourceBreakdown, moneySourceDetail)
		}

		// Sort money sources by amount (descending)
		sort.Slice(moneySourceBreakdown, func(i, j int) bool {
			return moneySourceBreakdown[i].Amount > moneySourceBreakdown[j].Amount
		})

		category.MoneySourceBreakdown = moneySourceBreakdown
		categories = append(categories, category)
	}

	// Sort categories by amount (descending)
	sort.Slice(categories, func(i, j int) bool {
		return categories[i].Amount > categories[j].Amount
	})

	// Build money source breakdown for expense types
	fixedMoneySourceBreakdown := s.buildMoneySourceBreakdown(fixedMoneySourceMap, fixedTotal)
	variableMoneySourceBreakdown := s.buildMoneySourceBreakdown(variableMoneySourceMap, variableTotal)
	debtMoneySourceBreakdown := s.buildMoneySourceBreakdown(debtMoneySourceMap, debtTotal)

	return &dashboard.ExpenseAnalysis{
		FixedExpenses: dashboard.ExpenseCategory{
			Total:                fixedTotal,
			Percentage:           s.calculatePercentage(fixedTotal, monthlyIncome),
			Count:                fixedCount,
			MoneySourceBreakdown: fixedMoneySourceBreakdown,
		},
		VariableExpenses: dashboard.ExpenseCategory{
			Total:                variableTotal,
			Percentage:           s.calculatePercentage(variableTotal, monthlyIncome),
			Count:                variableCount,
			MoneySourceBreakdown: variableMoneySourceBreakdown,
		},
		Debts: dashboard.ExpenseCategory{
			Total:                debtTotal,
			Percentage:           s.calculatePercentage(debtTotal, monthlyIncome),
			Count:                debtCount,
			MoneySourceBreakdown: debtMoneySourceBreakdown,
		},
		Categories: categories,
	}
}

// Helper function to build money source breakdown from a map
func (s *service) buildMoneySourceBreakdown(moneySourceMap map[financialsheet.MoneySource]*dashboard.MoneySourceDetail, total monetary.Amount) []*dashboard.MoneySourceDetail {
	breakdown := make([]*dashboard.MoneySourceDetail, 0, len(moneySourceMap))

	for _, moneySourceDetail := range moneySourceMap {
		// Calculate percentage within this expense type
		if total > 0 {
			moneySourceDetail.Percentage = float64(moneySourceDetail.Amount) / float64(total) * 100
		}
		breakdown = append(breakdown, moneySourceDetail)
	}

	// Sort money sources by amount (descending)
	sort.Slice(breakdown, func(i, j int) bool {
		return breakdown[i].Amount > breakdown[j].Amount
	})

	return breakdown
}

// Helper function to calculate stress score based on financial commitments
func (s *service) calculateStressScore(expenseAnalysis *dashboard.ExpenseAnalysis, monthlyIncome monetary.Amount) *dashboard.StressScore {
	fixedCommitmentPercent := expenseAnalysis.FixedExpenses.Percentage
	variableCommitmentPercent := expenseAnalysis.VariableExpenses.Percentage
	debtCommitmentPercent := expenseAnalysis.Debts.Percentage

	// Stress score formula: (fixedCommitment% * 0.5) + (variableCommitmentPercent * 0.2) +(debtCommitment% * 0.3)
	score := (fixedCommitmentPercent*0.5 + variableCommitmentPercent*0.2 + debtCommitmentPercent*0.3)

	// Ensure score is between 0 and 100
	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	// Determine level and provide insights
	var level, label, insight string
	switch {
	case score <= 20:
		level = "verylow"
		label = "Tranquilo"
		insight = "Parabéns! Sua vida financeira está em paz. Continue assim e aproveite o sossego!"
	case score > 20 && score <= 40:
		level = "low"
		label = "Leve"
		insight = "Alguns ajustes podem te deixar ainda mais tranquilo. Que tal revisar um gasto ou dois?"
	case score > 40 && score <= 60:
		level = "medium"
		label = "Moderado"
		insight = "Atenção! Seu estresse financeiro está aparecendo. Um bom planejamento pode mudar o jogo."
	case score > 60 && score <= 80:
		level = "high"
		label = "Alto"
		insight = "Seu dinheiro tá gritando por socorro! Bora organizar a casa antes que vire incêndio."
	case score > 80 && score <= 100:
		level = "veryhigh"
		label = "Crítico"
		insight = "Alerta vermelho! É hora de agir rápido e virar esse jogo. Comece cortando excessos e priorizando o essencial!"
	default:
		level = "unknown"
		label = "Desconhecido"
		insight = "Não foi possível determinar o nível de estresse financeiro."
	}

	return &dashboard.StressScore{
		Score:   score,
		Level:   level,
		Label:   label,
		Insight: insight,
	}
}

// Helper function to calculate commitment analysis
func (s *service) calculateCommitmentAnalysis(expenseAnalysis *dashboard.ExpenseAnalysis, monthlyIncome monetary.Amount) *dashboard.CommitmentAnalysis {
	fixedAmount := expenseAnalysis.FixedExpenses.Total
	variableAmount := expenseAnalysis.VariableExpenses.Total
	debtAmount := expenseAnalysis.Debts.Total

	totalExpenses := fixedAmount + variableAmount + debtAmount
	availableForSavings := monthlyIncome - totalExpenses

	return &dashboard.CommitmentAnalysis{
		FixedExpense: dashboard.CommitmentDetail{
			Amount:     fixedAmount,
			Percentage: s.calculatePercentage(fixedAmount, monthlyIncome),
		},
		VariableExpense: dashboard.CommitmentDetail{
			Amount:     variableAmount,
			Percentage: s.calculatePercentage(variableAmount, monthlyIncome),
		},
		Debt: dashboard.CommitmentDetail{
			Amount:     debtAmount,
			Percentage: s.calculatePercentage(debtAmount, monthlyIncome),
		},
		AvailableForSavings: availableForSavings,
	}
}

// Helper function to analyze payment methods
func (s *service) analyzePaymentMethods(transactions []*financialsheet.Transaction) []*dashboard.PaymentMethodSummary {
	paymentMethodMap := make(map[financialsheet.PaymentMethod]*dashboard.PaymentMethodSummary)
	var totalAmount monetary.Amount

	for _, transaction := range transactions {
		totalAmount += transaction.Value

		if _, exists := paymentMethodMap[transaction.PaymentMethod]; !exists {
			// Create a category instance to payment method info
			category := financialsheet.Category{
				Identifier: transaction.Category,
				Type:       transaction.Type,
			}
			paymentMethodInfo := category.GetPaymentMethodInfo(transaction.PaymentMethod)

			paymentMethodMap[transaction.PaymentMethod] = &dashboard.PaymentMethodSummary{
				PaymentMethodId:   strconv.Itoa(int(transaction.PaymentMethod)),
				PaymentMethodName: paymentMethodInfo.Name,
				Icon:              string(paymentMethodInfo.Icon),
				Amount:            0,
				Transactions:      0,
			}
		}

		paymentMethodMap[transaction.PaymentMethod].Amount += transaction.Value
		paymentMethodMap[transaction.PaymentMethod].Transactions++
	}

	// Calculate percentages and convert to slice
	paymentMethods := []*dashboard.PaymentMethodSummary{}
	for _, pm := range paymentMethodMap {
		if totalAmount > 0 {
			pm.Percentage = float64(pm.Amount) / float64(totalAmount) * 100
		}
		paymentMethods = append(paymentMethods, pm)
	}

	// Sort by amount (descending)
	sort.Slice(paymentMethods, func(i, j int) bool {
		return paymentMethods[i].Amount > paymentMethods[j].Amount
	})

	return paymentMethods
}

// Helper function to calculate spending variation between periods
func (s *service) calculateSpendingVariation(currentTransactions, previousTransactions []*financialsheet.Transaction) *dashboard.SpendingVariation {
	currentAnalysis := s.categorizeExpenses(currentTransactions, 1) // Use 1 as dummy income for percentage calculation
	previousAnalysis := s.categorizeExpenses(previousTransactions, 1)

	currentTotal := currentAnalysis.FixedExpenses.Total + currentAnalysis.VariableExpenses.Total + currentAnalysis.Debts.Total
	previousTotal := previousAnalysis.FixedExpenses.Total + previousAnalysis.VariableExpenses.Total + previousAnalysis.Debts.Total

	return &dashboard.SpendingVariation{
		TotalChange:    s.calculateVariationDetail(currentTotal, previousTotal),
		FixedChange:    s.calculateVariationDetail(currentAnalysis.FixedExpenses.Total, previousAnalysis.FixedExpenses.Total),
		VariableChange: s.calculateVariationDetail(currentAnalysis.VariableExpenses.Total, previousAnalysis.VariableExpenses.Total),
		DebtChange:     s.calculateVariationDetail(currentAnalysis.Debts.Total, previousAnalysis.Debts.Total),
		TopIncreases:   s.calculateTopMoneySourceVariations(currentTransactions, previousTransactions, true),
		TopReductions:  s.calculateTopMoneySourceVariations(currentTransactions, previousTransactions, false),
	}
}

// Helper function to calculate variation detail
func (s *service) calculateVariationDetail(current, previous monetary.Amount) dashboard.VariationDetail {
	difference := current - previous
	var percentage float64
	var direction string

	if previous > 0 {
		percentage = float64(difference) / float64(previous) * 100
	}

	switch {
	case difference > 0:
		direction = "increase"
	case difference < 0:
		direction = "decrease"
		difference = -difference // Make positive for display
		percentage = -percentage // Keep negative for calculation
	default:
		direction = "stable"
	}

	return dashboard.VariationDetail{
		Amount:     difference,
		Percentage: percentage,
		Direction:  direction,
	}
}

// Helper function to calculate top category variations
func (s *service) calculateTopCategoryVariations(currentCategories, previousCategories []*dashboard.CategoryBreakdown, increases bool) []*dashboard.CategoryVariation {
	// Create map of previous categories for quick lookup
	previousMap := make(map[string]monetary.Amount)
	for _, category := range previousCategories {
		previousMap[category.CategoryIdentifier] = category.Amount
	}

	var variations []*dashboard.CategoryVariation

	for _, current := range currentCategories {
		previous := previousMap[current.CategoryIdentifier]
		difference := current.Amount - previous

		// Skip if no change
		if difference == 0 {
			continue
		}

		// Filter based on increases/decreases
		if (increases && difference <= 0) || (!increases && difference >= 0) {
			continue
		}

		var percentage float64
		if previous > 0 {
			percentage = float64(difference) / float64(previous) * 100
		}

		direction := "increase"
		if difference < 0 {
			direction = "decrease"
			difference = -difference
			percentage = -percentage
		}

		variations = append(variations, &dashboard.CategoryVariation{
			CategoryIdentifier: current.CategoryIdentifier,
			CategoryName:       current.CategoryName,
			Amount:             difference,
			Percentage:         percentage,
			Direction:          direction,
		})
	}

	// Sort by amount (descending)
	sort.Slice(variations, func(i, j int) bool {
		return variations[i].Amount > variations[j].Amount
	})

	// Return top 5
	if len(variations) > 5 {
		variations = variations[:5]
	}

	return variations
}

// Helper function to calculate top money source variations
func (s *service) calculateTopMoneySourceVariations(currentTransactions, previousTransactions []*financialsheet.Transaction, increases bool) []*dashboard.MoneySourceVariation {
	// Create maps to aggregate money source amounts for each period
	currentMoneySourceMap := make(map[financialsheet.MoneySource]monetary.Amount)
	previousMoneySourceMap := make(map[financialsheet.MoneySource]monetary.Amount)
	moneySourceInfoMap := make(map[financialsheet.MoneySource]*financialsheet.MoneySourceInfo)

	// Aggregate current period money source amounts
	for _, transaction := range currentTransactions {
		currentMoneySourceMap[transaction.MoneySource] += transaction.Value

		// Store money source info for later use
		if _, exists := moneySourceInfoMap[transaction.MoneySource]; !exists {
			category := financialsheet.Category{
				Identifier: transaction.Category,
				Type:       transaction.Type,
			}
			moneySourceInfo := category.GetMoneySourceInfo(transaction.MoneySource)
			moneySourceInfoMap[transaction.MoneySource] = &moneySourceInfo
		}
	}

	// Aggregate previous period money source amounts
	for _, transaction := range previousTransactions {
		previousMoneySourceMap[transaction.MoneySource] += transaction.Value

		// Store money source info for later use
		if _, exists := moneySourceInfoMap[transaction.MoneySource]; !exists {
			category := financialsheet.Category{
				Identifier: transaction.Category,
				Type:       transaction.Type,
			}
			moneySourceInfo := category.GetMoneySourceInfo(transaction.MoneySource)
			moneySourceInfoMap[transaction.MoneySource] = &moneySourceInfo
		}
	}

	var variations []*dashboard.MoneySourceVariation

	// Calculate variations for all money sources that appear in current period
	for moneySource, currentAmount := range currentMoneySourceMap {
		previousAmount := previousMoneySourceMap[moneySource]
		difference := currentAmount - previousAmount

		// Skip if no change
		if difference == 0 {
			continue
		}

		// Filter based on increases/decreases
		if (increases && difference <= 0) || (!increases && difference >= 0) {
			continue
		}

		var percentage float64
		if previousAmount > 0 {
			percentage = float64(difference) / float64(previousAmount) * 100
		}

		direction := "increase"
		absoluteDifference := difference
		if difference < 0 {
			direction = "decrease"
			absoluteDifference = -difference
		}

		moneySourceInfo := moneySourceInfoMap[moneySource]
		variations = append(variations, &dashboard.MoneySourceVariation{
			MoneySourceIdentifier: moneySourceInfo.Identifier,
			MoneySourceName:       moneySourceInfo.Name,
			Icon:                  string(moneySourceInfo.Icon),
			Amount:                absoluteDifference,
			Percentage:            percentage,
			Direction:             direction,
		})
	}

	// Sort by amount (descending)
	sort.Slice(variations, func(i, j int) bool {
		return variations[i].Amount > variations[j].Amount
	})

	// Return top 5
	if len(variations) > 5 {
		variations = variations[:5]
	}

	return variations
}

// Helper function to calculate category breakdown for a specific category
func (s *service) calculateCategoryBreakdown(transactions []*financialsheet.Transaction, monthlyIncome monetary.Amount) dashboard.CategoryBreakdown {
	if len(transactions) == 0 {
		return dashboard.CategoryBreakdown{}
	}

	var totalAmount monetary.Amount

	// Get category details from the first transaction
	category := financialsheet.Category{
		Identifier: transactions[0].Category,
		Type:       transactions[0].Type,
	}
	categoryInfo := category.GetCategoryInfo()

	categoryID := categoryInfo.Identifier.String()
	categoryName := categoryInfo.Name
	categoryIcon := string(categoryInfo.Icon)
	isFixed := s.isFixedExpense(transactions[0].Category)
	isDebt := s.isDebtExpense(transactions[0].Category)

	// Map to track money source breakdown
	moneySourceMap := make(map[financialsheet.MoneySource]*dashboard.MoneySourceDetail)

	for _, transaction := range transactions {
		totalAmount += transaction.Value

		// Initialize money source if not exists
		if _, exists := moneySourceMap[transaction.MoneySource]; !exists {
			// Create a category instance to get money source info
			category := financialsheet.Category{
				Identifier: transaction.Category,
				Type:       transaction.Type,
				Icon:       categoryInfo.Icon,
			}
			moneySourceInfo := category.GetMoneySourceInfo(transaction.MoneySource)

			moneySourceMap[transaction.MoneySource] = &dashboard.MoneySourceDetail{
				MoneySourceIdentifier: moneySourceInfo.Identifier,
				MoneySourceName:       moneySourceInfo.Name,
				Icon:                  string(moneySourceInfo.Icon),
				Amount:                0,
				Count:                 0,
			}
		}

		// Add to money source totals
		moneySourceMap[transaction.MoneySource].Amount += transaction.Value
		moneySourceMap[transaction.MoneySource].Count++
	}

	// Build money source breakdown array
	moneySourceBreakdown := make([]*dashboard.MoneySourceDetail, 0, len(moneySourceMap))
	for _, moneySourceDetail := range moneySourceMap {
		// Calculate percentage within this category
		if totalAmount > 0 {
			moneySourceDetail.Percentage = float64(moneySourceDetail.Amount) / float64(totalAmount) * 100
		}
		moneySourceBreakdown = append(moneySourceBreakdown, moneySourceDetail)
	}

	// Sort money sources by amount (descending)
	sort.Slice(moneySourceBreakdown, func(i, j int) bool {
		return moneySourceBreakdown[i].Amount > moneySourceBreakdown[j].Amount
	})

	percentage := s.calculatePercentage(totalAmount, monthlyIncome)

	return dashboard.CategoryBreakdown{
		CategoryIdentifier:   categoryID,
		CategoryName:         categoryName,
		Icon:                 categoryIcon,
		Amount:               totalAmount,
		Percentage:           percentage,
		IsFixed:              isFixed,
		IsDebt:               isDebt,
		Count:                len(transactions),
		MoneySourceBreakdown: moneySourceBreakdown,
	}
}

// Helper function to determine if an expense is fixed
func (s *service) isFixedExpense(category financialsheet.CategoryIdentifier) bool {
	// Define which categories are considered fixed expenses
	fixedCategories := map[financialsheet.CategoryIdentifier]bool{
		financialsheet.CategoryIdentifierHousing:          true,
		financialsheet.CategoryIdentifierHouseBills:       true,
		financialsheet.CategoryIdentifierEducation:        true,
		financialsheet.CategoryIdentifierCommunication:    true,
		financialsheet.CategoryIdentifierHealth:           true, // Assuming health insurance/regular medical costs
		financialsheet.CategoryIdentifierPersonalReserves: true, // Emergency fund contributions
	}

	return fixedCategories[category]
}

// Helper function to determine if an expense is debt-related
func (s *service) isDebtExpense(category financialsheet.CategoryIdentifier) bool {
	return category == financialsheet.CategoryIdentifierDebts
}

// Helper function to calculate percentage
func (s *service) calculatePercentage(amount, total monetary.Amount) float64 {
	if total == 0 {
		return 0
	}
	return float64(amount) / float64(total) * 100
}

// Helper function to get Portuguese month name
func (s *service) GetPortugueseMonthName(month time.Month) string {
	monthNames := map[time.Month]string{
		time.January:   "Jan",
		time.February:  "Fev",
		time.March:     "Mar",
		time.April:     "Abr",
		time.May:       "Mai",
		time.June:      "Jun",
		time.July:      "Jul",
		time.August:    "Ago",
		time.September: "Set",
		time.October:   "Out",
		time.November:  "Nov",
		time.December:  "Dez",
	}
	return monthNames[month]
}

// getCategoryColor returns the color for a given category index
func (s *service) getCategoryColor(index int) string {
	colors := []string{
		GraphColor1, GraphColor2, GraphColor3, GraphColor4, GraphColor5,
		GraphColor6, GraphColor7, GraphColor8, GraphColor9, GraphColor10,
		GraphColor11, GraphColor12, GraphColor13, GraphColor14, GraphColor15,
		GraphColor16, GraphColor17, GraphColor18, GraphColor19, GraphColor20,
		GraphColor21, GraphColor22, GraphColor23, GraphColor24, GraphColor25,
		GraphColor26, GraphColor27, GraphColor28, GraphColor29,
	}
	return colors[index%len(colors)]
}

// FinancialStress Graph Colors
const (
	GraphColor1  = "#E9F6FF"
	GraphColor2  = "#FFE4D4"
	GraphColor3  = "#FFECBC"
	GraphColor4  = "#F5D7EA"
	GraphColor5  = "#E4E7E8"
	GraphColor6  = "#FEF8CA"
	GraphColor7  = "#DCFFFE"
	GraphColor8  = "#F2DCF9"
	GraphColor9  = "#EFFFFC"
	GraphColor10 = "#F4EDC2"
	GraphColor11 = "#CEE2FB"
	GraphColor12 = "#FFE5B8"
	GraphColor13 = "#E5DEFA"
	GraphColor14 = "#FEC9FB"
	GraphColor15 = "#E2E8E8"
	GraphColor16 = "#B9FFE1"
	GraphColor17 = "#F1EEB5"
	GraphColor18 = "#F9F5B0"
	GraphColor19 = "#FDCADF"
	GraphColor20 = "#A6FFCD"
	GraphColor21 = "#FFE7D1"
	GraphColor22 = "#B0FFDD"
	GraphColor23 = "#FFF6B5"
	GraphColor24 = "#C1FFF0"
	GraphColor25 = "#D3D9EC"
	GraphColor26 = "#B0ECD4"
	GraphColor27 = "#E1DBF0"
	GraphColor28 = "#F5F4E5"
	GraphColor29 = "#FFF6CB"
	GraphColor30 = "#FADEC6"
	GraphColor31 = "#FFDCE6"
	GraphColor32 = "#F0FCD4"
	GraphColor33 = "#FFD6B5"
	GraphColor34 = "#F7EDE8"
	GraphColor35 = "#FFDCB8"
	GraphColor36 = "#FFC7FF"
	GraphColor37 = "#B1FFDB"
	GraphColor38 = "#AFFFB5"
	GraphColor39 = "#E1F5FF"
	GraphColor40 = "#B4E9D3"
	GraphColor41 = "#CFE0FF"
	GraphColor42 = "#CAF3E2"
	GraphColor43 = "#FDFFB7"
	GraphColor44 = "#C4E0FF"
	GraphColor45 = "#FFFFD8"
	GraphColor46 = "#FFD5CD"
	GraphColor47 = "#A8F2C6"
	GraphColor48 = "#B5F4CE"
	GraphColor49 = "#FDE5C7"
	GraphColor50 = "#BBDEF9"
)

// #BCD1FB
// #CADBFD
// #D8E5FF
// #E7EFFF
// #F6F9FF
// #FFFFFF
// #A0EBCD
// #ABF0D5
// #B6F5DC
// #C1F8E2
// #CEFBE9
// #DBFDEF
// #EFBFE4
// #F4CAEA
// #F8D6EF
// #FBE2F5
// #FDEFFA
// #FEFDFE
// #9FEFB6
// #AAF4BF
// #B5F8C8
// #C2FAD2
// #CFFDDC
// #DDFEE6
// #A8D8FA
// #B5DFFD
// #C3E6FF
// #D2ECFF
// #E1F2FF
// #F0F9FE
// #FAD7A8
// #FDDEB5
// #FFE5C3
// #FFEBD2
// #FFF2E1
// #FEF8F0
// #C8EDF2
// #D4F2F6
// #E0F7FA
// #ECFAFC
// #FAFEFE
// #FFFEFE
// #E6D2E9
// #ECDBEF
// #F2E5F4
// #F8EFF9
// #FDFAFD
// #FFFFFF
// #FAF2A9
// #FDF5B6
