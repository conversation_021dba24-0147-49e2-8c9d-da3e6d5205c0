package financialsheet

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
	trash      *mongo.Collection

	categoryCollection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.FINANCIAL_SHEETS_COLLECTION),
		trash:      db.Collection(repository.FINANCIAL_SHEETS_COLLECTION_TRASH),

		categoryCollection: db.Collection(repository.FINANCIAL_SHEETS_COLLECTION_CATEGORIES),
	}

	// Create unique index on user field
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "userID", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on financial sheet.user field")
		db.Client().Disconnect(context.Background())
	}

	return repo
}

// CRUD
func (m mongoDB) Create(ctx context.Context, record *financialsheet.Record) (string, error) {
	insertedResult, err := m.collection.InsertOne(ctx, record)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, errors.FinancialSheetConflictExists, errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, errors.FinancialSheetCreateFailed, errors.Internal, err)
	}
	return insertedResult.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (m mongoDB) Find(ctx context.Context, id primitive.ObjectID) (*financialsheet.Record, error) {
	var record financialsheet.Record
	err := m.collection.FindOne(ctx, bson.D{{Key: "_id", Value: id}}).Decode(&record)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.FinancialSheetNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.FinancialSheetFindFailed, errors.Internal, err)
	}

	if record.ObjectID.IsZero() {
		return nil, errors.New(errors.Repository, errors.FinancialSheetInvalidID, errors.BadRequest, nil)
	}

	record.ID = record.ObjectID.Hex()
	return &record, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*financialsheet.Record, error) {
	cursor, err := m.collection.Find(ctx, bson.D{})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*financialsheet.Record
	if err = cursor.All(ctx, &records); err != nil {
		return nil, errors.New(errors.Repository, errors.FinancialSheetFindAllFailed, errors.Internal, err)
	}

	// Set ID field from ObjectID for each record
	for _, record := range records {
		record.ID = record.ObjectID.Hex()
	}

	return records, nil
}

func (m mongoDB) FindByUser(ctx context.Context, userID string) (*financialsheet.Record, error) {
	var record financialsheet.Record
	err := m.collection.FindOne(ctx, bson.D{{Key: "userID", Value: userID}}).Decode(&record)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.FinancialSheetUserNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.FinancialSheetFindByUserFailed, errors.Internal, err)
	}
	record.ID = record.ObjectID.Hex()
	return &record, nil
}

func (m mongoDB) Update(ctx context.Context, record *financialsheet.Record) error {
	if record.ObjectID.IsZero() {
		return errors.New(errors.Repository, errors.FinancialSheetInvalidID, errors.BadRequest, nil)
	}

	opts := options.Update().SetUpsert(false)
	result, err := m.collection.UpdateOne(ctx,
		bson.D{{Key: "_id", Value: record.ObjectID}},
		bson.D{{Key: "$set", Value: record}},
		opts,
	)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, errors.FinancialSheetConflictUpdate, errors.Conflict, err)
		}
		return errors.New(errors.Repository, errors.FinancialSheetUpdateFailed, errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, errors.FinancialSheetNotFound, errors.NotFound, nil)
	}
	return nil
}

func (m mongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.collection.DeleteOne(ctx, bson.D{{Key: "_id", Value: id}})
	if err != nil {
		return errors.New(errors.Repository, errors.FinancialSheetDeleteFailed, errors.Internal, err)
	}
	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, errors.FinancialSheetNotFound, errors.NotFound, nil)
	}
	return nil
}

// Category CRUD
func (m mongoDB) CreateCategory(ctx context.Context, category *financialsheet.Category) (string, error) {
	insertedResult, err := m.categoryCollection.InsertOne(ctx, category)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, errors.FinancialSheetCategoryConflictExists, errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, errors.FinancialSheetCategoryCreateFailed, errors.Internal, err)
	}
	category.ID = insertedResult.InsertedID.(primitive.ObjectID).Hex()
	return category.ID, nil
}

func (m mongoDB) FindCategory(ctx context.Context, id primitive.ObjectID) (*financialsheet.Category, error) {
	var category financialsheet.Category
	err := m.categoryCollection.FindOne(ctx, bson.D{{Key: "_id", Value: id}}).Decode(&category)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryFindFailed, errors.Internal, err)
	}

	if category.ObjectID.IsZero() {
		return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryInvalidID, errors.BadRequest, nil)
	}

	category.ID = category.ObjectID.Hex()
	return &category, nil
}

func (m mongoDB) FindAllCategories(ctx context.Context) ([]*financialsheet.Category, error) {
	cursor, err := m.categoryCollection.Find(ctx, bson.D{})
	if err != nil {
		return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryFindFailed, errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var categories []*financialsheet.Category
	if err = cursor.All(ctx, &categories); err != nil {
		return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryFindFailed, errors.Internal, err)
	}

	// Set ID field from ObjectID for each category
	for _, category := range categories {
		category.ID = category.ObjectID.Hex()
	}

	return categories, nil
}

// FindCategoryByUserAndIdentifier finds a category with empty user field or with the specified user field and the specified identifier
func (m mongoDB) FindCategoryByUserAndIdentifier(ctx context.Context, userID string, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error) {
	var category financialsheet.Category
	err := m.categoryCollection.FindOne(ctx, bson.D{{Key: "user", Value: bson.M{"$in": []string{userID, ""}}}, {Key: "identifier", Value: identifier}}).Decode(&category)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryFindFailed, errors.Internal, err)
	}
	category.ID = category.ObjectID.Hex()
	return &category, nil
}

func (m mongoDB) UpdateCategory(ctx context.Context, category *financialsheet.Category) error {
	if category.ObjectID.IsZero() {
		return errors.New(errors.Repository, errors.FinancialSheetCategoryInvalidID, errors.BadRequest, nil)
	}

	opts := options.Update().SetUpsert(false)
	result, err := m.categoryCollection.UpdateOne(ctx,
		bson.D{{Key: "_id", Value: category.ObjectID}},
		bson.D{{Key: "$set", Value: category}},
		opts,
	)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, errors.FinancialSheetCategoryConflictUpdate, errors.Conflict, err)
		}
		return errors.New(errors.Repository, errors.FinancialSheetCategoryUpdateFailed, errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, errors.FinancialSheetCategoryNotFound, errors.NotFound, nil)
	}
	return nil
}

func (m mongoDB) DeleteCategory(ctx context.Context, id primitive.ObjectID) error {
	// First, find the category to get its identifier for cleanup
	var category financialsheet.Category
	err := m.categoryCollection.FindOne(ctx, bson.D{{Key: "_id", Value: id}}).Decode(&category)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return errors.New(errors.Repository, errors.FinancialSheetCategoryNotFound, errors.NotFound, err)
		}
		return errors.New(errors.Repository, errors.FinancialSheetCategoryDeleteFailed, errors.Internal, err)
	}

	// Delete the category from the categories collection
	result, err := m.categoryCollection.DeleteOne(ctx, bson.D{{Key: "_id", Value: id}})
	if err != nil {
		return errors.New(errors.Repository, errors.FinancialSheetCategoryDeleteFailed, errors.Internal, err)
	}
	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, errors.FinancialSheetCategoryNotFound, errors.NotFound, nil)
	}

	// Remove the category from all financial sheet records
	// This removes the category from the categories array in each month's data
	// We need to use a more complex approach since MongoDB doesn't support nested array updates directly
	categoryIDStr := id.Hex()

	// Find all records that might contain this category
	cursor, err := m.collection.Find(ctx, bson.D{})
	if err != nil {
		// Log the error but don't fail the operation since the category was already deleted
		return nil
	}
	defer cursor.Close(ctx)

	// Process each record to remove the category from nested month data
	for cursor.Next(ctx) {
		var record financialsheet.Record
		if err := cursor.Decode(&record); err != nil {
			continue // Skip this record if decode fails
		}

		updated := false
		// Iterate through years and months to remove the category
		for year, yearData := range record.YearData {
			for month, monthData := range yearData {
				// Filter out the category to be deleted
				filteredCategories := make([]financialsheet.CategoryCard, 0, len(monthData.Categories))
				for _, cat := range monthData.Categories {
					if cat.ID != categoryIDStr && cat.Identifier != string(category.Identifier) {
						filteredCategories = append(filteredCategories, cat)
					} else {
						updated = true
					}
				}

				if updated {
					monthData.Categories = filteredCategories
					record.YearData[year][month] = monthData
				}
			}
		}

		// Update the record if any categories were removed
		if updated {
			_, updateErr := m.collection.UpdateOne(ctx,
				bson.D{{Key: "_id", Value: record.ObjectID}},
				bson.D{{Key: "$set", Value: bson.D{{Key: "yearData", Value: record.YearData}}}},
			)
			if updateErr != nil {
				// Log the error but continue processing other records
				continue
			}
		}
	}

	return nil
}

// Transactions CRUD
// FindAllTransactions implements Repository.FindAllTransactions
func (m mongoDB) FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType, yearFilter, monthFilter int) ([]*financialsheet.Transaction, error) {
	record, err := m.FindByUser(ctx, userID)
	if err != nil {
		return nil, errors.New(errors.Repository, errors.FinancialSheetUserNotFound, errors.NotFound, err)
	}

	var transactions []*financialsheet.Transaction
	for year, yearData := range record.YearData {
		// Filter by year if yearFilter is provided
		if yearFilter != 0 && year != yearFilter {
			continue
		}
		for monthStr, monthData := range yearData {
			// Filter by month if monthFilter is provided
			if monthFilter != 0 {
				currentMonth, _ := strconv.Atoi(monthStr)
				if currentMonth != monthFilter {
					continue
				}
			}
			for i := range monthData.Transactions {
				transaction := monthData.Transactions[i]
				// Filter by category type if provided
				if categoryType == "" || transaction.Type == categoryType {
					transaction.ID = transaction.ObjectID.Hex()
					transactions = append(transactions, &transaction)
				}
			}
		}
	}

	return transactions, nil
}

// FindTransactionMonthsInRange fetches a unique set of "YYYY-MM" strings for months that have transactions in our range.
func (m mongoDB) FindTransactionMonthsInRange(ctx context.Context, userID string, startMonth, endMonth time.Time) (map[string]bool, error) {
	record, err := m.FindByUser(ctx, userID)
	if err != nil {
		return nil, errors.New(errors.Repository, errors.FinancialSheetUserNotFound, errors.NotFound, err)
	}

	uniqueMonths := make(map[string]bool)

	for year, yearData := range record.YearData {
		for monthStr := range yearData {
			// Convert "YYYY-MM" string to time.Time
			yearMonth, err := time.Parse("2006-01", fmt.Sprintf("%d-%s", year, monthStr))
			if err != nil {
				continue // Skip malformed month strings
			}

			// CORRECTED: Use the proper exclusive end date check
			if !yearMonth.Before(startMonth) && yearMonth.Before(endMonth) {
				uniqueMonths[fmt.Sprintf("%d-%s", year, monthStr)] = true
			}
		}
	}

	return uniqueMonths, nil
}
