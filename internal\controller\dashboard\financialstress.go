package dashboard

import (
	"math"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/labstack/echo/v4"
)

// FindFinancialStress handles GET /dashboard/financialstress?period=30d
func (dc *controller) FindFinancialStress() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user ID from context (set by auth middleware)
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get period parameter with default value
		period := c.QueryParam("period")
		if period == "" {
			period = "30d" // Default to 30 days
		}

		// Validate period parameter
		if !isValidPeriod(period) {
			return errors.New(errors.Controller, "invalid period parameter", errors.BadRequest, nil)
		}

		// Get financial stress analysis from service
		financialStress, err := dc.Service.FindFinancialStress(ctx, userToken.Uid, period)
		if err != nil {
			return err
		}

		// Build the DTO
		categoriesDTO := []*ExpenseAnalysisCategoriesDTO{}
		if financialStress.ExpenseAnalysis != nil {
			for _, category := range financialStress.ExpenseAnalysis.Categories {
				categoriesDTO = append(categoriesDTO, &ExpenseAnalysisCategoriesDTO{
					CategoryIdentifier: category.CategoryIdentifier,
					CategoryName:       category.CategoryName,
					Icon:               category.Icon,
					Amount:             category.Amount,
					Percentage:         category.Percentage,
				})
			}
		}

		topIncreases := []*dashboard.MoneySourceVariation{}
		topReductions := []*dashboard.MoneySourceVariation{}

		if financialStress.SpendingVariation != nil {
			// Only append increase or reductions that have a percentage different than 0
			for _, increase := range financialStress.SpendingVariation.TopIncreases {
				if increase.Percentage != 0 {
					topIncreases = append(topIncreases, &dashboard.MoneySourceVariation{
						MoneySourceIdentifier: increase.MoneySourceIdentifier,
						MoneySourceName:       increase.MoneySourceName,
						Icon:                  increase.Icon,
						Amount:                increase.Amount,
						Percentage:            increase.Percentage,
						Direction:             increase.Direction,
					})
				}
			}

			for _, reduction := range financialStress.SpendingVariation.TopReductions {
				if reduction.Percentage != 0 {
					topReductions = append(topReductions, &dashboard.MoneySourceVariation{
						MoneySourceIdentifier: reduction.MoneySourceIdentifier,
						MoneySourceName:       reduction.MoneySourceName,
						Icon:                  reduction.Icon,
						Amount:                reduction.Amount,
						Percentage:            reduction.Percentage,
						Direction:             reduction.Direction,
					})
				}
			}
		}

		financialStressDTO := &FinancialStressDTO{
			Score:              financialStress.StressScore,
			CommitmentAnalysis: financialStress.CommitmentAnalysis,
			ExpenseAnalysis:    categoriesDTO,
			PaymentMethods:     financialStress.PaymentMethods,
			TopIncreases:       topIncreases,
			TopReductions:      topReductions,
			Period:             financialStress.Period,
			AnalysisDate:       financialStress.AnalysisDate.Format("2006-01-02"),
		}

		// Adjust to two decimal rouding the percentage information
		if financialStressDTO.Score != nil {
			financialStressDTO.Score.Score = math.Round(financialStressDTO.Score.Score*100) / 100 // rounds to 2 decimal places
		}

		if financialStressDTO.CommitmentAnalysis != nil {
			financialStressDTO.CommitmentAnalysis.FixedExpense.Percentage = math.Round(financialStressDTO.CommitmentAnalysis.FixedExpense.Percentage*100) / 100       // rounds to 2 decimal places
			financialStressDTO.CommitmentAnalysis.VariableExpense.Percentage = math.Round(financialStressDTO.CommitmentAnalysis.VariableExpense.Percentage*100) / 100 // rounds to 2 decimal places
			financialStressDTO.CommitmentAnalysis.Debt.Percentage = math.Round(financialStressDTO.CommitmentAnalysis.Debt.Percentage*100) / 100                       // rounds to 2 decimal places
		}

		for _, category := range financialStressDTO.ExpenseAnalysis {
			category.Percentage = math.Round(category.Percentage*100) / 100 // rounds to 2 decimal places
		}

		for _, paymentMethod := range financialStressDTO.PaymentMethods {
			paymentMethod.Percentage = math.Round(paymentMethod.Percentage*100) / 100 // rounds to 2 decimal places
		}

		for _, increase := range financialStressDTO.TopIncreases {
			increase.Percentage = math.Round(increase.Percentage*100) / 100 // rounds to 2 decimal places
		}

		for _, reduction := range financialStressDTO.TopReductions {
			reduction.Percentage = math.Round(reduction.Percentage*100) / 100 // rounds to 2 decimal places
		}

		return c.JSON(http.StatusOK, financialStressDTO)
	}
}

// FindFixedExpenses handles GET /dashboard/financialstress/expenses/fixed?period=30d
func (dc *controller) FindFixedExpenses() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user ID from context (set by auth middleware)
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get period parameter with default value
		period := c.QueryParam("period")
		if period == "" {
			period = "30d" // Default to 30 days
		}

		// Validate period parameter
		if !isValidPeriod(period) {
			return errors.New(errors.Controller, "invalid period parameter", errors.BadRequest, nil)
		}

		// Get fixed expenses analysis from service
		fixedExpenses, err := dc.Service.FindFixedExpenses(ctx, userToken.Uid, period)
		if err != nil {
			return err
		}

		// Adjust to two decimal rouding the percentage information
		fixedExpenses.Percentage = math.Round(fixedExpenses.Percentage*100) / 100 // rounds to 2 decimal places

		for _, moneySourceDetail := range fixedExpenses.MoneySourceBreakdown {
			moneySourceDetail.Percentage = math.Round(moneySourceDetail.Percentage*100) / 100 // rounds to 2 decimal places
		}

		return c.JSON(http.StatusOK, fixedExpenses)
	}
}

// FindVariableExpenses handles GET /dashboard/financialstress/expenses/variable?period=30d
func (dc *controller) FindVariableExpenses() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user ID from context (set by auth middleware)
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get period parameter with default value
		period := c.QueryParam("period")
		if period == "" {
			period = "30d" // Default to 30 days
		}

		// Validate period parameter
		if !isValidPeriod(period) {
			return errors.New(errors.Controller, "invalid period parameter", errors.BadRequest, nil)
		}

		// Get variable expenses analysis from service
		variableExpenses, err := dc.Service.FindVariableExpenses(ctx, userToken.Uid, period)
		if err != nil {
			return err
		}

		// Adjust to two decimal rouding the percentage information
		variableExpenses.Percentage = math.Round(variableExpenses.Percentage*100) / 100 // rounds to 2 decimal places

		for _, moneySourceDetail := range variableExpenses.MoneySourceBreakdown {
			moneySourceDetail.Percentage = math.Round(moneySourceDetail.Percentage*100) / 100 // rounds to 2 decimal places
		}

		return c.JSON(http.StatusOK, variableExpenses)
	}
}

// FindDebtExpenses handles GET /dashboard/financialstress/expenses/debt?period=30d
func (dc *controller) FindDebtExpenses() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user ID from context (set by auth middleware)
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get period parameter with default value
		period := c.QueryParam("period")
		if period == "" {
			period = "30d" // Default to 30 days
		}

		// Validate period parameter
		if !isValidPeriod(period) {
			return errors.New(errors.Controller, "invalid period parameter", errors.BadRequest, nil)
		}

		// Get debt expenses analysis from service
		debtExpenses, err := dc.Service.FindDebtExpenses(ctx, userToken.Uid, period)
		if err != nil {
			return err
		}

		// Adjust to two decimal rouding the percentage information
		debtExpenses.Percentage = math.Round(debtExpenses.Percentage*100) / 100 // rounds to 2 decimal places

		for _, moneySourceDetail := range debtExpenses.MoneySourceBreakdown {
			moneySourceDetail.Percentage = math.Round(moneySourceDetail.Percentage*100) / 100 // rounds to 2 decimal places
		}

		return c.JSON(http.StatusOK, debtExpenses)
	}
}

// FindExpenseAnalysisDetails handles GET /dashboard/financialstress/expenses/analysis/:identifier?period=30d
func (dc *controller) FindExpenseAnalysisDetails() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user ID from context (set by auth middleware)
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get category ID from path parameter
		categoryID := c.Param("identifier")
		if categoryID == "" {
			return errors.New(errors.Controller, "category ID is required", errors.BadRequest, nil)
		}

		// Get period parameter with default value
		period := c.QueryParam("period")
		if period == "" {
			period = "30d" // Default to 30 days
		}

		// Validate period parameter
		if !isValidPeriod(period) {
			return errors.New(errors.Controller, "invalid period parameter", errors.BadRequest, nil)
		}

		// Get category details from service
		expenseAnalysisDetailsDetails, err := dc.Service.FindExpenseAnalysisDetails(ctx, userToken.Uid, categoryID, period)
		if err != nil {
			return err
		}

		// Build the DTO
		// Adjust to two decimal rouding the percentage information
		if expenseAnalysisDetailsDetails.MonthlyVariation != nil {
			expenseAnalysisDetailsDetails.MonthlyVariation.Percentage = math.Round(expenseAnalysisDetailsDetails.MonthlyVariation.Percentage*100) / 100 // rounds to 2 decimal places
		}

		for _, moneySourceDetail := range expenseAnalysisDetailsDetails.MoneySourceBreakdown {
			moneySourceDetail.Percentage = math.Round(moneySourceDetail.Percentage*100) / 100 // rounds to 2 decimal places
		}

		expenseAnalysisDetailsDTO := &ExpenseAnalysisDetailsDTO{
			CategoryIdentifier:   expenseAnalysisDetailsDetails.CategoryIdentifier,
			CategoryName:         expenseAnalysisDetailsDetails.CategoryName,
			Amount:               expenseAnalysisDetailsDetails.Amount,
			MonthlyVariation:     expenseAnalysisDetailsDetails.MonthlyVariation,
			MoneySourceBreakdown: expenseAnalysisDetailsDetails.MoneySourceBreakdown,
			MonthlyEvolution:     expenseAnalysisDetailsDetails.MonthlyEvolution,
		}

		return c.JSON(http.StatusOK, expenseAnalysisDetailsDTO)
	}
}

// Helper function to validate period parameter
func isValidPeriod(period string) bool {
	validPeriods := map[string]bool{
		"7d":  true,
		"30d": true,
		"90d": true,
		"1y":  true,
	}

	return validPeriods[period]
}
