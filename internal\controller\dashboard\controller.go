package dashboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing"
	"github.com/dsoplabs/dinbora-backend/internal/service/dashboard"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// Dashboard
	FindDashboard() echo.HandlerFunc

	// Financial Map
	FindFinancialMap() echo.HandlerFunc

	// Cards
	FindFinancialMapCard() echo.HandlerFunc

	// IncomeSource CRUD
	CreateIncomeSource() echo.HandlerFunc
	FindIncomeSources() echo.HandlerFunc
	UpdateIncomeSource() echo.HandlerFunc
	DeleteIncomeSource() echo.HandlerFunc

	// StrategicFund operations
	FindStrategicFund() echo.HandlerFunc
	UpdateStrategicFund() echo.HandlerFunc
	UpdateStrategicFundGoal() echo.HandlerFunc

	// Investment CRUD
	CreateInvestment() echo.HandlerFunc
	FindInvestments() echo.HandlerFunc
	UpdateInvestment() echo.HandlerFunc
	DeleteInvestment() echo.HandlerFunc

	// Asset CRUD
	CreateAsset() echo.HandlerFunc
	FindAssets() echo.HandlerFunc
	UpdateAsset() echo.HandlerFunc
	DeleteAsset() echo.HandlerFunc

	// Snapshot operations
	FindNetWorthHistory() echo.HandlerFunc
	SyncNetworth() echo.HandlerFunc

	// Financial Stress operations
	FindFinancialStress() echo.HandlerFunc
	FindFixedExpenses() echo.HandlerFunc
	FindVariableExpenses() echo.HandlerFunc
	FindDebtExpenses() echo.HandlerFunc
	FindExpenseAnalysisDetails() echo.HandlerFunc

	// Financial Independence operations
	FindFinancialIndependence() echo.HandlerFunc
	UpdateRetirementTargetAmount() echo.HandlerFunc
}

type controller struct {
	Service        dashboard.Service
	BillingService billing.Service
}

func New(service dashboard.Service, billingService billing.Service) Controller {
	return &controller{
		Service:        service,
		BillingService: billingService,
	}
}

// RegisterRoutes connects the dashboard routes to the Echo router group
func (dc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	authChain := []echo.MiddlewareFunc{
		middlewares.AuthGuard(),
		middlewares.UserContextMiddleware(),
	}
	dinboraPlusChain := append(authChain, middlewares.RequireDinboraPlusAccess(dc.BillingService))

	dashboardGroup := currentGroup.Group("/dashboard", dinboraPlusChain...)

	// Dashboard
	dashboardGroup.GET("/me", dc.FindDashboard())

	// Financial Map
	dashboardGroup.GET("/financialmaps/me", dc.FindFinancialMap())

	// Cards
	cardsGroup := dashboardGroup.Group("/financialmaps/me/cards")
	cardsGroup.GET("", dc.FindFinancialMapCard())

	// IncomeSource endpoints
	incomeSourcesGroup := dashboardGroup.Group("/financialmaps/me/incomes")
	incomeSourcesGroup.POST("", dc.CreateIncomeSource())
	incomeSourcesGroup.GET("", dc.FindIncomeSources())
	incomeSourcesGroup.PUT("/:id", dc.UpdateIncomeSource())
	incomeSourcesGroup.DELETE("/:id", dc.DeleteIncomeSource())

	// StrategicFund endpoints
	strategicFundGroup := dashboardGroup.Group("/financialmaps/me/strategicfunds")
	strategicFundGroup.GET("", dc.FindStrategicFund())
	strategicFundGroup.PUT("", dc.UpdateStrategicFund())
	strategicFundGroup.PUT("/goal", dc.UpdateStrategicFundGoal())

	// Investment endpoints
	investmentsGroup := dashboardGroup.Group("/financialmaps/me/investments")
	investmentsGroup.POST("", dc.CreateInvestment())
	investmentsGroup.GET("", dc.FindInvestments())
	investmentsGroup.PUT("/:id", dc.UpdateInvestment())
	investmentsGroup.DELETE("/:id", dc.DeleteInvestment())

	// Asset endpoints
	assetsGroup := dashboardGroup.Group("/financialmaps/me/assets")
	assetsGroup.POST("", dc.CreateAsset())
	assetsGroup.GET("", dc.FindAssets())
	assetsGroup.PUT("/:id", dc.UpdateAsset())
	assetsGroup.DELETE("/:id", dc.DeleteAsset())

	// Snapshot endpoints
	snapshotsGroup := dashboardGroup.Group("/financialmaps/me/networths")
	snapshotsGroup.GET("", dc.FindNetWorthHistory())
	snapshotsGroup.POST("/sync", dc.SyncNetworth())

	// Financial Stress endpoints
	financialStressGroup := dashboardGroup.Group("/financialstress")
	financialStressGroup.GET("/me", dc.FindFinancialStress())
	financialStressGroup.GET("/me/expenses/fixed", dc.FindFixedExpenses())
	financialStressGroup.GET("/me/expenses/variable", dc.FindVariableExpenses())
	financialStressGroup.GET("/me/expenses/debt", dc.FindDebtExpenses())
	financialStressGroup.GET("/me/expenses/analysis/:identifier", dc.FindExpenseAnalysisDetails())

	// Financial Independence endpoints
	financialIndependenceGroup := dashboardGroup.Group("/financialindependence")
	financialIndependenceGroup.GET("/me", dc.FindFinancialIndependence())
	financialIndependenceGroup.PUT("/me", dc.UpdateRetirementTargetAmount())
}
