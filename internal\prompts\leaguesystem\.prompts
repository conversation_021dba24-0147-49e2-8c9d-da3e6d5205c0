I need to update the LeagueSystem of the financial sheet. In the @/internal\model\financialsheet\record.go you can find the object League which is referring to the league of the financialsheet.

The requirements changed and you can check on @/docs\feature\leaguesystem\leaguesystem.md . The idea is to give users hability to create leagues instead of having a global league. I still need to record individually the "Best points" defined in the Points struct.

We will need to store the league to in diferent document, so I'm thinking in something like "leagues". You can find all of them in @/internal\repository\collections.go

You can find the implementation of the financialsheet in the packages "financialsheet" (repository, service, controler and model layers)
@/internal\repository\financialsheet\mongo.go @/internal\repository\financialsheet\repository.go @/internal\service\financialsheet\service.go @/internal\controller\financialsheet\controller.go @/internal\model\financialsheet\model.go

The Level and LevelRequirement is the same (Bronze, Silver, etc.) in this new league system. The "Investidas" is just to track how much of transactions user added daily, but the Level will remain. Feel free to also add this in the leaguesystem.md documentation you can use @/docs\feature\financialsheet\financialsheet.md for reference.

The level is a caracteristic of the league and not the financial sheet. We should do the oposite. Have the levels in the league and refer to it in the financialsheet services in case needed. Same apply for level requirement and other dependencies like points, etc.

The link code can be a randon string which will identify who is the user inviting and also inditify which league the users which accepted the invitation will join.

Please use the sequencial-thinking mcp server to help the planning. Use the context7 for documentation reference and correct application of the business logic. If you think you will use any other library please also check in context7 for the most updated documentation.