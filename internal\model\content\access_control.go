package content

import (
	"strings"
	"time"
)

// AccessType defines the type of access for a TrailExtra
type AccessType string

const (
	// AccessTypeFree indicates access is granted to all users
	AccessTypeFree AccessType = "FREE"
	// AccessTypeCorporate indicates access is granted through a corporate contract
	AccessTypeCorporate AccessType = "CORPORATE"
	// AccessTypeSubscription indicates access is granted through a subscription
	AccessTypeSubscription AccessType = "SUBSCRIPTION"
	// AccessTypePremium indicates access is granted through premium status
	AccessTypePremium AccessType = "PREMIUM"
)

// AccessControl defines the access control rules for a TrailExtra
type AccessControl struct {
	// Type defines the type of access control (CORPORATE, SUBSCRIPTION, PREMIUM)
	Type AccessType `json:"type" bson:"type"`
	// ContractIDs contains the list of contract IDs that have access to this trail
	ContractIDs []string `json:"contractIds,omitempty" bson:"contractIds,omitempty"`
	// ValidFrom defines when the access starts
	Valid<PERSON>rom time.Time `json:"validFrom,omitempty" bson:"validFrom,omitempty"`
	// ValidUntil defines when the access ends
	ValidUntil time.Time `json:"validUntil,omitempty" bson:"validUntil,omitempty"`
	// UserClassifications contains the list of user classifications that have access
	UserClassifications []string `json:"userClassifications,omitempty" bson:"userClassifications,omitempty"`
}

// PrepareCreate prepares the AccessControl for creation
func (ac *AccessControl) PrepareCreate() error {
	// Convert type to uppercase
	ac.Type = AccessType(strings.ToUpper(string(ac.Type)))

	// Validate the access control
	if err := ac.ValidateCreate(); err != nil {
		return err
	}

	return nil
}

// ValidateCreate validates the AccessControl for creation
func (ac *AccessControl) ValidateCreate() error {
	// Type is required
	if ac.Type == "" {
		return ErrAccessControlRequiredType
	}

	// Validate type is one of the allowed values
	if ac.Type != AccessTypeFree && ac.Type != AccessTypeCorporate && ac.Type != AccessTypeSubscription && ac.Type != AccessTypePremium {
		return ErrAccessControlInvalidType
	}

	// For corporate access, contract IDs are required
	if ac.Type == AccessTypeCorporate && len(ac.ContractIDs) == 0 {
		return ErrAccessControlRequiredContractIDs
	}

	// If ValidUntil is set, it must be after ValidFrom
	if !ac.ValidUntil.IsZero() && !ac.ValidFrom.IsZero() && ac.ValidUntil.Before(ac.ValidFrom) {
		return ErrAccessControlInvalidDateRange
	}

	return nil
}

// IsActive checks if the access control is currently active
func (ac *AccessControl) IsActive() bool {
	now := time.Now()

	// If ValidFrom is set and it's in the future, access is not active yet
	if !ac.ValidFrom.IsZero() && now.Before(ac.ValidFrom) {
		return false
	}

	// If ValidUntil is set and it's in the past, access has expired
	if !ac.ValidUntil.IsZero() && now.After(ac.ValidUntil) {
		return false
	}

	return true
}

// HasAccess checks if a user has access based on their classification
func (ac *AccessControl) HasAccess(userClassification string) bool {
	// First check if the access control is active
	if !ac.IsActive() {
		return false
	}

	// If user classifications are specified, check if the user has one of them
	if len(ac.UserClassifications) > 0 {
		hasClassification := false
		for _, classification := range ac.UserClassifications {
			if classification == userClassification {
				hasClassification = true
				break
			}
		}
		if !hasClassification {
			return false
		}
	}

	return true
}
