package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// FinancialProfileHistory represents a historical record of a user's financial profile changes
type FinancialProfileHistory struct {
	ObjectID  primitive.ObjectID      `json:"-" bson:"_id,omitempty"`
	ID        string                  `json:"id,omitempty" bson:"-"`
	UserID    string                  `json:"userID" bson:"userID"`
	Status    FinancialProfileStatus  `json:"status" bson:"status"`
	CreatedAt time.Time               `json:"createdAt" bson:"createdAt"`
}

// PrepareCreate sets up the model for creation
func (fph *FinancialProfileHistory) PrepareCreate() error {
	fph.CreatedAt = time.Now()
	return nil
}

// PrepareUpdate sets up the model for update (not typically used for history records)
func (fph *FinancialProfileHistory) PrepareUpdate() error {
	// History records are typically immutable, but including for consistency
	return nil
}

// String returns the string representation of the financial profile status
func (fph *FinancialProfileHistory) String() string {
	switch fph.Status {
	case StatusOverindebted:
		return "Overindebted"
	case StatusIndebted:
		return "Indebted"
	case StatusBalanced:
		return "Balanced"
	case StatusInvestor:
		return "Investor"
	default:
		return "Undefined"
	}
}
