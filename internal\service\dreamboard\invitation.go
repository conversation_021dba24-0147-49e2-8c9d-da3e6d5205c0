package dreamboard

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// Invitation Management

// InviteDetails retrieves invitation details for a share link token
func (s *service) InviteDetails(ctx context.Context, code string) (*InviteDetailsDTO, error) {
	shareLink, err := s.FindShareLinkByCode(ctx, code)
	if err != nil {
		return nil, err
	}

	// Validate share link
	if !shareLink.IsEnabled {
		return nil, errors.New(errors.Service, "invite link is disabled", errors.BadRequest, nil)
	}
	if shareLink.ExpiresAt.Before(time.Now()) {
		return nil, errors.New(errors.Service, "invite link has expired", errors.BadRequest, nil)
	}

	// Get dream details
	dream, err := s.findDreamByID(ctx, shareLink.DreamID)
	if err != nil {
		return nil, err
	}

	// Get active contributions count
	activeContributions, err := s.FindActiveContributionsByDreamID(ctx, shareLink.DreamID)
	if err != nil {
		return nil, err
	}

	// TODO: Get creator user details from user service
	inviteDetails := &InviteDetailsDTO{
		DreamID:                  shareLink.DreamID,
		DreamTitle:               dream.Title,
		CreatorUserID:            dream.CreatorUserID,
		CreatorUserName:          "", // User service will be filled in the controller
		CreatorPhotoURL:          "", // User service will be filled in the controller
		EstimatedCost:            dream.EstimatedCost,
		CalculatedDurationMonths: dream.CalculatedDurationMonths,
		CurrentRaisedAmount:      dream.CurrentRaisedAmount,
		ParticipantCount:         len(activeContributions),
		IsExpired:                shareLink.ExpiresAt.Before(time.Now()),
		IsEnabled:                shareLink.IsEnabled,
	}

	return inviteDetails, nil
}

// JoinSharedDream allows a user to join a shared dream
func (s *service) JoinSharedDream(ctx context.Context, code string, userID string, isCreator bool, monthlyPledgedAmount monetary.Amount) (*dreamboard.Contribution, error) {
	shareLink, err := s.FindShareLinkByCode(ctx, code)
	if err != nil {
		return nil, err
	}

	// Validate share link
	if !shareLink.IsEnabled {
		return nil, errors.New(errors.Service, "invite link is disabled", errors.BadRequest, nil)
	}
	if shareLink.ExpiresAt.Before(time.Now()) {
		return nil, errors.New(errors.Service, "invite link has expired", errors.BadRequest, nil)
	}

	// Check if user already has an active contribution for this dream
	existing, err := s.Repository.FindContributionByDreamAndUser(ctx, shareLink.DreamID, userID)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return nil, err
	}
	if existing != nil && existing.Status == dreamboard.ContributionStatusActive {
		return nil, errors.New(errors.Service, "user already contributing to this dream", errors.Conflict, nil)
	}

	// Create new contribution
	contribution := &dreamboard.Contribution{
		DreamID:              shareLink.DreamID,
		ContributorUserID:    userID,
		IsCreator:            isCreator,
		MonthlyPledgedAmount: monthlyPledgedAmount,
		Status:               dreamboard.ContributionStatusActive,
		JoinedAt:             time.Now(),
		UpdatedAt:            time.Now(),
	}

	if err := contribution.Validate(); err != nil {
		return nil, err
	}

	_, err = s.Repository.CreateContribution(ctx, contribution)
	if err != nil {
		return nil, err
	}

	// Update dream's monthly savings if the user is not the creator (creator will be filling the monthly savings when creating the dream)
	// The MonthlySavings field in the dream represents the total amount pledged by all contributors
	// Look all contributions attached to a dream and sum the monthly pledged amount
	if !isCreator {
		contributions, err := s.FindContributionsByDreamID(ctx, shareLink.DreamID)
		if err != nil {
			return nil, err
		}

		var totalMonthlyPledged monetary.Amount
		for _, contribution := range contributions {
			totalMonthlyPledged += contribution.MonthlyPledgedAmount
		}

		dreamboard, err := s.findDreamboardByDreamID(ctx, shareLink.DreamID)
		if err != nil {
			return nil, err
		}

		dream, err := s.findDreamByID(ctx, shareLink.DreamID)
		if err != nil {
			return nil, err
		}

		dream.MonthlySavings = totalMonthlyPledged
		if _, err := s.UpdateDream(ctx, dreamboard, dream); err != nil {
			return nil, err
		}
	}

	contribution.ID = contribution.ObjectID.Hex()
	return contribution, nil
}

// Helper functions
// generateCode creates a cryptographically secure random token
func generateCode() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
