# Trail: Personalized Educational Trails with Access Control

The Trail feature in the Dinbora platform includes comprehensive access control functionality that enables premium, personalized educational content delivery. This feature supports both standard trails and trails with advanced access control, allowing customized financial education content to be delivered to specific user groups based on their access type, classification, or contractual relationships.

## Access Control System

The access control system is the core component of the Trail feature's advanced functionality, enabling fine-grained control over who can access specific educational content.

### Access Types

The Trail feature supports four primary access types:

1. **FREE** (`AccessTypeFree`):
   - Content accessible to all users without restrictions
   - Typically used with "standard" user classification
   - Ideal for basic educational content available to everyone
   - Does not require contract IDs or special subscriptions

2. **CORPORATE** (`AccessTypeCorporate`):
   - Content accessible through corporate contracts (e.g., schools, businesses)
   - Requires specific contract IDs to be specified
   - Ideal for B2B scenarios where an organization purchases access for its members

3. **SUBSCRIPTION** (`AccessTypeSubscription`):
   - Content accessible through individual subscriptions
   - Does not require contract IDs
   - Suitable for B2C scenarios where users purchase access directly

4. **PREMIUM** (`AccessTypePremium`):
   - Content accessible to users with premium status
   - Does not require contract IDs
   - Appropriate for tiered access models where premium users get additional content

### Access Control Structure

The `AccessControl` structure contains all the rules for determining access:

```go
type AccessControl struct {
    // Type defines the type of access control (FREE, CORPORATE, SUBSCRIPTION, PREMIUM)
    Type AccessType `json:"type" bson:"type"`

    // ContractIDs contains the list of contract IDs that have access to this trail
    ContractIDs []string `json:"contractIds,omitempty" bson:"contractIds,omitempty"`

    // ValidFrom defines when the access starts
    ValidFrom time.Time `json:"validFrom,omitempty" bson:"validFrom,omitempty"`

    // ValidUntil defines when the access ends
    ValidUntil time.Time `json:"validUntil,omitempty" bson:"validUntil,omitempty"`

    // UserClassifications contains the list of user classifications that have access
    UserClassifications []string `json:"userClassifications,omitempty" bson:"userClassifications,omitempty"`
}
```

### Access Validation Logic

Access validation occurs through two primary methods:

1. **Time-based Validation** (`IsActive()`):
   - Checks if the current date falls within the `ValidFrom` and `ValidUntil` range
   - If no dates are specified, access is considered active
   - Enables time-limited access for promotional or seasonal content

2. **User-based Validation** (`HasAccess()`):
   - For `CORPORATE` access: Checks if the user is associated with one of the specified contract IDs
   - For all access types: Checks if the user's classification matches one of the specified classifications
   - Combines time-based validation to ensure access is currently active

### Access Control Examples

#### Corporate Access Example

```json
"accessControl": {
  "type": "CORPORATE",
  "contractIds": ["contract123", "contract456"],
  "userClassifications": ["business", "enterprise"],
  "validFrom": "2023-01-01T00:00:00Z",
  "validUntil": "2024-12-31T23:59:59Z"
}
```

This configuration:
- Restricts access to users associated with contract123 or contract456
- Further restricts access to users with "business" or "enterprise" classification
- Limits access to the specified date range

#### Subscription Access Example

```json
"accessControl": {
  "type": "SUBSCRIPTION",
  "userClassifications": ["subscriber", "premium_subscriber"],
  "validFrom": "2023-01-01T00:00:00Z"
}
```

This configuration:
- Grants access to users with active subscriptions
- Further restricts access to users classified as "subscriber" or "premium_subscriber"
- Makes content available from January 1, 2023, with no end date

#### Premium Access Example

```json
"accessControl": {
  "type": "PREMIUM",
  "userClassifications": ["premium", "vip"]
}
```

This configuration:
- Grants access to premium users
- Restricts access to users classified as "premium" or "vip"
- Has no time limitations

#### Free Access Example

```json
"accessControl": {
  "type": "FREE",
  "userClassifications": ["standard"]
}
```

This configuration:
- Grants access to all users with standard classification
- No contract IDs or premium subscriptions required
- Ideal for basic educational content available to everyone
- Has no time limitations

## Trail Structure with Access Control

Trails with access control functionality include additional fields for access control and customization:

```go
type TrailExtra struct {
    ObjectID     primitive.ObjectID `json:"-" bson:"_id,omitempty"`
    ID           string             `json:"_id,omitempty" bson:"-"`
    Name         string             `json:"name" bson:"name"`
    Identifier   string             `json:"identifier" bson:"identifier"`
    Description  string             `json:"description" bson:"description"`
    Level        uint8              `json:"level" bson:"level"`
    Logo         string             `json:"logo" bson:"logo"`
    Color        string             `json:"color" bson:"color"`
    Lessons      []*Lesson          `json:"lessons" bson:"lessons"`
    Requirements []string           `json:"requirements" bson:"requirements"`
    Challenge    *Challenge         `json:"challenge" bson:"challenge"`

    // Access control fields
    AccessControl *AccessControl    `json:"accessControl" bson:"accessControl"`

    // Customization fields
    LogoURL     string             `json:"logoUrl" bson:"logoUrl"`
    ThemeColor  string             `json:"themeColor" bson:"themeColor"`

    CreatedAt   time.Time          `json:"createdAt" bson:"createdAt"`
    UpdatedAt   time.Time          `json:"updatedAt" bson:"updatedAt"`
}
```

### Required Fields

When creating or updating trails with access control, the following fields are **mandatory** and must be provided:

- **`name`** - The display name of the trail (cannot be empty)
- **`identifier`** - A unique identifier for the trail (cannot be empty)
- **`description`** - A description explaining what the trail covers (cannot be empty)
- **`logo`** - URL to the trail's logo image (cannot be empty)
- **`lessons`** - An array of lessons (cannot be null, but can be empty)
- **`accessControl`** - Access control configuration (cannot be null)

The validation logic in the `ValidateCreate()` method enforces these requirements and will return specific errors if any required field is missing:
- `ErrTrailExtraRequiredName` - when name is empty
- `ErrTrailExtraRequiredIdentifier` - when identifier is empty
- `ErrTrailExtraRequiredDescription` - when description is empty
- `ErrTrailExtraRequiredLogo` - when logo is empty
- `ErrTrailExtraRequiredLessons` - when lessons is null
- `ErrTrailExtraRequiredAccessControl` - when accessControl is null

### Key Components

1. **Basic Information**:
   - Standard trail fields (name, identifier, description, etc.)
   - Level indicator for difficulty
   - Visual elements (logo, color)

2. **Content Structure**:
   - Lessons: Sequential learning units with interactive content
   - Challenge: Final assessment to test knowledge
   - Requirements: Prerequisites for accessing the trail

3. **Access Control**:
   - AccessControl: Rules for determining who can access the content
   - Time-based and user-based access restrictions

4. **Customization**:
   - LogoURL: Partner or sponsor logo
   - ThemeColor: Custom theme color for visual elements

## API Endpoints

The Trail feature with access control provides the following API endpoints (V2):

### CRUD Operations

- `POST /contents/trails/extras` - Create a new trail with access control (Admin only)
- `GET /contents/trails/extras/:id` - Get a trail with access control by ID (Admin only)
- `GET /contents/trails/extras` - Get all trails with access control (Admin only)
- `GET /contents/trails/extras/findByIdentifier` - Find a trail with access control by identifier (Note: This endpoint needs to be fixed as it should be a GET method) (Admin only)
- `PUT /contents/trails/extras/:id` - Update a trail with access control (Admin only)
- `DELETE /contents/trails/extras/:id` - Delete a trail with access control (Admin only)

### Lesson Management

- `POST /contents/trails/extras/:trailExtraId/lessons` - Add a lesson to a trail with access control (Admin only)
- `GET /contents/trails/extras/:trailExtraId/lessons/:id` - Get a lesson from a trail with access control (Admin only)
- `PUT /contents/trails/extras/:trailExtraId/lessons/:id` - Update a lesson in a trail with access control (Admin only)
- `DELETE /contents/trails/extras/:trailExtraId/lessons/:id` - Delete a lesson from a trail with access control (Admin only)

### Card Data

- `GET /contents/trails/extras/cards/:id` - Get card data for a trail with access control (Admin only)
- `GET /contents/trails/extras/cards` - Get card data for all trails with access control (Admin only)

### Access Control

- `GET /contents/trails/extras/me` - Get trails with access control that the current user has access to
- `GET /contents/trails/extras/me/cards` - Get card data for trails with access control that the current user has access to

## Implementation Details

### Repository Layer

The Trail repository provides methods for CRUD operations and access control:

- Basic CRUD operations (Create, Read, Update, Delete)
- Optimized card data retrieval for UI display
- Access control filtering based on user contracts and classification

### Service Layer

The Trail service implements business logic and caching:

- CRUD operations with validation
- Lesson management within trails
- Efficient caching of frequently accessed data
- Access control verification

### Controller Layer

The Trail controller handles HTTP requests and responses:

- Route registration with appropriate middleware
- Request validation and error handling
- JWT token validation for authentication
- Integration with user and contract services for access control

## Business Use Cases

### 1. Corporate Financial Education

- **Scenario**: A company wants to provide customized financial education to its employees
- **Implementation**:
  - Create a trail with CORPORATE access type
  - Specify the company's contract ID
  - Customize with company logo and brand colors
  - Employees automatically see the content when they log in

### 2. Premium Subscription Content

- **Scenario**: Offer exclusive financial education content to premium subscribers
- **Implementation**:
  - Create a trail with SUBSCRIPTION access type
  - Set user classifications to target specific subscriber tiers
  - Users with active subscriptions see the content in their dashboard

### 3. Educational Partnerships

- **Scenario**: Collaborate with educational institutions to provide co-branded content
- **Implementation**:
  - Create a trail with CORPORATE access type
  - Specify the institution's contract ID
  - Customize with institution's logo and colors
  - Students and faculty see the institution-specific content

### 4. Time-Limited Promotional Content

- **Scenario**: Offer special content during financial literacy month
- **Implementation**:
  - Create a trail with appropriate access type
  - Set ValidFrom and ValidUntil to the promotion period
  - Content automatically appears and disappears based on the date range

## Migration and Deployment

Trail content with access control is managed through JSON files in the `migration/trails_extra` directory, similar to regular trails. Each file represents a single trail with all its lessons, challenges, and access control settings.

All migration files must include the required fields, particularly the **description** field which is mandatory for all trail instances with access control.

Example migration file structure:
```
migration_applied/
  trails_extra/
    standard_sample.json              # FREE access type
    corporate_sample.json             # CORPORATE access type
    subscription_sample.json          # SUBSCRIPTION access type
    premium_sample.json               # PREMIUM access type
```

Each JSON file must contain a valid trail structure with all required fields populated, including:
- `name`: Display name of the trail
- `identifier`: Unique identifier
- `description`: Detailed description of the trail content
- `logo`: URL to the trail logo
- `lessons`: Array of lesson objects
- `accessControl`: Access control configuration

## Conclusion

The Trail feature's access control functionality extends Dinbora's educational capabilities with personalized, access-controlled content. By leveraging the flexible access control system, the platform can deliver targeted financial education to specific user groups, enabling new business models and partnership opportunities.

**Important Note**: All trails with access control must include the mandatory `description` field, as this is enforced by the validation logic and is required for proper content management and user experience.
