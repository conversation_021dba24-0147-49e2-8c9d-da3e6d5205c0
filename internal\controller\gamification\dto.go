package gamification

type Achievement struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Identifier string `json:"identifier"`
	HowTo      string `json:"howto"`
	Level      uint8  `json:"level"`
	Logo       string `json:"logo"`
	Conquered  bool   `json:"conquered"`
}

// {
//         "_id": "62cd10aa9bf2385cba97f742",
//         "name": "Descobriu o DNA",
//         "identifier": "DNA",
//         "requirement": "67f6ddf3181babca8896e73c",
//         "level": 1,
//         "logo": "https://images.dinbora.com.br/conquitas/guardiao-dna.png",
//         "createdAt": "0001-01-01T00:00:00Z",
//         "updatedAt": "2025-07-28T17:13:16.741Z",
//         "conquered": true
//     },
