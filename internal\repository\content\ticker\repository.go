package ticker

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/content"
)

type Reader interface {
	Find(ctx context.Context, id string) (*content.Ticker, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.Ticker, error)
	FindByCategory(ctx context.Context, group string) ([]*content.Ticker, error)
	FindAll(ctx context.Context) ([]*content.Ticker, error)
}

type Writer interface {
	Create(ctx context.Context, wallet *content.Ticker) error
	Update(ctx context.Context, wallet *content.Ticker) error
	Delete(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
