package cache

import (
	"context"
	"sync"
	"time"
)

// InMemoryCache implements the CacheService interface using an in-memory map.
type InMemoryCache struct {
	items map[string]cacheItem
	mu    sync.RWMutex
	// Default expiration duration for items added without a specific TTL.
	// Use 0 for no default expiration.
	defaultExpiration time.Duration
	// Cleanup interval. Use 0 to disable automatic cleanup.
	cleanupInterval time.Duration
}

// cacheItem holds the cached value and its expiration time.
type cacheItem struct {
	value      interface{}
	expiration int64 // Unix nano timestamp
	permanent  bool  // True if the item should not expire based on TTL
}

// NewInMemoryCache creates a new in-memory cache instance.
// defaultExpiration: Default time-to-live for cache items.
// cleanupInterval: Interval at which expired items are removed. If 0, no cleanup occurs.
func NewInMemoryCache(defaultExpiration, cleanupInterval time.Duration) *InMemoryCache {
	c := &InMemoryCache{
		items:             make(map[string]cacheItem),
		defaultExpiration: defaultExpiration,
		cleanupInterval:   cleanupInterval,
	}

	if cleanupInterval > 0 {
		go c.startCleanupTicker()
	}

	return c
}

// Get retrieves an item from the cache.
func (c *InMemoryCache) Get(ctx context.Context, key string) (interface{}, bool) {
	c.mu.RLock()
	item, found := c.items[key]
	c.mu.RUnlock()

	if !found {
		return nil, false
	}

	// Check expiration
	if !item.permanent && item.expiration > 0 && time.Now().UnixNano() > item.expiration {
		// Item expired, delete it (lazy deletion)
		c.mu.Lock()
		delete(c.items, key)
		c.mu.Unlock()
		return nil, false
	}

	return item.value, true
}

// Set adds an item to the cache.
func (c *InMemoryCache) Set(ctx context.Context, key string, value interface{}, duration time.Duration) error {
	var expiration int64
	permanent := false

	if duration == 0 {
		duration = c.defaultExpiration
	}

	if duration > 0 {
		expiration = time.Now().Add(duration).UnixNano()
	} else {
		// Duration is negative or zero (and no default), treat as permanent
		permanent = true
		expiration = 0 // Or keep as 0, doesn't matter if permanent is true
	}

	c.mu.Lock()
	c.items[key] = cacheItem{
		value:      value,
		expiration: expiration,
		permanent:  permanent,
	}
	c.mu.Unlock()
	return nil // In-memory set rarely fails unless out of memory
}

// Delete removes an item from the cache.
func (c *InMemoryCache) Delete(ctx context.Context, key string) error {
	c.mu.Lock()
	delete(c.items, key)
	c.mu.Unlock()
	return nil
}

// deleteExpired removes expired items from the cache.
func (c *InMemoryCache) deleteExpired() {
	now := time.Now().UnixNano()
	c.mu.Lock()
	defer c.mu.Unlock()

	for k, v := range c.items {
		if !v.permanent && v.expiration > 0 && now > v.expiration {
			delete(c.items, k)
		}
	}
}

// startCleanupTicker runs the cleanup process at regular intervals.
func (c *InMemoryCache) startCleanupTicker() {
	ticker := time.NewTicker(c.cleanupInterval)
	defer ticker.Stop()
	for range ticker.C {
		c.deleteExpired()
	}
}

// // Optional Clear method
// func (c *InMemoryCache) Clear(ctx context.Context) error {
// 	c.mu.Lock()
// 	c.items = make(map[string]cacheItem)
// 	c.mu.Unlock()
// 	return nil
// }
