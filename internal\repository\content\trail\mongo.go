package trail

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection         *mongo.Collection
	extraCollection    *mongo.Collection
	tutorialCollection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		db.Collection(repository.TRAILS_COLLECTION),
		db.Collection(repository.TRAILS_EXTRA_COLLECTION),
		db.Collection(repository.TRAILS_TUTORIAL_COLLECTION),
	}

	// Create unique index on identifier field for efficient lookups for trail collection
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "identifier", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on trail.identifier field")
		db.Client().Disconnect(context.Background())
	}

	// Create unique index on identifier field for efficient lookups for trail extra collection
	_, err = repo.extraCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "identifier", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on trail.extra.identifier field")
		db.Client().Disconnect(context.Background())
	}

	// Create unique index on identifier field for efficient lookups for tuturial collection
	_, err = repo.tutorialCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "identifier", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on tutorial.identifier field")
		db.Client().Disconnect(context.Background())
	}

	// Create index on accessControl field for efficient access control queries for trail extra collection
	_, err = repo.extraCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "accessControl", Value: 1}},
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on trail.extra.accessControl field")
		db.Client().Disconnect(context.Background())
	}

	return repo
}

// Find searches for a trail by ID in both regular and extra trails collections
// This function is used by admin to retrieve all trails regardless of type
func (m mongoDB) Find(ctx context.Context, id string) (*content.Trail, error) {
	trailObjectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid trail ID format", errors.Validation, err)
	}

	// First try to find in regular trails collection
	var trailContent content.Trail
	if err = m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: trailObjectID}}).Decode(&trailContent); err == nil {
		return &trailContent, nil
	}

	// If not found in regular trails and error is not "no documents", return error
	if err != mongo.ErrNoDocuments {
		return nil, errors.New(errors.Repository, "failed to find trail in regular collection", errors.Internal, err)
	}

	// Try to find in extra trails collection
	if err = m.extraCollection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: trailObjectID}}).Decode(&trailContent); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "failed to find trail in extra collection", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find trail", errors.Internal, err)
	}

	return &trailContent, nil
}

// FindAll retrieves all trails from both regular and extra trails collections
// This function is used by admin to retrieve all trails regardless of type
func (m mongoDB) FindAll(ctx context.Context) ([]*content.Trail, error) {
	var allTrails []*content.Trail

	// Get all regular trails
	regularCursor, err := m.collection.Find(ctx, bson.D{}, nil)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query regular trails", errors.Internal, err)
	}
	defer regularCursor.Close(ctx)

	for regularCursor.Next(ctx) {
		var trail content.Trail
		if err = regularCursor.Decode(&trail); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode regular trail", errors.Internal, err)
		}
		allTrails = append(allTrails, &trail)
	}

	// Get all extra trails
	extraCursor, err := m.extraCollection.Find(ctx, bson.D{}, nil)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query extra trails", errors.Internal, err)
	}
	defer extraCursor.Close(ctx)

	for extraCursor.Next(ctx) {
		var trail content.Trail
		if err = extraCursor.Decode(&trail); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode extra trail", errors.Internal, err)
		}
		allTrails = append(allTrails, &trail)
	}

	return allTrails, nil
}

// FindByIdentifier searches for a trail by identifier in both regular and extra trails collections
// This function is used by admin to retrieve all trails regardless of type
func (m mongoDB) FindByIdentifier(ctx context.Context, identifier string) (*content.Trail, error) {
	// First try to find in regular trails collection
	var trailContent content.Trail
	err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "identifier", Value: identifier}}).Decode(&trailContent)

	if err == nil {
		return &trailContent, nil
	}

	// If not found in regular trails and error is not "no documents", return error
	if err != mongo.ErrNoDocuments {
		return nil, errors.New(errors.Repository, "failed to find trail in regular collection by identifier", errors.Internal, err)
	}

	// Try to find in extra trails collection
	if err := m.extraCollection.FindOne(ctx,
		bson.D{primitive.E{Key: "identifier", Value: identifier}}).Decode(&trailContent); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "trail not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find trail by identifier", errors.Internal, err)
	}

	return &trailContent, nil
}

// FindCardData searches for trail card data by ID in both regular and extra trails collections
// This function is used by admin to retrieve all trail cards regardless of type
func (m mongoDB) FindCardData(ctx context.Context, id string) (*content.TrailCard, error) {
	trailObjectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid trail ID format", errors.Validation, err)
	}

	// Set options to optimize query performance
	findOptions := options.FindOne()

	// Add projection to include only necessary fields for card data
	projection := bson.D{
		{Key: "accessControl", Value: 1},
		{Key: "companyLogo", Value: 1},
		{Key: "themeColor", Value: 1},
		{Key: "name", Value: 1},
		{Key: "identifier", Value: 1},
		{Key: "level", Value: 1},
		{Key: "logo", Value: 1},
		{Key: "color", Value: 1},
		{Key: "requirements", Value: 1},
	}
	findOptions.SetProjection(projection)

	// First try to find in regular trails collection
	var cardData content.TrailCard
	if err = m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: trailObjectID}},
		findOptions).Decode(&cardData); err == nil {
		cardData.ID = cardData.ObjectID.Hex()
		return &cardData, nil
	}

	// If not found in regular trails and error is not "no documents", return error
	if err != mongo.ErrNoDocuments {
		return nil, errors.New(errors.Repository, "failed to find trail card data in regular collection", errors.Internal, err)
	}

	// Try to find in extra trails collection
	if err = m.extraCollection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: trailObjectID}},
		findOptions).Decode(&cardData); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "trail not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find trail card data", errors.Internal, err)
	}

	cardData.ID = cardData.ObjectID.Hex()
	return &cardData, nil
}

// FindAllCardData retrieves all trail card data from both regular and extra trails collections
// This function is used by admin to retrieve all trail cards regardless of type
func (m mongoDB) FindAllCardData(ctx context.Context) ([]*content.TrailCard, error) {
	// Set options to optimize query performance
	findOptions := options.Find()

	// Add projection to include only necessary fields for card data
	projection := bson.D{
		{Key: "accessControl", Value: 1},
		{Key: "companyLogo", Value: 1},
		{Key: "themeColor", Value: 1},
		{Key: "name", Value: 1},
		{Key: "identifier", Value: 1},
		{Key: "level", Value: 1},
		{Key: "logo", Value: 1},
		{Key: "color", Value: 1},
		{Key: "requirements", Value: 1},
	}
	findOptions.SetProjection(projection)

	var allCardDataList []*content.TrailCard

	// Get all regular trail cards
	regularCursor, err := m.collection.Find(ctx, bson.D{}, findOptions)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query regular trail cards", errors.Internal, err)
	}
	defer regularCursor.Close(ctx)

	for regularCursor.Next(ctx) {
		var result struct {
			ObjectID     primitive.ObjectID `bson:"_id"`
			CompanyLogo  string             `bson:"companyLogo"`
			ThemeColor   string             `bson:"themeColor"`
			Name         string             `bson:"name"`
			Identifier   string             `bson:"identifier"`
			Level        uint8              `bson:"level"`
			Logo         string             `bson:"logo"`
			Color        string             `bson:"color"`
			Requirements []string           `bson:"requirements"`
		}

		if err = regularCursor.Decode(&result); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode regular trail card data", errors.Internal, err)
		}

		cardData := &content.TrailCard{
			ObjectID:     result.ObjectID,
			ID:           result.ObjectID.Hex(),
			CompanyLogo:  result.CompanyLogo,
			ThemeColor:   result.ThemeColor,
			Name:         result.Name,
			Identifier:   result.Identifier,
			Level:        result.Level,
			Logo:         result.Logo,
			Color:        result.Color,
			Requirements: result.Requirements,
		}

		allCardDataList = append(allCardDataList, cardData)
	}

	// Get all extra trail cards
	extraCursor, err := m.extraCollection.Find(ctx, bson.D{}, findOptions)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query extra trail cards", errors.Internal, err)
	}
	defer extraCursor.Close(ctx)

	for extraCursor.Next(ctx) {
		var result struct {
			ObjectID      primitive.ObjectID     `bson:"_id"`
			AccessControl *content.AccessControl `bson:"accessControl"`
			CompanyLogo   string                 `bson:"companyLogo"`
			ThemeColor    string                 `bson:"themeColor"`
			Name          string                 `bson:"name"`
			Identifier    string                 `bson:"identifier"`
			Level         uint8                  `bson:"level"`
			Logo          string                 `bson:"logo"`
			Color         string                 `bson:"color"`
			Requirements  []string               `bson:"requirements"`
		}

		if err = extraCursor.Decode(&result); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode extra trail card data", errors.Internal, err)
		}

		cardData := &content.TrailCard{
			ObjectID:      result.ObjectID,
			ID:            result.ObjectID.Hex(),
			AccessControl: result.AccessControl,
			CompanyLogo:   result.CompanyLogo,
			ThemeColor:    result.ThemeColor,
			Name:          result.Name,
			Identifier:    result.Identifier,
			Level:         result.Level,
			Logo:          result.Logo,
			Color:         result.Color,
			Requirements:  result.Requirements,
		}

		allCardDataList = append(allCardDataList, cardData)
	}

	return allCardDataList, nil
}
