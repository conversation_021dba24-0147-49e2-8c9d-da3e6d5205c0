package gamification

import "fmt"

// Hero represents a frontend gamification card what to display to the user
type Hero struct {
	Key         string `json:"key"`
	Title       string `json:"title"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Image       string `json:"image"`
	Order       int    `json:"order"` // Lower number = higher priority
	Conquered   bool   `json:"conquered"`
	TrailID     string `json:"trailId"`
}

const (
	HeroDinboraPlus       = "user-dashboard"
	HeroFinancialTree     = "financial-tree"
	HeroFinancialNote     = "financial-note"
	HeroDreamBoard        = "dream-board"
	HeroFinancialPlanning = "financial-planning"
)

var Heroes = map[string]Hero{
	HeroDinboraPlus: {
		Key:         HeroDinboraPlus,
		Title:       "",
		Name:        "Dinbora+",
		Description: "",
		Image:       "https://images.dinbora.com.br/heroes/dinbora-plus.png",
		Order:       1,
	},
	HeroFinancialTree: {
		Key:         HeroFinancialTree,
		Title:       "Ops! Seu DNA Financeiro ainda está sendo sequenciado!",
		Name:        "Meu DNA",
		Description: "Descubra qual é o seu perfil financeiro único completando a trilha Início da Jornada. Que tipo de investidor você é? A resposta está esperando por você!",
		Image:       "https://images.dinbora.com.br/heroes/financial-tree.png",
		Order:       2,
		TrailID:     "67f6ddf3181babca8896e73c", // Início da Jornada
	},
	HeroFinancialNote: {
		Key:         HeroFinancialNote,
		Title:       "Calma lá, detetive financeiro!",
		Name:        "Apontamento",
		Description: "Antes de investigar suas finanças, você precisa aprender a fazer o diagnóstico perfeito! Complete a trilha Diagnosticar e se torne um especialista das suas finanças.",
		Image:       "https://images.dinbora.com.br/heroes/financial-note.png",
		Order:       3,
		TrailID:     "67fd63f5886e2c50f312b910", // Diagnosticar
	},
	HeroDreamBoard: {
		Key:         HeroDreamBoard,
		Title:       "Seus sonhos estão trancados!",
		Name:        "Sonhos",
		Description: "Para abrir o baú dos seus desejos e transformá-los em metas reais, complete a trilha Sonhos. Que tal descobrir como realizar o impossível?",
		Image:       "https://images.dinbora.com.br/heroes/dreamboard.png",
		Order:       4,
		TrailID:     "67fd63fe286e566993c369f3", // Sonhar
	},
	HeroFinancialPlanning: {
		Key:         HeroFinancialPlanning,
		Title:       "Ainda não temos um plano de voo!",
		Name:        "Planejamento",
		Description: "Para criar o seu planejamento financeiro estratégico e decolar rumo aos seus objetivos, complete a trilha Orçar. Sua missão ao sucesso começa aqui!",
		Image:       "https://images.dinbora.com.br/heroes/financial-planning.png",
		Order:       5,
		TrailID:     "67fd6407fa56fc1ddbf88602", // Orçar
	},
}

func GetHero(key string) *Hero {
	if hero, ok := Heroes[key]; ok {
		return &hero
	}
	return nil
}

func GetAllHeroes() []*Hero {
	heroes := make([]*Hero, 0, len(Heroes))
	for _, hero := range Heroes {
		heroes = append(heroes, &hero)
	}
	return heroes
}

func MustGetHero(key string) *Hero {
	hero, ok := Heroes[key]
	if !ok {
		panic(fmt.Sprintf("gamification: hero with key '%s' does not exist", key))
	}
	return &hero
}

// const hero = [
//     { key: "user-dashboard", title: "learn.hero.dinboraplus", image: require("@assets/images/dinbora-plus.png") },
//     { key: "financial-tree", title: "learn.hero.financialTree", image: require("@assets/images/financial-tree.png") },
//     { key: "dream-board", title: "learn.hero.dreamBoard", image: require("@assets/images/dreamboard.png") },
//     { key: "financial-note", title: "learn.hero.financialNote", image: require("@assets/images/financial-note.png") },
//     { key: "financial-planning", title: "learn.hero.financialPlanning", image: require("@assets/images/financial-planning.png") }
//   ];
