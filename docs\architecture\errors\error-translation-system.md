# Error Translation System

This document describes the robust, clean, and scalable error translation solution implemented in the Dinbora backend.

## Overview

The error translation system provides:
- **Structured, Translatable Errors**: Errors contain translation keys for i18n lookup
- **Language Detection**: Automatic language detection from Accept-Language header
- **Translation Files**: JSON-based translation files for pt-BR, en, and es
- **Boundary Translation**: Error translation happens at the controller/middleware level
- **Backward Compatibility**: Existing error handling continues to work

## Architecture

### High-Level Flow

1. **Request arrives** with Accept-Language header
2. **Language Detection Middleware** detects user's preferred language
3. **Business Logic** creates structured errors with translation keys
4. **Error Handler Middleware** translates errors based on user language
5. **Response** contains translated error messages

### Components

#### 1. Enhanced DomainError (`internal/errors/domain.go`)

```go
type DomainError struct {
    layer          Layer
    message        Message
    translationKey string  // New field for i18n
    kind           Kind
    original       error
}
```

**Key Features:**
- Contains translation key for i18n lookup
- Maintains backward compatibility with existing Message field
- Provides helper functions for common error types

#### 2. I18n Service (`internal/service/i18n/`)

```go
type Service struct {
    bundle     *i18n.Bundle
    localizers map[string]*i18n.Localizer
}
```

**Responsibilities:**
- Load translation files from `locales/` directory
- Detect language from Accept-Language header
- Translate messages with fallback support
- Normalize language codes

#### 3. Language Detection Middleware (`internal/api/middlewares/language.go`)

**Features:**
- Parses Accept-Language header
- Stores detected language in Echo context
- Configurable with skipper function
- Falls back to Portuguese (pt) as default

#### 4. Enhanced Error Handler (`internal/api/middlewares/errorhandler.go`)

**Enhancements:**
- Integrates with i18n service
- Translates DomainError messages based on user language
- Maintains backward compatibility for non-translated errors

## Supported Languages

- **Portuguese (pt)** - Default language
- **English (en)**
- **Spanish (es)**

## Translation Files

Located in `locales/` directory:

```
locales/
├── pt.json    # Portuguese (default)
├── en.json    # English
└── es.json    # Spanish
```

### Translation File Format

Translation keys follow a hierarchical dot notation structure: `package.error.specificError`

```json
{
  "user.error.notFound": "Usuário não encontrado",
  "user.error.notFoundById": "Usuário não encontrado pelo ID",
  "auth.error.invalidCredentials": "Usuário ou senha inválidos",
  "financialSheet.error.createFailed": "Falha ao criar planilha financeira",
  "financialSheet.error.notFound": "Planilha financeira não encontrada"
}
```

**Key Structure:**
- **package**: The domain package (user, auth, financialSheet, etc.) organized alphabetically
- **category**: Always `error` for error messages
- **specificError**: The specific error type (notFound, notFoundById, createFailed, etc.)

## Usage Examples

### Creating Translatable Errors

#### Method 1: Using Helper Functions

```go
// Not found error
return errors.NewNotFoundError(
    errors.Service,
    errors.UserNotFound,
    errors.KeyUserErrorNotFound,
    nil,
)

// Not found by ID error
return errors.NewNotFoundError(
    errors.Service,
    errors.UserNotFound,
    errors.KeyUserErrorNotFoundById,
    nil,
)

// Authentication error
return errors.NewUnauthorizedError(
    errors.Controller,
    errors.AuthInvalidCredentials,
    errors.KeyAuthErrorInvalidCredentials,
    nil,
)
```

#### Method 2: Using NewWithTranslationKey

```go
return errors.NewWithTranslationKey(
    errors.Repository,
    errors.UserNotFound,
    errors.KeyUserErrorNotFound,
    errors.NotFound,
    originalErr,
)
```

#### Method 3: Backward Compatibility

```go
// Still works - uses message as translation key
return errors.New(
    errors.Service,
    errors.UserNotFound,
    errors.NotFound,
    nil,
)
```

### Manual Translation

```go
func (s *service) GetTranslatedMessage(ctx context.Context, key string) string {
    lang := middlewares.GetLanguageFromEchoContext(c)
    return s.i18nService.Translate(lang, key)
}
```

## Integration Guide

### 1. Server Setup

```go
// Initialize i18n service
i18nService, err := i18n.New()
if err != nil {
    log.Printf("Warning: Failed to initialize i18n service: %v", err)
}

// Setup error handler with i18n
errorHandler := middlewares.NewHttpErrorHandler()
if i18nService != nil {
    errorHandler.SetI18nService(i18nService)
    // Add language detection middleware
    e.Use(middlewares.LanguageDetection(i18nService))
}
e.HTTPErrorHandler = errorHandler.Handler
```

### 2. Creating New Translation Keys

1. **Add to translation files** (`locales/*.json`):
```json
{
  "package.error.newError": "New error message in Portuguese"
}
```

2. **Add constant** (`internal/errors/translation_keys.go`):
```go
const KeyPackageErrorNewError = "package.error.newError"
```

3. **Use in code**:
```go
return errors.NewNotFoundError(
    errors.Controller,
    "New error message",
    errors.KeyPackageErrorNewError,
    nil,
)
```

## Testing

### Language Detection

```bash
# Test Portuguese
curl -H 'Accept-Language: pt-BR' http://localhost:8080/api/endpoint

# Test English  
curl -H 'Accept-Language: en-US' http://localhost:8080/api/endpoint

# Test Spanish
curl -H 'Accept-Language: es-ES' http://localhost:8080/api/endpoint
```

### Running Tests

```bash
# Test i18n service
go test ./internal/service/i18n/...

# Test error handling
go test ./internal/errors/...

# Test middleware
go test ./internal/api/middlewares/...
```

## Best Practices

### 1. Error Creation
- Use helper functions (`NewValidationError`, `NewNotFoundError`, etc.)
- Always provide translation keys for user-facing errors

### 2. Translation Keys
- Use hierarchical dot notation: `package.error.specificError`
- Follow consistent naming patterns:
  - **Not found errors**: `package.error.notFound`, `package.error.notFoundById`, `package.error.notFoundByEmail`
  - **Conflict errors**: `package.error.conflictExists`, `package.error.conflictUpdate`
  - **Operation errors**: `package.error.createFailed`, `package.error.updateFailed`, `package.error.deleteFailed`
  - **Authentication errors**: `auth.error.invalidCredentials`, `auth.error.passwordResetFailed`
- Organize packages alphabetically
- Group related keys together within each package
- Maintain consistency across languages

### 3. Fallback Strategy
- Portuguese is the default fallback language
- If translation key not found, falls back to original message
- If i18n service unavailable, uses original message

### 4. Performance
- Translation files are loaded once at startup
- Localizers are cached per language
- Language detection happens once per request

## Migration Guide

### Existing Code
No changes required for existing error handling code. The system is backward compatible.

### New Code
Use the new helper functions and translation keys for better i18n support:

```go
// Old way (still works)
return errors.New(errors.Service, "user not found", errors.NotFound, nil)

// New way (recommended)
return errors.NewNotFoundError(
    errors.Service,
    errors.UserNotFound,
    errors.KeyUserErrorNotFound,
    nil,
)
```

## Troubleshooting

### Common Issues

1. **Translation files not found**
   - Ensure `locales/` directory exists in project root
   - Check file permissions

2. **Missing translations**
   - Add missing keys to all translation files
   - System falls back to original message

3. **Language not detected**
   - Check Accept-Language header format
   - Verify middleware is properly configured

### Debug Mode

Enable debug logging to see language detection and translation process:

```go
// Set LOG_LEVEL=DEBUG environment variable
```

## Future Enhancements

- Support for pluralization rules
- Context-aware translations
- Dynamic translation loading
- Translation management UI
- Additional language support
