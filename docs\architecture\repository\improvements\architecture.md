# Repository Layer Improvements

Caption: This document outlines architectural suggestions specifically for improving the repository layer of the codebase.
⏳ - Pending
✅ - Completed
🚧 - In Progress

## Repository Architecture - ⏳
Issue: Code Duplication for each Model
- Each model has its own repository file, leading to code duplication.
- Example: `internal/repository/financialsheet/repository.go` has similar code to `internal/repository/dreamboard/repository.go`.
- Solution: Create a generic repository interface to handle common CRUD operations.
- This will reduce code duplication and improve maintainability.
- Consider using generics in Go 1.18+ to create a type-safe generic repository.
- This will allow for a single repository implementation that can handle multiple models.
- if a model has specific logic, it can still implement its own repository interface that extends the generic one.
