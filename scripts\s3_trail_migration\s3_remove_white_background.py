import os
import sys
from PIL import Image, ImageSequence
import argparse

# --- Configuration ---
# Define "white" - adjust if your white isn't exactly 255,255,255
# Option 1: Exact white
WHITE_RGB = (255, 255, 255)
# Option 2: Tolerance (e.g., values close to white)
# Set tolerance > 0 to enable this. Higher value means more colors treated as white.
TOLERANCE = 15 # Example: Allows (250, 255, 252), etc. Set to 0 for exact match.
# --- End Configuration ---

def is_close_to_white(pixel_rgb):
    """Checks if a pixel's RGB values are close to white within tolerance."""
    if TOLERANCE == 0:
        return pixel_rgb == WHITE_RGB
    else:
        # Calculate sum of differences
        diff = sum(abs(p - w) for p, w in zip(pixel_rgb, WHITE_RGB))
        return diff <= TOLERANCE

def process_gif(input_path, output_path):
    """
    Opens a GIF, makes white pixels transparent frame by frame,
    and saves the result.
    """
    try:
        # Open the original GIF
        original_gif = Image.open(input_path)
        print(f"Processing {os.path.basename(input_path)}...")

        frames = []
        durations = []
        disposals = [] # To store disposal method per frame

        # Iterate through each frame of the GIF
        for frame_image in ImageSequence.Iterator(original_gif):
            # Get frame duration, default to 100ms if not specified
            duration = frame_image.info.get('duration', 100)
            durations.append(duration)

            # Get disposal method (how frame replaces previous one)
            # 2 usually means "restore background" which works well for transparency
            disposal = frame_image.info.get('disposal', 2)
            disposals.append(disposal)

            # Convert the frame to RGBA to handle transparency
            frame_rgba = frame_image.convert("RGBA")
            datas = frame_rgba.getdata()

            newData = []
            for item in datas:
                # Check if the pixel (without alpha) is white or close to it
                if is_close_to_white(item[:3]):
                    # Make it transparent (set alpha to 0)
                    newData.append((item[0], item[1], item[2], 0))
                else:
                    # Keep original pixel (ensure alpha is 255 / opaque)
                     newData.append((item[0], item[1], item[2], 255))


            frame_rgba.putdata(newData)
            frames.append(frame_rgba)

        # Ensure we have frames to save
        if not frames:
            print(f"Warning: No frames found or processed for {input_path}")
            return False

        # Save the processed frames as a new GIF
        frames[0].save(
            output_path,
            save_all=True,
            append_images=frames[1:],
            duration=durations,
            loop=original_gif.info.get('loop', 0), # Preserve loop count (0=infinite)
            disposal=disposals, # Use original/calculated disposal methods
            transparency=0, # Set transparency index (though RGBA uses alpha directly)
                             # Pillow sometimes needs this hint even for RGBA.
                             # The important part is the alpha channel we set.
            optimize=False # Avoid potential optimization issues with transparency
        )
        print(f"Saved transparent GIF to {output_path}")
        return True

    except FileNotFoundError:
        print(f"Error: Input file not found: {input_path}")
        return False
    except Exception as e:
        print(f"Error processing {input_path}: {e}")
        return False
    finally:
        # Make sure the original file is closed if it was opened
        if 'original_gif' in locals() and hasattr(original_gif, 'close'):
            original_gif.close()

def main():
    parser = argparse.ArgumentParser(description="Remove white background from GIF images.")
    parser.add_argument("input_dir", help="Path to the folder containing original GIF files.")
    parser.add_argument("output_dir", help="Path to the folder where transparent GIFs will be saved.")
    args = parser.parse_args()

    input_folder = args.input_dir
    output_folder = args.output_dir

    # Validate input directory
    if not os.path.isdir(input_folder):
        print(f"Error: Input directory not found: {input_folder}")
        sys.exit(1)

    # Create output directory if it doesn't exist
    os.makedirs(output_folder, exist_ok=True)
    print(f"Output will be saved to: {output_folder}")
    if TOLERANCE > 0:
        print(f"Using tolerance: {TOLERANCE} (pixels with RGB diff sum <= {TOLERANCE} from white will be transparent)")
    else:
        print("Using exact white (255, 255, 255) matching.")


    processed_count = 0
    skipped_count = 0
    error_count = 0

    # Process each file in the input directory
    for filename in os.listdir(input_folder):
        if filename.lower().endswith(".gif"):
            input_path = os.path.join(input_folder, filename)
            output_filename = f"{os.path.splitext(filename)[0]}_transparent.gif"
            output_path = os.path.join(output_folder, output_filename)

            if process_gif(input_path, output_path):
                processed_count += 1
            else:
                error_count += 1
        else:
            # print(f"Skipping non-GIF file: {filename}")
            skipped_count += 1

    print("\n--- Processing Summary ---")
    print(f"Successfully processed: {processed_count}")
    print(f"Skipped (non-GIF):     {skipped_count}")
    print(f"Errors encountered:   {error_count}")
    print("-------------------------")

if __name__ == "__main__":
    main()