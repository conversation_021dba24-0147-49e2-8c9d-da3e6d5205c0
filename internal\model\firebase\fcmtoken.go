package firebase

import (
	"time"
)

// FCMToken represents the structure for storing Firebase Cloud Messaging tokens.
type FCMToken struct {
	UserID    string    `json:"userID" bson:"userID"`       // Unique identifier for the user
	FCMToken  string    `json:"fcm" bson:"fcm"`             // Firebase Cloud Messaging token
	CreatedAt time.Time `json:"createdAt" bson:"createdAt"` // Timestamp for creation
	UpdatedAt time.Time `json:"updatedAt" bson:"updatedAt"` // Timestamp for last update
}
