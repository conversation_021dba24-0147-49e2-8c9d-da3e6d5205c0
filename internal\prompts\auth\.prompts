I would like to improve the authentication of my server. Currently the authentication can be accessed through the files /internal/service/auth/service.go /internal/controller/auth/controller.go the tokens are handled by the file /internal/api/token/jwt.go. Also there some middleware that look for authenticated user the responsible file is in /internal/api/middlewares/auth.go and /internal/api/middlewares/errorhandler.go

I will provide more information about Oauth2 and ClientID so you can plan this improvement better.

To apply these ideas to your own authentication system (for users who don’t use Gmail or Apple), you can build your own OAuth2 authorization server. Here’s a high-level overview of what that entails:

Act as Your Own Identity Provider:

When a user logs in with a username and password, your system validates their credentials.
Upon successful validation, instead of just creating a session, you generate an authorization code or token.
Implement OAuth2 Flows:

Authorization Code Flow:
Your client (e.g., a mobile app or web app) directs the user to your authorization endpoint. After the user logs in, you issue an authorization code. The client then exchanges this code at your token endpoint for an access token (and possibly a refresh token).
Implicit Flow or Hybrid Flow:
Depending on your client type (browser-based or mobile), you might choose one of these flows.
Token Issuance and Validation:

Access Tokens:
These tokens (often in the form of JWTs) allow your clients to access protected resources without needing to handle user credentials directly.
Refresh Tokens:
For long-lived sessions, a refresh token can be issued to allow clients to obtain new access tokens without requiring the user to re-authenticate frequently.
Security Measures:
Ensure tokens are securely stored, have proper expiration times, and support revocation if necessary.
(Optional) Use OpenID Connect for Authentication:

Since OAuth2 isn’t designed for authentication, adding OpenID Connect provides an identity layer. This means you can return an ID token (typically a JWT) that contains user identity information.
This helps clients verify not just that the user has been authorized, but also that the user is who they claim to be.
Leverage Existing Libraries and Frameworks:

Libraries can help you implement an OAuth2 server. For example:
Fosite
A robust, security-first OAuth2 and OpenID Connect framework developed by ORY. It's highly extensible and supports various OAuth2 flows along with advanced features like token revocation and introspection.
GitHub: ory/fosite

Osin
A lightweight and easy-to-integrate OAuth2 server library for Golang. It's been around for a while and provides the core functionality needed to build an OAuth2 server.
GitHub: openshift/osin

golang.org/x/oauth2
While this package is mainly used for OAuth2 client implementations (to interact with OAuth2 providers), it's useful if you need to integrate with other OAuth2 systems or even for parts of your own server's client-side flows.
Documentation: golang.org/x/oauth2

Each of these libraries has its own strengths:

Fosite is more comprehensive and security-focused, making it a great choice for production systems that require robust features.
Osin is simpler and might be easier to set up if you need a straightforward OAuth2 server.
golang.org/x/oauth2 is essential if you're working with OAuth2 on the client side or integrating with other OAuth2 providers.

Benefits of This Approach
Unified Security Model:
By using OAuth2/OpenID Connect, you apply a consistent, robust security protocol across all your authentication methods.
Decoupling of Client and Credentials:
Your clients receive tokens to interact with your API without directly handling sensitive user credentials.
Scalability and Flexibility:
Once your OAuth2 server is in place, adding new clients (like mobile apps or third-party services) becomes easier and more secure.

Evaluate all these informations, and check what is the best approach to follow, consider having a own OAuth2 authorization server to the api.

Now proceed with the planning of the feature.
