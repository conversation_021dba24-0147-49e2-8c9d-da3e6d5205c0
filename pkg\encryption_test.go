package pkg

import (
	"os"
	"testing"
)

func TestEncryption(t *testing.T) {
	originalString := "joaocastro"
	_ = os.Setenv("ENCRYPTION_KEY", "C&F)J@NcRfUjXn2r5u8x/A?D(G-KaPdS")

	encryptedString, err := Encrypt(originalString)
	if err != nil {
		t.<PERSON><PERSON>("Expected nil but was %v", err)
	}

	if encryptedString == originalString {
		t.<PERSON><PERSON>rf("expected encryptedString but was equals")
	}
}

func TestDecrypt(t *testing.T) {
	originalString := "joaocastro"
	_ = os.Setenv("ENCRYPTION_KEY", "C&F)J@NcRfUjXn2r5u8x/A?D(G-KaPdS")

	encryptedString, err := Encrypt(originalString)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Expected nil but was %v", err)
	}

	if encryptedString == originalString {
		t.<PERSON><PERSON><PERSON>("expected encryptedString but was equals")
	}

	decryptedString, err := Decrypt(encryptedString)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Expected nil but was %v", err)
	}

	if decryptedString != originalString {
		t.Errorf("expected to be equals but was %s", decryptedString)
	}
}
