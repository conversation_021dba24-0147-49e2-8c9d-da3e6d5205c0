import os
import sys
from PIL import Image, ImageSequence
import argparse

# --- Configuration ---
# Define "white" baseline
WHITE_RGB = (255, 255, 255)
# Tolerance for considering a pixel "light" or "white-ish" border
# This might need to be higher than initial attempts (e.g., 50-80)
BORDER_TOLERANCE = 65 # Adjust based on results

# Minimum alpha for a pixel to be considered *not* transparent
# (Used to check neighbors)
MIN_OPAQUE_ALPHA = 1
# --- End Configuration ---

def is_close_to_white(rgb, tolerance):
    """Checks if an RGB tuple is close to white within tolerance."""
    if tolerance == 0:
        return rgb == WHITE_RGB
    else:
        diff = sum(abs(p - w) for p, w in zip(rgb, WHITE_RGB))
        return diff <= tolerance

def has_transparent_neighbor(x, y, width, height, pixel_data):
    """Checks if pixel (x,y) has a neighbor with alpha < MIN_OPAQUE_ALPHA."""
    for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]: # Check N, S, E, W
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            idx = ny * width + nx
            # Check if neighbor pixel data is valid and if alpha is effectively 0
            if isinstance(pixel_data[idx], tuple) and len(pixel_data[idx]) >= 4:
                if pixel_data[idx][3] < MIN_OPAQUE_ALPHA: # Check if neighbor is transparent
                    return True
            # Handle cases where neighbor might be just RGB (less common but possible)
            elif isinstance(pixel_data[idx], tuple) and len(pixel_data[idx]) == 3:
                 # Treat RGB pixels as fully opaque - they cannot be the transparent neighbor
                 pass # Cannot be transparent
            # else: # Optional logging for unexpected formats
                # print(f"Warning: Unexpected neighbor pixel format at {nx},{ny}: {pixel_data[idx]}")


    return False # No fully transparent neighbors found

def remove_border(input_path, output_path, tolerance):
    """
    Opens GIF, finds light-colored pixels adjacent to transparency,
    and makes them fully transparent.
    """
    try:
        original_gif = Image.open(input_path)
        print(f"Removing edge border for {os.path.basename(input_path)}...")

        processed_frames = []
        durations = []
        disposals = []
        is_animated = getattr(original_gif, "is_animated", False)

        for i, frame_image in enumerate(ImageSequence.Iterator(original_gif)):
            duration = frame_image.info.get('duration', 100)
            durations.append(duration)
            disposal = frame_image.info.get('disposal', 2)
            disposals.append(disposal)

            # Work on RGBA copy
            if frame_image.mode != 'RGBA':
                frame_rgba = frame_image.convert("RGBA")
            else:
                frame_rgba = frame_image.copy()

            width, height = frame_rgba.size
            current_pixels = list(frame_rgba.getdata())
            new_pixels = list(current_pixels) # Modifiable copy
            pixels_changed = 0

            # Iterate through pixels
            for y in range(height):
                for x in range(width):
                    index = y * width + x
                    # Ensure pixel data is valid before unpacking
                    if not isinstance(current_pixels[index], tuple) or len(current_pixels[index]) < 4:
                        continue # Skip malformed pixel data

                    r, g, b, a = current_pixels[index]

                    # Check conditions:
                    # 1. Pixel is not already fully transparent (a >= MIN_OPAQUE_ALPHA)
                    # 2. Pixel color is close to white (is_close_to_white)
                    # 3. Pixel has a fully transparent neighbor (has_transparent_neighbor)
                    if a >= MIN_OPAQUE_ALPHA and \
                       is_close_to_white((r, g, b), tolerance) and \
                       has_transparent_neighbor(x, y, width, height, current_pixels):

                        # Make this border pixel fully transparent
                        new_pixels[index] = (r, g, b, 0)
                        pixels_changed += 1

            # Update frame if changes occurred
            if pixels_changed > 0:
                print(f"  Frame {i}: Cleared {pixels_changed} border pixels.")
                frame_rgba.putdata(new_pixels)
            # else:
                # print(f"  Frame {i}: No border pixels detected/changed.")

            processed_frames.append(frame_rgba)

        # --- Saving Logic ---
        if not processed_frames:
            print(f"Warning: No frames processed for {input_path}")
            original_gif.close()
            return False

        loop_count = original_gif.info.get('loop', 0) if is_animated else 0
        processed_frames[0].save(
            output_path, save_all=True, append_images=processed_frames[1:],
            duration=durations if is_animated else 0, loop=loop_count,
            disposal=disposals if is_animated else 2, optimize=False, transparency=0
        )
        print(f"Saved border-cleaned GIF to {output_path}")
        return True

    except FileNotFoundError:
        print(f"Error: Input file not found: {input_path}")
        return False
    except Exception as e:
        print(f"Error processing {input_path}: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'original_gif' in locals() and hasattr(original_gif, 'close'):
            original_gif.close()

def main():
    # Declare global intent *before* using BORDER_TOLERANCE in default
    global BORDER_TOLERANCE

    parser = argparse.ArgumentParser(description="Remove light-colored border pixels adjacent to transparent areas in GIFs.")
    parser.add_argument("input_dir", help="Path to folder containing GIFs with borders.")
    parser.add_argument("output_dir", help="Path to folder where cleaned GIFs will be saved.")
    parser.add_argument("-t", "--tolerance", type=int, default=BORDER_TOLERANCE,
                        help=f"Color tolerance (closeness to white) for detecting border pixels. Default: {BORDER_TOLERANCE}")

    args = parser.parse_args()
    input_folder = args.input_dir
    output_folder = args.output_dir
    # Update global tolerance from args
    BORDER_TOLERANCE = args.tolerance

    if not os.path.isdir(input_folder):
        print(f"Error: Input directory not found: {input_folder}")
        sys.exit(1)

    os.makedirs(output_folder, exist_ok=True)
    print(f"Processing GIFs from: {input_folder}")
    print(f"Saving results to:  {output_folder}")
    print(f"Using border tolerance: {BORDER_TOLERANCE}")

    processed_count = 0
    skipped_count = 0
    error_count = 0

    for filename in os.listdir(input_folder):
        if filename.lower().endswith(".gif"):
            input_path = os.path.join(input_folder, filename)
            output_path = os.path.join(output_folder, filename) # Keep original name

            if remove_border(input_path, output_path, BORDER_TOLERANCE):
                processed_count += 1
            else:
                error_count += 1
        else:
            skipped_count += 1

    print("\n--- Edge Border Removal Summary ---")
    print(f"Successfully processed: {processed_count}")
    print(f"Skipped (non-GIF):     {skipped_count}")
    print(f"Errors encountered:   {error_count}")
    print("---------------------------------")

if __name__ == "__main__":
    main()