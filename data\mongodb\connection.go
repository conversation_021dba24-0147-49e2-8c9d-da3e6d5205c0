package mongodb

import (
	"context"
	"fmt" // Import fmt
	"log"
	"os"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

var (
	clientInstance *mongo.Client
	dbInstance     *mongo.Database
	connectOnce    sync.Once
)

const (
	connectTimeout = 10 * time.Second // Standard connect timeout
	// --- M0 Specific Settings ---
	maxPoolSize     uint64 = 100             // LOWERED: Keep pool small. 100 is likely overkill.
	minPoolSize     uint64 = 2               // Optional: Keep a couple warm, but not many.
	maxConnIdleTime        = 5 * time.Minute // Standard idle timeout
	// Optional: Set server selection timeout (time to find a suitable server)
	serverSelectionTimeout = 10 * time.Second
	// Optional: Set socket timeout (max time for network operations)
	// socketTimeout          = 30 * time.Second // Can keep default or set explicitly
)

// const (
// 	connectTimeout        = 10 * time.Second // Standard connect timeout
// 	// --- Flex Tier Settings ---
// 	maxPoolSize    uint64 = 100 // RESTORED: Start with the driver default. Monitor!
// 	minPoolSize    uint64 = 5   // Optional: Keep a few warm.
// 	maxConnIdleTime       = 5 * time.Minute // Standard idle timeout
// 	// Optional: Set server selection timeout
// 	serverSelectionTimeout = 15 * time.Second // Can be slightly more generous
// 	// Optional: Set socket timeout
// 	// socketTimeout          = 60 * time.Second // Can be slightly more generous
// )

// Connect initializes and returns a thread-safe singleton *mongo.Database instance.
// It manages a single underlying mongo.Client.
func Connect(ctx context.Context) (*mongo.Database, error) {
	var err error // Use a local error variable for the Once.Do scope

	connectOnce.Do(func() {
		log.Println("Initializing MongoDB client and database connection...")
		mongoURI := os.Getenv("DATABASE_URL")
		dbName := os.Getenv("DATABASE_NAME")

		if mongoURI == "" {
			err = fmt.Errorf("DATABASE_URL environment variable not set")
			log.Println(err)
			return
		}
		if dbName == "" {
			err = fmt.Errorf("DATABASE_NAME environment variable not set")
			log.Println(err)
			return
		}

		// Configure Connection Pool Options
		clientOptions := options.Client().ApplyURI(mongoURI)
		clientOptions.SetMaxPoolSize(maxPoolSize)
		clientOptions.SetMinPoolSize(minPoolSize)
		clientOptions.SetMaxConnIdleTime(maxConnIdleTime)
		clientOptions.SetConnectTimeout(connectTimeout)
		clientOptions.SetServerSelectionTimeout(serverSelectionTimeout)
		// clientOptions.SetSocketTimeout(socketTimeout) // If you uncomment and set it

		// Connect
		client, connectErr := mongo.Connect(ctx, clientOptions)
		if connectErr != nil {
			err = fmt.Errorf("mongo.Connect error: %w", connectErr)
			log.Printf("Error connecting to MongoDB: %v", err)
			return
		}

		// Ping to verify connection
		pingCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
		defer cancel()
		pingErr := client.Ping(pingCtx, readpref.Primary())
		if pingErr != nil {
			_ = client.Disconnect(context.Background()) // Attempt cleanup
			err = fmt.Errorf("ping error: %w", pingErr)
			log.Printf("Error pinging MongoDB: %v", err)
			return
		}

		log.Printf("MongoDB client connected successfully. Using database: %s", dbName)
		clientInstance = client              // Assign to global singleton
		dbInstance = client.Database(dbName) // Assign to global singleton
	})

	// Return error if initialization failed within Once.Do
	if err != nil {
		return nil, err
	}

	// Safety check - should not happen if err is nil
	if dbInstance == nil {
		log.Println("Error: MongoDB database instance is nil after initialization attempt.")
		return nil, fmt.Errorf("failed to initialize mongo database instance")
	}

	// Return the singleton database instance
	return dbInstance, nil
}

// DisconnectClient should be called during graceful shutdown
// It accesses the singleton clientInstance directly.
func DisconnectClient(ctx context.Context) {
	if clientInstance != nil {
		log.Println("Disconnecting MongoDB client...")
		if err := clientInstance.Disconnect(ctx); err != nil {
			log.Printf("Error disconnecting MongoDB client: %v", err)
		}
		clientInstance = nil // Clear instance after disconnect
		dbInstance = nil
	} else {
		log.Println("MongoDB client already disconnected or never initialized.")
	}
}

// GetClient returns the underlying singleton client if needed elsewhere
// (though passing the *mongo.Database is usually sufficient)
func GetClient() *mongo.Client {
	// Ensure Connect has been called at least once if using this directly early on
	if clientInstance == nil {
		log.Println("Warning: GetClient called before client was initialized.")
	}
	return clientInstance
}
