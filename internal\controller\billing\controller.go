package billing

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"os"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	_billing "github.com/dsoplabs/dinbora-backend/internal/service/billing"
	"github.com/labstack/echo/v4"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Controller defines the billing controller interface
type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// Plan endpoints
	CreatePlan() echo.HandlerFunc
	FindPlan() echo.HandlerFunc
	FindAllPlans() echo.HandlerFunc
	FindActivePlans() echo.HandlerFunc
	UpdatePlan() echo.HandlerFunc
	DeletePlan() echo.HandlerFunc

	// Subscription endpoints
	CreateSubscription() echo.HandlerFunc
	FindSubscription() echo.HandlerFunc
	FindUserSubscriptions() echo.HandlerFunc
	FindActiveUserSubscriptions() echo.HandlerFunc
	CancelSubscription() echo.HandlerFunc
	SuspendSubscription() echo.HandlerFunc
	ReactivateSubscription() echo.HandlerFunc

	// Payment endpoints
	FindPayment() echo.HandlerFunc
	FindUserPayments() echo.HandlerFunc
	FindSubscriptionPayments() echo.HandlerFunc

	// Access control endpoints
	GetUserAccess() echo.HandlerFunc

	// Webhook endpoints
	HotmartWebhook() echo.HandlerFunc
	ApplePayWebhook() echo.HandlerFunc
}

type controller struct {
	Service _billing.Service
}

func New(service _billing.Service) Controller {
	return &controller{
		Service: service,
	}
}

func (bc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	// Use v2 API for billing endpoints
	billingGroup := currentGroup.Group("/billing")

	// Authentication middleware for all billing endpoints except webhooks
	authChain := []echo.MiddlewareFunc{
		middlewares.AuthGuard(),
		middlewares.UserContextMiddleware(),
	}

	// Admin-only endpoints for plan management
	adminChain := append(authChain, middlewares.RoleGuard(middlewares.RoleGuardConfig{
		RequiredRoles: []auth.Role{"admin"},
	}))

	// Plan management (admin only)
	plansGroup := billingGroup.Group("/plans", adminChain...)
	plansGroup.POST("", bc.CreatePlan())
	plansGroup.GET("", bc.FindAllPlans())
	plansGroup.GET("/active", bc.FindActivePlans())
	plansGroup.GET("/:id", bc.FindPlan())
	plansGroup.PUT("/:id", bc.UpdatePlan())
	plansGroup.DELETE("/:id", bc.DeletePlan())

	// Subscription management (user endpoints)
	subscriptionsGroup := billingGroup.Group("/subscriptions", authChain...)
	subscriptionsGroup.GET("/me", bc.FindUserSubscriptions())
	subscriptionsGroup.GET("/me/active", bc.FindActiveUserSubscriptions())
	subscriptionsGroup.GET("/:id", bc.FindSubscription())
	subscriptionsGroup.POST("/:id/cancel", bc.CancelSubscription())

	// Admin subscription management
	adminSubscriptionsGroup := billingGroup.Group("/admin/subscriptions", adminChain...)
	adminSubscriptionsGroup.POST("", bc.CreateSubscription()) // Admin-only for trial grants
	adminSubscriptionsGroup.POST("/:id/suspend", bc.SuspendSubscription())
	adminSubscriptionsGroup.POST("/:id/reactivate", bc.ReactivateSubscription())

	// Payment endpoints (user endpoints)
	paymentsGroup := billingGroup.Group("/payments", authChain...)
	paymentsGroup.GET("/me", bc.FindUserPayments())
	paymentsGroup.GET("/:id", bc.FindPayment())
	paymentsGroup.GET("/subscriptions/:subscriptionId", bc.FindSubscriptionPayments())

	// Access control endpoints
	accessGroup := billingGroup.Group("/access", authChain...)
	accessGroup.GET("/me", bc.GetUserAccess())

	// Webhook endpoints (no authentication required)
	webhooksGroup := billingGroup.Group("/webhooks")
	webhooksGroup.POST("/hotmart", bc.HotmartWebhook())
	webhooksGroup.POST("/applepay", bc.ApplePayWebhook())
}

// Helper functions
func (bc *controller) getUserIDFromContext(c echo.Context) (primitive.ObjectID, error) {
	userCtx, err := middlewares.GetUserContext(c)
	if err != nil {
		return primitive.NilObjectID, err
	}

	userID, err := primitive.ObjectIDFromHex(userCtx.UserID)
	if err != nil {
		return primitive.NilObjectID, errors.New(errors.Controller, "invalid user ID", errors.Validation, err)
	}

	return userID, nil
}

func (bc *controller) getObjectIDFromParam(c echo.Context, param string) (primitive.ObjectID, error) {
	idStr := c.Param(param)
	if idStr == "" {
		return primitive.NilObjectID, errors.New(errors.Controller, errors.Message(param+" is required"), errors.Validation, nil)
	}

	objectID, err := primitive.ObjectIDFromHex(idStr)
	if err != nil {
		return primitive.NilObjectID, errors.New(errors.Controller, errors.Message("invalid "+param), errors.Validation, err)
	}

	return objectID, nil
}

func (bc *controller) validateRequest(c echo.Context, req interface{}) error {
	if err := c.Bind(req); err != nil {
		return errors.New(errors.Controller, "invalid request body", errors.Validation, err)
	}

	if err := c.Validate(req); err != nil {
		return errors.New(errors.Controller, "validation failed", errors.Validation, err)
	}

	return nil
}

// Plan endpoints
func (bc *controller) CreatePlan() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var req CreatePlanRequest
		if err := bc.validateRequest(c, &req); err != nil {
			return err
		}

		plan := &billing.Plan{
			Name:             req.Name,
			Description:      req.Description,
			Price:            req.Price,
			Currency:         req.Currency,
			Features:         req.Features,
			HotmartProductID: req.HotmartProductID,
			AppleProductID:   req.AppleProductID,
			DurationMonths:   req.DurationMonths,
			TrialDays:        req.TrialDays,
			AutoRenew:        req.AutoRenew,
			Status:           billing.PlanStatus(req.Status),
		}

		planID, err := bc.Service.CreatePlan(ctx, plan)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, CreateResponse{ID: planID})
	}
}

func (bc *controller) FindPlan() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		planID, err := bc.getObjectIDFromParam(c, "id")
		if err != nil {
			return err
		}

		plan, err := bc.Service.FindPlan(ctx, planID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, PlanToResponse(plan))
	}
}

func (bc *controller) FindAllPlans() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		plans, err := bc.Service.FindAllPlans(ctx)
		if err != nil {
			return err
		}

		responses := make([]*PlanResponse, len(plans))
		for i, plan := range plans {
			responses[i] = PlanToResponse(plan)
		}

		return c.JSON(http.StatusOK, responses)
	}
}

func (bc *controller) FindActivePlans() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		plans, err := bc.Service.FindActivePlans(ctx)
		if err != nil {
			return err
		}

		responses := make([]*PlanResponse, len(plans))
		for i, plan := range plans {
			responses[i] = PlanToResponse(plan)
		}

		return c.JSON(http.StatusOK, responses)
	}
}

func (bc *controller) UpdatePlan() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		planID, err := bc.getObjectIDFromParam(c, "id")
		if err != nil {
			return err
		}

		var req UpdatePlanRequest
		if err := bc.validateRequest(c, &req); err != nil {
			return err
		}

		plan := &billing.Plan{
			ObjectID:         planID,
			Name:             req.Name,
			Description:      req.Description,
			Price:            req.Price,
			Currency:         req.Currency,
			Features:         req.Features,
			HotmartProductID: req.HotmartProductID,
			AppleProductID:   req.AppleProductID,
			DurationMonths:   req.DurationMonths,
			TrialDays:        req.TrialDays,
			AutoRenew:        req.AutoRenew,
			Status:           billing.PlanStatus(req.Status),
		}

		if err := bc.Service.UpdatePlan(ctx, plan); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, MessageResponse{Message: "Plan updated successfully"})
	}
}

func (bc *controller) DeletePlan() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		planID, err := bc.getObjectIDFromParam(c, "id")
		if err != nil {
			return err
		}

		if err := bc.Service.DeletePlan(ctx, planID); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, MessageResponse{Message: "Plan deleted successfully"})
	}
}

// Subscription endpoints
func (bc *controller) CreateSubscription() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userID, err := bc.getUserIDFromContext(c)
		if err != nil {
			return err
		}

		var req CreateSubscriptionRequest
		if err := bc.validateRequest(c, &req); err != nil {
			return err
		}

		planID, err := primitive.ObjectIDFromHex(req.PlanID)
		if err != nil {
			return errors.New(errors.Controller, "invalid plan ID", errors.Validation, err)
		}

		provider := billing.PaymentProvider(req.Provider)
		if !provider.IsValid() {
			return errors.New(errors.Controller, "invalid payment provider", errors.Validation, nil)
		}

		subscription, err := bc.Service.CreateSubscription(ctx, userID, planID, provider)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, SubscriptionToResponse(subscription))
	}
}

func (bc *controller) FindSubscription() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userID, err := bc.getUserIDFromContext(c)
		if err != nil {
			return err
		}

		subscriptionID, err := bc.getObjectIDFromParam(c, "id")
		if err != nil {
			return err
		}

		subscription, err := bc.Service.FindSubscription(ctx, subscriptionID)
		if err != nil {
			return err
		}

		// Check if user owns this subscription
		if subscription.UserID != userID {
			return errors.New(errors.Controller, "subscription not found", errors.NotFound, nil)
		}

		return c.JSON(http.StatusOK, SubscriptionToResponse(subscription))
	}
}

func (bc *controller) FindUserSubscriptions() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userID, err := bc.getUserIDFromContext(c)
		if err != nil {
			return err
		}

		subscriptions, err := bc.Service.FindUserSubscriptions(ctx, userID)
		if err != nil {
			return err
		}

		responses := make([]*SubscriptionResponse, len(subscriptions))
		for i, subscription := range subscriptions {
			responses[i] = SubscriptionToResponse(subscription)
		}

		return c.JSON(http.StatusOK, responses)
	}
}

func (bc *controller) FindActiveUserSubscriptions() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userID, err := bc.getUserIDFromContext(c)
		if err != nil {
			return err
		}

		subscriptions, err := bc.Service.FindActiveUserSubscriptions(ctx, userID)
		if err != nil {
			return err
		}

		responses := make([]*SubscriptionResponse, len(subscriptions))
		for i, subscription := range subscriptions {
			responses[i] = SubscriptionToResponse(subscription)
		}

		return c.JSON(http.StatusOK, responses)
	}
}

func (bc *controller) CancelSubscription() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userID, err := bc.getUserIDFromContext(c)
		if err != nil {
			return err
		}

		subscriptionID, err := bc.getObjectIDFromParam(c, "id")
		if err != nil {
			return err
		}

		var req CancelSubscriptionRequest
		if err := bc.validateRequest(c, &req); err != nil {
			return err
		}

		// Verify user owns this subscription
		subscription, err := bc.Service.FindSubscription(ctx, subscriptionID)
		if err != nil {
			return err
		}

		if subscription.UserID != userID {
			return errors.New(errors.Controller, "subscription not found", errors.NotFound, nil)
		}

		// Let the service handle the logic based on trial status
		if err := bc.Service.CancelSubscription(ctx, subscriptionID, req.Reason); err != nil {
			return err
		}

		// Return appropriate message based on trial status
		if subscription.IsInTrial() {
			return c.JSON(http.StatusOK, MessageResponse{Message: "Subscription cancelled successfully - access revoked immediately"})
		} else {
			return c.JSON(http.StatusOK, MessageResponse{Message: "Subscription cancelled successfully - access retained until end date"})
		}
	}
}

func (bc *controller) SuspendSubscription() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		subscriptionID, err := bc.getObjectIDFromParam(c, "id")
		if err != nil {
			return err
		}

		if err := bc.Service.SuspendSubscription(ctx, subscriptionID); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, MessageResponse{Message: "Subscription suspended successfully"})
	}
}

func (bc *controller) ReactivateSubscription() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		subscriptionID, err := bc.getObjectIDFromParam(c, "id")
		if err != nil {
			return err
		}

		if err := bc.Service.ReactivateSubscription(ctx, subscriptionID); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, MessageResponse{Message: "Subscription reactivated successfully"})
	}
}

// Payment endpoints
func (bc *controller) FindPayment() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userID, err := bc.getUserIDFromContext(c)
		if err != nil {
			return err
		}

		paymentID, err := bc.getObjectIDFromParam(c, "id")
		if err != nil {
			return err
		}

		payment, err := bc.Service.FindPayment(ctx, paymentID)
		if err != nil {
			return err
		}

		// Check if user owns this payment
		if payment.UserID != userID {
			return errors.New(errors.Controller, "payment not found", errors.NotFound, nil)
		}

		return c.JSON(http.StatusOK, PaymentToResponse(payment))
	}
}

func (bc *controller) FindUserPayments() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userID, err := bc.getUserIDFromContext(c)
		if err != nil {
			return err
		}

		payments, err := bc.Service.FindUserPayments(ctx, userID)
		if err != nil {
			return err
		}

		responses := make([]*PaymentResponse, len(payments))
		for i, payment := range payments {
			responses[i] = PaymentToResponse(payment)
		}

		return c.JSON(http.StatusOK, responses)
	}
}

func (bc *controller) FindSubscriptionPayments() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userID, err := bc.getUserIDFromContext(c)
		if err != nil {
			return err
		}

		subscriptionID, err := bc.getObjectIDFromParam(c, "subscriptionId")
		if err != nil {
			return err
		}

		// Verify user owns this subscription
		subscription, err := bc.Service.FindSubscription(ctx, subscriptionID)
		if err != nil {
			return err
		}

		if subscription.UserID != userID {
			return errors.New(errors.Controller, "subscription not found", errors.NotFound, nil)
		}

		payments, err := bc.Service.FindSubscriptionPayments(ctx, subscriptionID)
		if err != nil {
			return err
		}

		responses := make([]*PaymentResponse, len(payments))
		for i, payment := range payments {
			responses[i] = PaymentToResponse(payment)
		}

		return c.JSON(http.StatusOK, responses)
	}
}

// Access control endpoints
func (bc *controller) GetUserAccess() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userID, err := bc.getUserIDFromContext(c)
		if err != nil {
			return err
		}

		hasActiveSubscription, err := bc.Service.HasActiveSubscription(ctx, userID)
		if err != nil {
			return err
		}

		features, err := bc.Service.GetUserFeatures(ctx, userID)
		if err != nil {
			return err
		}

		response := &UserAccessResponse{
			HasActiveSubscription: hasActiveSubscription,
			Features:              features,
		}

		return c.JSON(http.StatusOK, response)
	}
}

// Webhook endpoints
func (bc *controller) HotmartWebhook() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		hottok := c.Request().Header.Get("X-HOTMART-HOTTOK")
		expectedToken := os.Getenv("HOTMART_HOTTOK")

		// 2. Valida o token
		if hottok == "" || hottok != expectedToken {
			return c.JSON(http.StatusUnauthorized, MessageResponse{Message: "unauthorized webhook"})
		}

		body, err := io.ReadAll(c.Request().Body)
		if err != nil {
			return c.JSON(http.StatusBadRequest, MessageResponse{Message: "error by reading body"})
		}

		if len(body) == 0 {
			return c.JSON(http.StatusBadRequest, MessageResponse{Message: "empty body"})
		}

		var payload map[string]interface{}
		if err := json.Unmarshal(body, &payload); err != nil {
			return c.JSON(http.StatusBadRequest, MessageResponse{Message: "invalid json"})
		}

		if err := bc.Service.ProcessHotmartWebhook(ctx, payload); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, MessageResponse{Message: "webhook processed successfully"})
	}
}

func (bc *controller) ApplePayWebhook() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var req ApplePayWebhookRequest
		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid apple pay webhook data", errors.Validation, err)
		}

		// Store the full request body for processing
		req.Data = make(map[string]interface{})
		if err := c.Bind(&req.Data); err != nil {
			// If we can't bind to map, use the struct data
			req.Data = map[string]interface{}{
				"notificationType":      req.NotificationType,
				"transactionId":         req.TransactionID,
				"originalTransactionId": req.OriginalTransactionID,
				"productId":             req.ProductID,
				"purchaseDate":          req.PurchaseDate,
				"expiresDate":           req.ExpiresDate,
				"environment":           req.Environment,
				"status":                req.Status,
				"amount":                req.Amount,
				"currency":              req.Currency,
			}
		}

		if err := bc.Service.ProcessApplePayWebhook(ctx, req.Data); err != nil {
			// Log error but return success to prevent webhook retries for invalid data
			return c.JSON(http.StatusOK, MessageResponse{Message: "Webhook processed"})
		}

		return c.JSON(http.StatusOK, MessageResponse{Message: "Webhook processed successfully"})
	}
}
