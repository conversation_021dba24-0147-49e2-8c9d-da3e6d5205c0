# Financial Map Dashboard

## Overview

The Financial Map Dashboard is a comprehensive financial overview system that provides users with a unified view of their financial health. It aggregates data from multiple sources to create a complete picture of income, investments, assets, and emergency funds, with historical tracking through automated monthly snapshots.

## Architecture

The Financial Map follows a layered architecture pattern:

```
┌─────────────────┐
│   Controller    │ ← HTTP API endpoints
├─────────────────┤
│    Service      │ ← Business logic & orchestration
├─────────────────┤
│   Repository    │ ← Data persistence layer
├─────────────────┤
│     Model       │ ← Data structures & validation
└─────────────────┘
```

### Key Components

- **FinancialMap**: Main DTO containing all dashboard data
- **Income Sources**: Aggregated from financial sheet transactions
- **Emergency Fund**: User's emergency savings tracking
- **Investments**: Investment portfolio management
- **Assets**: Physical asset tracking
- **Net Worth Snapshots**: Historical data points for charts

## Core Features

### 1. Hybrid Data Approach

The dashboard uses a sophisticated hybrid approach for chart data:

- **Historical Data**: Stored monthly snapshots (up to 11 months)
- **Live Data**: Real-time calculations from current user data
- **Combined View**: 12-month evolution chart with historical + current data

### 2. Income Source Integration

Income sources are automatically aggregated from financial sheet transactions:

- Tracks latest transaction per money source
- Includes category icons and background colors
- Filters by `CategoryTypeIncome` constant
- Updates monthly amounts based on most recent transactions

### 3. Automated Snapshots

Monthly snapshots are created automatically via background scheduler:

- Runs on the first day of each month at midnight
- Captures point-in-time financial state for all users
- Enables historical trend analysis
- Preserves data integrity for charts

## Database Design

### Unified Collection Approach

The dashboard system uses a **single MongoDB collection** (`dashboard.financialmap`) for optimal performance:

```javascript
// Collection: dashboard.financialmap
{
  "_id": ObjectId,
  "userID": "string",
  "monthlyIncome": 5000,
  "strategicFund": {
    "userID": "string",
    "currentValue": 10000,
    "goalValue": 50000,
    "createdAt": ISODate,
    "updatedAt": ISODate
  },
  "totalInvestments": 25000,
  "totalAssets": 80000,
  "netWorthHistory": [
    {
      "userID": "string",
      "date": ISODate,
      "strategicFundValue": 10000,
      "investmentsValue": 25000,
      "assetsValue": 80000,
      "totalValue": 115000,
      "createdAt": ISODate
    }
  ],
  "incomeSources": [
    {
      "userID": "string",
      "name": "Salary",
      "monthlyAmount": 5000,
      "moneySource": 1,
      "icon": "salary",
      "backgroundColor": "#4CAF50",
      "lastUpdated": ISODate,
      "createdAt": ISODate,
      "updatedAt": ISODate
    }
  ],
  "investments": [
    {
      "userID": "string",
      "name": "Stock Portfolio",
      "currentValue": 25000,
      "createdAt": ISODate,
      "updatedAt": ISODate
    }
  ],
  "assets": [
    {
      "userID": "string",
      "description": "Real Estate",
      "value": 80000,
      "createdAt": ISODate,
      "updatedAt": ISODate
    }
  ],
  "createdAt": ISODate,
  "updatedAt": ISODate
}
```

### Benefits of Unified Collection

1. **Performance**: Single database query instead of 5 separate queries
2. **Consistency**: Atomic updates across all financial components
3. **Simplicity**: Reduced complexity in data management
4. **Scalability**: Better MongoDB performance with fewer collections

### Specialized Queries

While using a unified collection, the system still supports specialized operations:

- `FindIncomeSourceFromMap()`: Finds specific income sources within the document
- `FindInvestmentFromMap()`: Locates investments by ID
- `FindAssetFromMap()`: Retrieves specific assets
- Component-level CRUD operations work within the unified document structure

## API Endpoints

> **Note**: All endpoints now work with the unified collection approach. Individual component operations (income sources, investments, assets) are performed within the context of the unified FinancialMap document, ensuring data consistency and optimal performance.

### Main Dashboard

```http
GET /v2/dashboard/financialmaps/me
```

Returns complete financial map with all dashboard data from the unified collection.

**Response:**
```json
{
  "userID": "string",
  "monthlyIncome": 5000,
  "strategicFund": {
    "currentValue": 10000,
    "goalValue": 50000
  },
  "totalInvestments": 25000,
  "totalAssets": 80000,
  "netWorthHistory": [
    {
      "date": "2024-01-01T00:00:00Z",
      "strategicFundValue": 8000,
      "investmentsValue": 20000,
      "assetsValue": 80000,
      "totalValue": 108000
    }
  ],
  "incomeSources": [...],
  "investments": [...],
  "assets": [...]
}
```

### Income Sources

```http
POST   /v2/dashboard/financialmaps/me/incomes
GET    /v2/dashboard/financialmaps/me/incomes
PUT    /v2/dashboard/financialmaps/me/incomes/:id
DELETE /v2/dashboard/financialmaps/me/incomes/:id
```

**Create Request:**
```json
{
  "name": "Salary CLT",
  "monthlyAmount": 5000,
  "moneySource": 1
}
```

### Emergency Fund

```http
GET /v2/dashboard/financialmaps/me/strategicfunds
PUT /v2/dashboard/financialmaps/me/strategicfunds
PUT /v2/dashboard/financialmaps/me/strategicfunds/goal
```

**Update Request:**
```json
{
  "currentValue": 15000
}
```

**Update Goal Request:**
```json
{
  "goalValue": 60000
}
```

### Investments

```http
POST   /v2/dashboard/financialmaps/me/investments
GET    /v2/dashboard/financialmaps/me/investments
PUT    /v2/dashboard/financialmaps/me/investments/:id
DELETE /v2/dashboard/financialmaps/me/investments/:id
```

**Create Request:**
```json
{
  "name": "Tesouro Selic",
  "currentValue": 25000
}
```

### Assets

```http
POST   /v2/dashboard/financialmaps/me/assets
GET    /v2/dashboard/financialmaps/me/assets
PUT    /v2/dashboard/financialmaps/me/assets/:id
DELETE /v2/dashboard/financialmaps/me/assets/:id
```

**Create Request:**
```json
{
  "description": "Honda Civic 2020",
  "value": 80000
}
```

### Net Worth History

```http
GET /v2/dashboard/financialmaps/me/snapshots?limit=12
```

Returns historical net worth snapshots with optional limit parameter.

## Data Models

### FinancialMap

Main dashboard DTO containing aggregated financial data.

```go
type FinancialMap struct {
    UserID           string               `json:"userID"`
    MonthlyIncome    monetary.Amount      `json:"monthlyIncome"`
    StrategicFund    *StrategicFund       `json:"strategicFund"`
    TotalInvestments monetary.Amount      `json:"totalInvestments"`
    TotalAssets      monetary.Amount      `json:"totalAssets"`
    NetWorthHistory  []NetWorthSnapshot   `json:"netWorthHistory"`
    IncomeSources    []IncomeSource       `json:"incomeSources"`
    Investments      []Investment         `json:"investments"`
    Assets           []Asset              `json:"assets"`
}
```

### IncomeSource

Represents a source of monthly income.

```go
type IncomeSource struct {
    ID              string                     `json:"id"`
    UserID          string                     `json:"userID"`
    Name            string                     `json:"name"`
    MonthlyAmount   monetary.Amount            `json:"monthlyAmount"`
    MoneySource     financialsheet.MoneySource `json:"moneySource"`
    Icon            financialsheet.CategoryIcon `json:"icon"`
    BackgroundColor string                     `json:"backgroundColor"`
    LastUpdated     time.Time                  `json:"lastUpdated"`
}
```

### StrategicFund

Tracks emergency fund progress.

```go
type StrategicFund struct {
    ID           string          `json:"id"`
    UserID       string          `json:"userID"`
    CurrentValue monetary.Amount `json:"currentValue"`
    GoalValue    monetary.Amount `json:"goalValue"`
}
```

### Investment

Represents an investment holding.

```go
type Investment struct {
    ID           string          `json:"id"`
    UserID       string          `json:"userID"`
    Name         string          `json:"name"`
    CurrentValue monetary.Amount `json:"currentValue"`
}
```

### Asset

Represents a physical asset.

```go
type Asset struct {
    ID          string          `json:"id"`
    UserID      string          `json:"userID"`
    Description string          `json:"description"`
    Value       monetary.Amount `json:"value"`
}
```

### NetWorthSnapshot

Historical point-in-time financial data.

```go
type NetWorthSnapshot struct {
    ID                 string          `json:"id"`
    UserID             string          `json:"userID"`
    Date               time.Time       `json:"date"`
    StrategicFundValue monetary.Amount `json:"strategicFundValue"`
    InvestmentsValue   monetary.Amount `json:"investmentsValue"`
    AssetsValue        monetary.Amount `json:"assetsValue"`
    TotalValue         monetary.Amount `json:"totalValue"`
}
```

## Business Logic

### FindFinancialMap Algorithm

The core dashboard method implements a sophisticated data aggregation strategy:

1. **Concurrent Data Fetching**
   - Historical snapshots (last 11 months)
   - Current income sources from financial sheet
   - Current emergency fund
   - Current investments
   - Current assets

2. **Live Calculations**
   - Sum monthly income from all sources
   - Sum total investment values
   - Sum total asset values
   - Get current emergency fund value

3. **Dynamic Snapshot Creation**
   - Create current month snapshot from live data
   - Calculate total net worth
   - Set current date

4. **Data Combination**
   - Merge historical snapshots with current snapshot
   - Convert pointer slices to value slices for JSON serialization
   - Return complete FinancialMap

### Income Source Aggregation

Income sources are derived from financial sheet transactions:

1. Fetch all income transactions for current month
2. Group by money source (keep latest transaction per source)
3. Extract category information (name, icon, background color)
4. Create IncomeSource objects with aggregated data

## Background Jobs

### Monthly Snapshot Scheduler

Automated system for creating historical data points:

- **Schedule**: First day of each month at midnight
- **Process**: Iterate through all users and create snapshots
- **Data**: Captures current state of emergency fund, investments, and assets
- **Storage**: Permanent records in `dashboard.net_worth_snapshots` collection

### Scheduler Implementation

```go
// Starts scheduler with intelligent timing
func (s *scheduler) Start(ctx context.Context) error {
    // Calculate time until next month
    now := time.Now()
    nextMonth := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, now.Location())
    
    // Wait until first day of next month, then run monthly
    // Creates snapshots for all users
}
```

## Database Schema

### Collections

- `dashboard.income_sources` - User income sources
- `dashboard.emergency_funds` - Emergency fund data (unique per user)
- `dashboard.investments` - Investment holdings
- `dashboard.assets` - Physical assets
- `dashboard.net_worth_snapshots` - Historical snapshots

### Indexes

```javascript
// Income sources
db.dashboard.income_sources.createIndex({ "userID": 1 })

// Emergency funds (unique per user)
db.dashboard.emergency_funds.createIndex({ "userID": 1 }, { unique: true })

// Investments
db.dashboard.investments.createIndex({ "userID": 1 })

// Assets
db.dashboard.assets.createIndex({ "userID": 1 })

// Net worth snapshots (optimized for history queries)
db.dashboard.net_worth_snapshots.createIndex({ "userID": 1, "date": -1 })
```

## Security

### Authentication

All endpoints require JWT authentication via `AuthGuard()` middleware.

### Authorization

- Users can only access their own financial data
- User ID extracted from JWT token
- All queries filtered by authenticated user ID

### Data Validation

- Input validation on all create/update operations
- Monetary amounts must be non-negative
- Required fields validation
- String trimming and sanitization

## Performance Considerations

### Optimizations

1. **Concurrent Data Fetching**: Uses goroutines for parallel database queries
2. **Efficient Indexing**: Optimized MongoDB indexes for fast lookups
3. **Limited History**: Snapshots limited to prevent excessive data transfer
4. **Projection Queries**: Only fetch necessary fields from database

### Caching Strategy

- Live data is calculated on-demand (always current)
- Historical data is cached in snapshots (updated monthly)
- No additional caching layer needed due to hybrid approach

## Error Handling

### Domain Errors

The system uses structured error handling with domain-specific error types:

- `errors.Validation` - Input validation failures
- `errors.NotFound` - Resource not found
- `errors.Internal` - System errors

### HTTP Status Codes

- `200 OK` - Successful retrieval
- `201 Created` - Successful creation
- `204 No Content` - Successful update/deletion
- `400 Bad Request` - Validation errors
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - System errors

## Testing

### Test Coverage

- **Model Tests**: Validation logic and data preparation
- **Repository Tests**: Database operations and error handling
- **Service Tests**: Business logic with mocked dependencies
- **Controller Tests**: HTTP handlers with mocked services

### Test Patterns

```go
// Model validation tests
func TestIncomeSource_PrepareCreate(t *testing.T) {
    // Test valid and invalid input scenarios
}

// Repository integration tests
func TestDashboardRepositoryTestSuite(t *testing.T) {
    // Full CRUD operations with real MongoDB
}

// Service unit tests with mocks
func TestDashboardServiceTestSuite(t *testing.T) {
    // Business logic with mocked dependencies
}

// Controller HTTP tests
func TestDashboardControllerTestSuite(t *testing.T) {
    // HTTP handlers with mocked services
}
```

## Deployment

### Environment Variables

No additional environment variables required. Uses existing MongoDB connection.

### Dependencies

- MongoDB for data persistence
- Existing financial sheet service for income aggregation
- JWT authentication system

### Monitoring

- Scheduler logs monthly snapshot creation results
- Standard HTTP access logs for API endpoints
- Database query performance monitoring via MongoDB tools

## Future Enhancements

### Potential Features

1. **Custom Categories**: Allow users to create custom income/expense categories
2. **Goal Tracking**: Enhanced goal setting and progress tracking
3. **Notifications**: Alerts for goal achievements or budget thresholds
4. **Export Features**: PDF/Excel export of financial reports
5. **Comparative Analysis**: Year-over-year comparisons
6. **Forecasting**: Predictive analytics based on historical trends

### Scalability Considerations

1. **Data Archiving**: Archive old snapshots for long-term users
2. **Caching Layer**: Add Redis for frequently accessed data
3. **Read Replicas**: Use MongoDB read replicas for heavy read operations
4. **Microservice Split**: Separate snapshot service for independent scaling
