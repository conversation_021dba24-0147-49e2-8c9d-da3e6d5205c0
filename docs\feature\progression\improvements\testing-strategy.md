# Testing Strategy for Event-Driven Progression System

## Overview

This document outlines a comprehensive testing strategy for the progression system refactor, ensuring data integrity, API compatibility, and performance validation during the migration from a complex nested model to an event-driven architecture.

## Testing Objectives

### Primary Goals

1. **Zero Data Loss**: All user progression data preserved during migration
2. **API Compatibility**: All existing endpoints return identical responses
3. **Performance Validation**: New system meets or exceeds performance benchmarks
4. **Business Logic Preservation**: All progression rules and calculations unchanged
5. **Rollback Safety**: Ability to revert changes without data corruption

### Success Criteria

- **100% API compatibility** (all existing tests pass)
- **Data integrity verification** for all migrated users
- **Performance improvements** match documented benchmarks
- **Zero production incidents** during rollout

## Testing Phases

### Phase 1: Unit Testing (Week 1-2)

#### 1.1 Event Model Testing

**Event Validation Tests**:
```go
func TestProgressEvent_Validate(t *testing.T) {
    tests := []struct {
        name    string
        event   ProgressEvent
        wantErr bool
        errType error
    }{
        {
            name: "valid lesson event",
            event: ProgressEvent{
                UserID:    "user123",
                TrailID:   "trail456",
                ItemID:    "lesson789",
                ItemType:  ProgressTypeLesson,
                Action:    ActionCompleted,
                ContentID: "content001",
                Choice: &Choice{
                    Identifier: "choice001",
                    Next:       "COIN",
                },
            },
            wantErr: false,
        },
        {
            name: "missing required fields",
            event: ProgressEvent{
                UserID: "user123",
                // Missing required fields
            },
            wantErr: true,
            errType: ErrRequiredTrailValue,
        },
        // ... more test cases
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := tt.event.Validate()
            if (err != nil) != tt.wantErr {
                t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
            }
            if err != nil && tt.errType != nil {
                assert.Equal(t, tt.errType, err)
            }
        })
    }
}
```

**Event Creation Tests**:
```go
func TestProgressEvent_Creation(t *testing.T) {
    // Test event creation from legacy progression body
    body := &ProgressionBody{
        Trail:   "trail123",
        Module:  "lesson456",
        Content: "content789",
        Type:    "LESSON",
        Choice: &ModuleContentChoice{
            Identifier: "choice001",
            Next:       "COIN",
        },
        Timestamp: time.Now(),
    }
    
    event := CreateEventFromBody("user123", body)
    
    assert.Equal(t, "user123", event.UserID)
    assert.Equal(t, "trail123", event.TrailID)
    assert.Equal(t, "lesson456", event.ItemID)
    assert.Equal(t, ProgressTypeLesson, event.ItemType)
    assert.Equal(t, ActionCompleted, event.Action)
    assert.Equal(t, "content789", event.ContentID)
    assert.NotNil(t, event.Choice)
    assert.Equal(t, "choice001", event.Choice.Identifier)
    assert.Equal(t, "COIN", event.Choice.Next)
}
```

#### 1.2 Progress Calculator Testing

**Progress Calculation Tests**:
```go
func TestCalculator_CalculateProgressFromEvents(t *testing.T) {
    calculator := &Calculator{
        trailService: mockTrailService(),
    }
    
    events := []*ProgressEvent{
        {
            UserID:    "user123",
            TrailID:   "trail456",
            ItemID:    "lesson1",
            ItemType:  ProgressTypeLesson,
            Action:    ActionStarted,
            ContentID: "content1",
            Timestamp: time.Now().Add(-1 * time.Hour),
        },
        {
            UserID:    "user123",
            TrailID:   "trail456",
            ItemID:    "lesson1",
            ItemType:  ProgressTypeLesson,
            Action:    ActionCompleted,
            ContentID: "content2",
            Choice: &Choice{
                Identifier: "choice1",
                Next:       "COIN",
            },
            Timestamp: time.Now(),
        },
    }
    
    summary, err := calculator.CalculateProgressFromEvents(context.Background(), events, "user123")
    
    assert.NoError(t, err)
    assert.NotNil(t, summary)
    assert.Equal(t, "user123", summary.UserID)
    assert.Contains(t, summary.Trails, "trail456")
    
    trailSummary := summary.Trails["trail456"]
    assert.True(t, trailSummary.LessonsCompleted["lesson1"])
    assert.True(t, trailSummary.LessonProgress["lesson1"].Completed)
    assert.True(t, trailSummary.LessonProgress["lesson1"].Rewarded)
    assert.Equal(t, 1, trailSummary.TotalRewards)
}
```

**Legacy Calculation Compatibility Tests**:
```go
func TestCalculator_LegacyCompatibility(t *testing.T) {
    // Test that new calculations match legacy calculations exactly
    legacyProgression := createLegacyProgressionFixture()
    events := convertLegacyToEvents(legacyProgression)
    
    newSummary, err := calculator.CalculateProgressFromEvents(context.Background(), events, "user123")
    assert.NoError(t, err)
    
    legacyTrail := legacyProgression.Trails[0]
    newTrail := newSummary.Trails[legacyTrail.ID]
    
    // Verify identical calculations
    assert.Equal(t, int(legacyTrail.Total), newTrail.ProgressPercent)
    assert.Equal(t, legacyTrail.LessonsCompleted, len(newTrail.LessonsCompleted) >= 1)
    assert.Equal(t, legacyTrail.ChallengeCompleted, newTrail.IsCompleted)
    
    // Verify lesson progress
    for _, legacyLesson := range legacyTrail.Lessons {
        newLesson := newTrail.LessonProgress[legacyLesson.Identifier]
        assert.NotNil(t, newLesson)
        assert.Equal(t, legacyLesson.Completed, newLesson.Completed)
        assert.Equal(t, legacyLesson.Rewarded, newLesson.Rewarded)
        assert.Equal(t, legacyLesson.Current, newLesson.Current)
    }
}
```

#### 1.3 Repository Testing

**Event Storage Tests**:
```go
func TestEventRepository_CRUD(t *testing.T) {
    repo := setupTestRepository()
    
    event := &ProgressEvent{
        UserID:    "user123",
        TrailID:   "trail456",
        ItemID:    "lesson789",
        ItemType:  ProgressTypeLesson,
        Action:    ActionCompleted,
        ContentID: "content001",
        Timestamp: time.Now(),
    }
    
    // Test Create
    err := repo.CreateEvent(context.Background(), event)
    assert.NoError(t, err)
    assert.NotEmpty(t, event.ID)
    
    // Test Read
    events, err := repo.GetUserEvents(context.Background(), "user123", 10)
    assert.NoError(t, err)
    assert.Len(t, events, 1)
    assert.Equal(t, event.UserID, events[0].UserID)
    
    // Test Query by Trail
    trailEvents, err := repo.GetTrailEvents(context.Background(), "user123", "trail456")
    assert.NoError(t, err)
    assert.Len(t, trailEvents, 1)
}
```

**Cache Testing**:
```go
func TestRepository_CacheOperations(t *testing.T) {
    repo := setupTestRepositoryWithCache()
    
    summary := &ProgressSummary{
        UserID: "user123",
        Trails: map[string]*TrailSummary{
            "trail456": {
                ID:              "trail456",
                ProgressPercent: 75,
                IsCompleted:     false,
            },
        },
        LastUpdated: time.Now(),
    }
    
    // Test Cache Save
    err := repo.SaveProgressSummary(context.Background(), summary)
    assert.NoError(t, err)
    
    // Test Cache Retrieve
    cached, err := repo.GetProgressSummary(context.Background(), "user123")
    assert.NoError(t, err)
    assert.NotNil(t, cached)
    assert.Equal(t, summary.UserID, cached.UserID)
    assert.Equal(t, summary.Trails["trail456"].ProgressPercent, cached.Trails["trail456"].ProgressPercent)
    
    // Test Cache Invalidation
    err = repo.InvalidateProgressSummary(context.Background(), "user123")
    assert.NoError(t, err)
    
    cached, err = repo.GetProgressSummary(context.Background(), "user123")
    assert.Error(t, err) // Should be cache miss
}
```

### Phase 2: Integration Testing (Week 2-3)

#### 2.1 Service Layer Integration

**Event Service Integration Tests**:
```go
func TestEventService_Integration(t *testing.T) {
    service := setupIntegrationTestService()
    
    // Test complete lesson flow
    body := &ProgressionBody{
        Trail:   "trail123",
        Module:  "lesson456",
        Content: "content789",
        Type:    "LESSON",
        Choice: &ModuleContentChoice{
            Identifier: "choice001",
            Next:       "COIN",
        },
        Timestamp: time.Now(),
    }
    
    err := service.CreateLesson(context.Background(), "user123", body)
    assert.NoError(t, err)
    
    // Verify event was created
    events, err := service.repository.GetUserEvents(context.Background(), "user123", 10)
    assert.NoError(t, err)
    assert.Len(t, events, 1)
    
    // Verify progress summary updated
    summary, err := service.GetUserProgress(context.Background(), "user123")
    assert.NoError(t, err)
    assert.Contains(t, summary.Trails, "trail123")
    
    // Verify rewards processed
    vault, err := service.vaultService.GetUserVault(context.Background(), "user123")
    assert.NoError(t, err)
    assert.Greater(t, vault.Coins, 0)
}
```

**Migration Service Integration**:
```go
func TestMigrationService_Integration(t *testing.T) {
    migration := setupMigrationService()
    
    // Create legacy progression data
    legacyProgression := createComplexLegacyProgression()
    err := migration.repository.CreateLegacyProgression(context.Background(), legacyProgression)
    assert.NoError(t, err)
    
    // Migrate user data
    err = migration.MigrateUserData(context.Background(), legacyProgression.User)
    assert.NoError(t, err)
    
    // Verify events were created
    events, err := migration.repository.GetUserEvents(context.Background(), legacyProgression.User, 0)
    assert.NoError(t, err)
    assert.Greater(t, len(events), 0)
    
    // Verify migrated flag set
    migrated, err := migration.repository.IsUserMigrated(context.Background(), legacyProgression.User)
    assert.NoError(t, err)
    assert.True(t, migrated)
    
    // Verify calculated progress matches legacy
    summary, err := migration.calculator.CalculateProgressFromEvents(context.Background(), events, legacyProgression.User)
    assert.NoError(t, err)
    
    for _, legacyTrail := range legacyProgression.Trails {
        newTrail := summary.Trails[legacyTrail.ID]
        assert.NotNil(t, newTrail)
        assert.Equal(t, int(legacyTrail.Total), newTrail.ProgressPercent)
    }
}
```

#### 2.2 API Compatibility Testing

**Controller Response Compatibility**:
```go
func TestController_APICompatibility(t *testing.T) {
    // Setup both legacy and new systems
    legacyController := setupLegacyController()
    newController := setupNewEventController()
    
    // Test data
    requestBody := map[string]interface{}{
        "trail":   "trail123",
        "lesson":  "lesson456",
        "content": "content789",
        "type":    "LESSON",
        "choice": map[string]interface{}{
            "identifier": "choice001",
            "next":       "COIN",
        },
        "timestamp": time.Now().Format(time.RFC3339),
    }
    
    // Create identical requests
    legacyReq := createTestRequest("POST", "/api/v1/progress/trail123/register/lesson456/", requestBody)
    newReq := createTestRequest("POST", "/api/v1/progress/trail123/register/lesson456/", requestBody)
    
    // Execute requests
    legacyResp := executeRequest(legacyController, legacyReq)
    newResp := executeRequest(newController, newReq)
    
    // Verify identical responses
    assert.Equal(t, legacyResp.Code, newResp.Code)
    assert.Equal(t, legacyResp.Header(), newResp.Header())
    assert.Equal(t, legacyResp.Body.String(), newResp.Body.String())
}
```

**Card Endpoint Compatibility**:
```go
func TestCardEndpoints_Compatibility(t *testing.T) {
    // Test all card endpoints return identical data
    endpoints := []string{
        "/api/v1/progress/getTrailCards/",
        "/api/v1/progress/getLessonCards/",
        "/api/v1/progress/getChallengePhaseCards/",
    }
    
    for _, endpoint := range endpoints {
        t.Run(endpoint, func(t *testing.T) {
            legacyResp := callLegacyEndpoint(endpoint, testRequestBody)
            newResp := callNewEndpoint(endpoint, testRequestBody)
            
            // Parse JSON responses
            var legacyData, newData interface{}
            json.Unmarshal(legacyResp.Body.Bytes(), &legacyData)
            json.Unmarshal(newResp.Body.Bytes(), &newData)
            
            // Deep comparison
            assert.Equal(t, legacyData, newData)
        })
    }
}
```

### Phase 3: Migration Testing (Week 3-4)

#### 3.1 Data Migration Validation

**Complete Migration Test**:
```go
func TestCompleteMigration(t *testing.T) {
    // Setup test database with legacy data
    db := setupTestDatabase()
    seedLegacyProgressions(db, 1000) // 1000 test users
    
    migration := setupMigrationService(db)
    
    // Migrate all users
    users := getAllTestUsers(db)
    for _, userID := range users {
        err := migration.MigrateUserData(context.Background(), userID)
        assert.NoError(t, err, "Migration failed for user %s", userID)
    }
    
    // Validate migration success
    for _, userID := range users {
        // Verify migration flag
        migrated, err := migration.repository.IsUserMigrated(context.Background(), userID)
        assert.NoError(t, err)
        assert.True(t, migrated, "User %s not marked as migrated", userID)
        
        // Verify events created
        events, err := migration.repository.GetUserEvents(context.Background(), userID, 0)
        assert.NoError(t, err)
        assert.Greater(t, len(events), 0, "No events created for user %s", userID)
        
        // Verify data consistency
        validateUserDataConsistency(t, userID, migration)
    }
}

func validateUserDataConsistency(t *testing.T, userID string, migration *MigrationService) {
    // Get legacy data
    legacyProgression, err := migration.repository.GetLegacyProgression(context.Background(), userID)
    assert.NoError(t, err)
    
    // Get migrated data
    events, err := migration.repository.GetUserEvents(context.Background(), userID, 0)
    assert.NoError(t, err)
    
    summary, err := migration.calculator.CalculateProgressFromEvents(context.Background(), events, userID)
    assert.NoError(t, err)
    
    // Compare critical data points
    for _, legacyTrail := range legacyProgression.Trails {
        newTrail := summary.Trails[legacyTrail.ID]
        assert.NotNil(t, newTrail, "Trail %s missing after migration", legacyTrail.ID)
        
        // Progress percentage should match
        assert.Equal(t, int(legacyTrail.Total), newTrail.ProgressPercent, 
            "Progress percentage mismatch for trail %s", legacyTrail.ID)
        
        // Completion status should match
        assert.Equal(t, legacyTrail.LessonsCompleted, len(newTrail.LessonsCompleted) > 0,
            "Lessons completion status mismatch for trail %s", legacyTrail.ID)
        
        // Individual lesson status
        for _, legacyLesson := range legacyTrail.Lessons {
            newLesson := newTrail.LessonProgress[legacyLesson.Identifier]
            assert.NotNil(t, newLesson, "Lesson %s missing after migration", legacyLesson.Identifier)
            assert.Equal(t, legacyLesson.Completed, newLesson.Completed,
                "Lesson completion mismatch for %s", legacyLesson.Identifier)
            assert.Equal(t, legacyLesson.Rewarded, newLesson.Rewarded,
                "Lesson reward mismatch for %s", legacyLesson.Identifier)
        }
    }
}
```

#### 3.2 Rollback Testing

**Rollback Scenario Tests**:
```go
func TestMigrationRollback(t *testing.T) {
    // Setup migration environment
    migration := setupMigrationService()
    
    // Create test user with legacy data
    userID := "rollback_test_user"
    legacyProgression := createLegacyProgression(userID)
    err := migration.repository.CreateLegacyProgression(context.Background(), legacyProgression)
    assert.NoError(t, err)
    
    // Take backup of legacy data
    originalData := deepCopyProgression(legacyProgression)
    
    // Perform migration
    err = migration.MigrateUserData(context.Background(), userID)
    assert.NoError(t, err)
    
    // Verify migration occurred
    migrated, err := migration.repository.IsUserMigrated(context.Background(), userID)
    assert.NoError(t, err)
    assert.True(t, migrated)
    
    // Simulate rollback
    err = migration.RollbackUser(context.Background(), userID)
    assert.NoError(t, err)
    
    // Verify rollback success
    migrated, err = migration.repository.IsUserMigrated(context.Background(), userID)
    assert.NoError(t, err)
    assert.False(t, migrated)
    
    // Verify legacy data intact
    restoredData, err := migration.repository.GetLegacyProgression(context.Background(), userID)
    assert.NoError(t, err)
    assert.Equal(t, originalData, restoredData)
    
    // Verify events cleaned up
    events, err := migration.repository.GetUserEvents(context.Background(), userID, 0)
    assert.NoError(t, err)
    assert.Len(t, events, 0)
}
```

### Phase 4: Performance Testing (Week 4-5)

#### 4.1 Load Testing

**Write Performance Tests**:
```go
func TestWritePerformance(t *testing.T) {
    service := setupPerformanceTestService()
    
    // Test single write performance
    start := time.Now()
    body := createTestProgressionBody()
    err := service.CreateLesson(context.Background(), "user123", body)
    duration := time.Since(start)
    
    assert.NoError(t, err)
    assert.Less(t, duration, 50*time.Millisecond, "Write operation too slow")
    
    // Test concurrent writes
    concurrentUsers := 100
    writeLatencies := make([]time.Duration, concurrentUsers)
    var wg sync.WaitGroup
    
    for i := 0; i < concurrentUsers; i++ {
        wg.Add(1)
        go func(userIndex int) {
            defer wg.Done()
            
            start := time.Now()
            userID := fmt.Sprintf("user%d", userIndex)
            body := createTestProgressionBody()
            err := service.CreateLesson(context.Background(), userID, body)
            writeLatencies[userIndex] = time.Since(start)
            
            assert.NoError(t, err)
        }(i)
    }
    
    wg.Wait()
    
    // Analyze performance
    totalTime := time.Duration(0)
    maxTime := time.Duration(0)
    for _, latency := range writeLatencies {
        totalTime += latency
        if latency > maxTime {
            maxTime = latency
        }
    }
    
    avgTime := totalTime / time.Duration(concurrentUsers)
    assert.Less(t, avgTime, 100*time.Millisecond, "Average write time too high")
    assert.Less(t, maxTime, 500*time.Millisecond, "Max write time too high")
}
```

**Read Performance Tests**:
```go
func TestReadPerformance(t *testing.T) {
    service := setupPerformanceTestService()
    
    // Setup test data
    userID := "perf_test_user"
    setupUserProgressData(service, userID, 10) // 10 trails with progress
    
    // Test cache hit performance
    start := time.Now()
    summary, err := service.GetUserProgress(context.Background(), userID)
    cacheHitDuration := time.Since(start)
    
    assert.NoError(t, err)
    assert.NotNil(t, summary)
    assert.Less(t, cacheHitDuration, 10*time.Millisecond, "Cache hit too slow")
    
    // Test cache miss performance
    err = service.repository.InvalidateProgressSummary(context.Background(), userID)
    assert.NoError(t, err)
    
    start = time.Now()
    summary, err = service.GetUserProgress(context.Background(), userID)
    cacheMissDuration := time.Since(start)
    
    assert.NoError(t, err)
    assert.NotNil(t, summary)
    assert.Less(t, cacheMissDuration, 100*time.Millisecond, "Cache miss too slow")
    
    // Test card endpoint performance
    start = time.Now()
    cards, err := service.FindTrailCards(context.Background(), userID, "")
    cardsDuration := time.Since(start)
    
    assert.NoError(t, err)
    assert.Greater(t, len(cards), 0)
    assert.Less(t, cardsDuration, 50*time.Millisecond, "Cards endpoint too slow")
}
```

#### 4.2 Memory Usage Testing

**Memory Profiling Tests**:
```go
func TestMemoryUsage(t *testing.T) {
    // Setup memory profiling
    var m1, m2 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)
    
    service := setupMemoryTestService()
    
    // Simulate heavy usage
    for i := 0; i < 1000; i++ {
        userID := fmt.Sprintf("user%d", i)
        body := createTestProgressionBody()
        err := service.CreateLesson(context.Background(), userID, body)
        assert.NoError(t, err)
        
        if i%100 == 0 {
            // Get progress to trigger calculation
            _, err := service.GetUserProgress(context.Background(), userID)
            assert.NoError(t, err)
        }
    }
    
    runtime.GC()
    runtime.ReadMemStats(&m2)
    
    // Calculate memory usage
    allocatedMB := float64(m2.Alloc-m1.Alloc) / 1024 / 1024
    totalAllocMB := float64(m2.TotalAlloc-m1.TotalAlloc) / 1024 / 1024
    
    t.Logf("Memory allocated: %.2f MB", allocatedMB)
    t.Logf("Total allocations: %.2f MB", totalAllocMB)
    
    // Assert memory usage is reasonable
    assert.Less(t, allocatedMB, 50.0, "Too much memory allocated")
    assert.Less(t, totalAllocMB, 500.0, "Too many total allocations")
}
```

### Phase 5: End-to-End Testing (Week 5)

#### 5.1 Complete User Journey Testing

**Full User Progression Flow**:
```go
func TestCompleteUserJourney(t *testing.T) {
    // Setup complete system
    system := setupCompleteTestSystem()
    userID := "journey_test_user"
    
    // 1. User starts first lesson
    err := system.StartLesson(userID, "trail1", "lesson1")
    assert.NoError(t, err)
    
    // Verify progress recorded
    summary, err := system.GetProgress(userID)
    assert.NoError(t, err)
    assert.Contains(t, summary.Trails, "trail1")
    
    // 2. User completes lesson content
    for _, contentID := range []string{"content1", "content2", "content3"} {
        err := system.CompleteContent(userID, "trail1", "lesson1", contentID)
        assert.NoError(t, err)
    }
    
    // 3. User completes lesson
    err = system.CompleteLesson(userID, "trail1", "lesson1")
    assert.NoError(t, err)
    
    // Verify lesson completion and rewards
    summary, err = system.GetProgress(userID)
    assert.NoError(t, err)
    assert.True(t, summary.Trails["trail1"].LessonsCompleted["lesson1"])
    assert.True(t, summary.Trails["trail1"].LessonsRewarded["lesson1"])
    
    vault, err := system.GetVault(userID)
    assert.NoError(t, err)
    assert.Greater(t, vault.Coins, 0)
    
    // 4. User completes all lessons in trail
    for _, lessonID := range []string{"lesson2", "lesson3", "lesson4"} {
        err := system.CompleteLesson(userID, "trail1", lessonID)
        assert.NoError(t, err)
    }
    
    // 5. User accesses challenge
    err = system.StartChallenge(userID, "trail1", "challenge1")
    assert.NoError(t, err)
    
    // 6. User completes challenge
    err = system.CompleteChallenge(userID, "trail1", "challenge1")
    assert.NoError(t, err)
    
    // Verify trail completion
    summary, err = system.GetProgress(userID)
    assert.NoError(t, err)
    assert.True(t, summary.Trails["trail1"].IsCompleted)
    assert.Equal(t, 100, summary.Trails["trail1"].ProgressPercent)
    
    // 7. User gets achievement
    achievements, err := system.GetAchievements(userID)
    assert.NoError(t, err)
    assert.Greater(t, len(achievements), 0)
}
```

#### 5.2 Error Scenario Testing

**Error Handling Tests**:
```go
func TestErrorScenarios(t *testing.T) {
    system := setupTestSystem()
    userID := "error_test_user"
    
    // Test invalid progression attempts
    testCases := []struct {
        name        string
        action      func() error
        expectedErr error
    }{
        {
            name: "lesson jump",
            action: func() error {
                // Try to access lesson 3 without completing 1 and 2
                return system.StartLesson(userID, "trail1", "lesson3")
            },
            expectedErr: ErrInvalidLessonProgress,
        },
        {
            name: "challenge without lessons",
            action: func() error {
                // Try to access challenge without completing lessons
                return system.StartChallenge(userID, "trail1", "challenge1")
            },
            expectedErr: ErrInvalidChallengeProgress,
        },
        {
            name: "trail requirements not met",
            action: func() error {
                // Try to access trail with unfulfilled requirements
                return system.StartLesson(userID, "trail2", "lesson1") // requires trail1
            },
            expectedErr: ErrInvalidTrailProgress,
        },
    }
    
    for _, tc := range testCases {
        t.Run(tc.name, func(t *testing.T) {
            err := tc.action()
            assert.Error(t, err)
            assert.Equal(t, tc.expectedErr, err)
        })
    }
}
```

## Test Data Management

### Test Fixtures

**Progression Data Fixtures**:
```go
func createLegacyProgressionFixture() *Progression {
    return &Progression{
        User: "test_user",
        Trails: []*Trail{
            {
                ID: "trail1",
                Lessons: []*Lesson{
                    {
                        Identifier: "lesson1",
                        Completed:  true,
                        Rewarded:   true,
                        Current:    "content3",
                        Path: []*LessonContent{
                            {
                                Identifier: "content1",
                                Choice: &ModuleContentChoice{
                                    Identifier: "choice1",
                                    Next:       "content2",
                                },
                                Timestamp: time.Now().Add(-2 * time.Hour),
                            },
                            // ... more content
                        },
                    },
                    // ... more lessons
                },
                Challenge: &Challenge{
                    Identifier: "challenge1",
                    Completed:  false,
                    Phases: []*ChallengePhase{
                        // ... challenge phases
                    },
                },
                Total:              50,
                LessonsCompleted:   true,
                ChallengeCompleted: false,
            },
        },
        CreatedAt: time.Now().Add(-24 * time.Hour),
        UpdatedAt: time.Now(),
    }
}
```

### Test Database Management

**Database Setup and Cleanup**:
```go
func setupTestDatabase() *mongo.Database {
    // Use test database
    client := mongo.Connect(context.Background(), options.Client().ApplyURI("mongodb://localhost:27017"))
    db := client.Database("progression_test")
    
    // Clean existing data
    collections := []string{"progressions", "progress_events", "progress_summaries"}
    for _, collection := range collections {
        db.Collection(collection).Drop(context.Background())
    }
    
    return db
}

func teardownTestDatabase(db *mongo.Database) {
    // Clean up test data
    db.Drop(context.Background())
}
```

## Continuous Testing

### Automated Test Execution

**CI/CD Pipeline Tests**:
```yaml
# .github/workflows/progression-tests.yml
name: Progression System Tests

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: 1.21
      - run: go test ./internal/model/progression/... -v
      - run: go test ./internal/service/progression/... -v
      - run: go test ./internal/repository/progression/... -v

  integration-tests:
    runs-on: ubuntu-latest
    services:
      mongodb:
        image: mongo:latest
        ports:
          - 27017:27017
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
      - run: go test ./tests/integration/progression/... -v

  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
      - run: go test ./tests/performance/progression/... -v -bench=.
```

### Test Reporting

**Test Coverage and Metrics**:
```go
func TestCoverage(t *testing.T) {
    // Run tests with coverage
    cmd := exec.Command("go", "test", "-coverprofile=coverage.out", "./...")
    output, err := cmd.CombinedOutput()
    assert.NoError(t, err, "Tests failed: %s", output)
    
    // Parse coverage
    coverage := parseCoverageReport("coverage.out")
    assert.GreaterOrEqual(t, coverage, 90.0, "Test coverage below threshold")
}
```

## Testing Timeline

| Week | Phase | Focus | Deliverables |
|------|-------|-------|--------------|
| 1 | Unit Testing | Models, calculators, repositories | Unit test suite |
| 2 | Integration | Service layer, API compatibility | Integration tests |
| 3 | Migration | Data migration, rollback scenarios | Migration validation |
| 4 | Performance | Load testing, memory profiling | Performance benchmarks |
| 5 | E2E | Complete user journeys, error scenarios | E2E test suite |

## Success Metrics

### Test Quality Metrics

- **Test Coverage**: >90% code coverage
- **Test Execution Time**: <5 minutes for full suite
- **Test Reliability**: <1% flaky test rate
- **Performance Regression**: 0% performance degradation

### Functional Metrics

- **API Compatibility**: 100% existing tests pass
- **Data Integrity**: 0% data loss during migration
- **Error Handling**: All error scenarios covered
- **User Experience**: No breaking changes in user journeys

This comprehensive testing strategy ensures a safe, reliable migration to the new event-driven progression system while maintaining full backward compatibility and performance improvements.
