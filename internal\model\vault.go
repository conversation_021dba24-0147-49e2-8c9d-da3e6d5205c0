package model

import (
	"time"

	"github.com/imdario/mergo"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Vault struct {
	ObjectID  primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID        string             `json:"id,omitempty" bson:"-"`
	User      string             `json:"user" bson:"user"`
	Coins     int64              `json:"coins" bson:"coins"`
	CreatedAt time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time          `json:"updatedAt" bson:"updatedAt"`
}

func (v *Vault) PrepareCreate() error {
	v.CreatedAt = time.Now()
	v.UpdatedAt = v.CreatedAt

	return v.ValidateCreate()
}

func (v *Vault) ValidateCreate() error {

	if v.User == "" {
		return ErrVaultInvalidUser
	}

	return nil
}

func (v *Vault) PrepareUpdate(newVault *Vault) error {
	if err := mergo.Merge(v, newVault, mergo.WithOverride); err != nil {
		return err
	}

	v.UpdatedAt = time.Now()

	if v.ID == "" {
		if !v.ObjectID.IsZero() {
			v.ID = v.ObjectID.Hex()
		} else {
			return ErrVaultInvalidId
		}
	} else {
		vaultObjectId, err := primitive.ObjectIDFromHex(v.ID)
		if err != nil {
			return err
		}
		v.ObjectID = vaultObjectId
	}

	return v.ValidateUpdate()
}

func (v *Vault) ValidateUpdate() error {
	return v.ValidateCreate()
}
