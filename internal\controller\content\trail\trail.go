package trail

import (
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/labstack/echo/v4"
)

// Regular Trail CRUD

func (tc *controller) CreateRegularTrail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var trail content.Trail
		if err := c.Bind(&trail); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := trail.PrepareCreate(); err != nil {
			return err
		}

		if err := tc.Service.CreateRegularTrail(ctx, &trail); err != nil {
			return err
		}

		createdTrail, err := tc.Service.FindRegularTrailByIdentifier(ctx, trail.Identifier)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, createdTrail.Sanitize())
	}
}

func (tc *controller) FindRegularTrail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		trail, err := tc.Service.FindRegularTrail(ctx, c.Param("id"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *controller) FindAllRegularTrails() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		result, err := tc.Service.FindAllRegularTrails(ctx)
		if err != nil {
			return err
		}

		if result == nil {
			return c.JSON(http.StatusNoContent, nil)
		}

		for _, trail := range result {
			trail.Sanitize()
		}

		return c.JSON(http.StatusOK, result)
	}
}

func (tc *controller) FindRegularTrailByIdentifier() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		trail, err := tc.Service.FindRegularTrailByIdentifier(ctx, c.Param("identifier"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *controller) UpdateRegularTrail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")

		trail := &content.Trail{
			ID: trailID,
		}
		if err := c.Bind(&trail); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := tc.Service.UpdateRegularTrail(ctx, trail); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *controller) PatchRegularTrail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")

		trail, err := tc.Service.FindRegularTrail(ctx, trailID)
		if err != nil {
			return err
		}

		var newTrail content.Trail
		if err = c.Bind(&newTrail); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err = trail.PrepareUpdate(&newTrail); err != nil {
			return err
		}

		if err = tc.Service.UpdateRegularTrail(ctx, trail); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *controller) DeleteRegularTrail() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")

		if err := tc.Service.DeleteRegularTrail(ctx, trailID); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Card CRUD

func (tc *controller) FindRegularTrailCardData() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		cardData, err := tc.Service.FindRegularTrailCardData(ctx, c.Param("id"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, cardData)
	}
}

func (tc *controller) FindAllRegularTrailsCardData() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		result, err := tc.Service.FindAllRegularTrailsCardData(ctx)
		if err != nil {
			return err
		}

		if result == nil {
			return c.JSON(http.StatusNoContent, nil)
		}

		return c.JSON(http.StatusOK, result)
	}
}

// Lesson CRUD
func (tc *controller) CreateRegularLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")

		var lesson content.Lesson
		if err := c.Bind(&lesson); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := lesson.PrepareCreate(); err != nil {
			return err
		}

		if err := tc.Service.CreateRegularLesson(ctx, trailID, &lesson); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lesson)
	}
}

func (tc *controller) FindRegularLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")
		lessonID := c.Param("identifier")

		lesson, err := tc.Service.FindRegularLesson(ctx, trailID, lessonID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lesson)
	}
}

func (tc *controller) UpdateRegularLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")
		lessonID := c.Param("identifier")

		var lesson content.Lesson
		if err := c.Bind(&lesson); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := tc.Service.UpdateRegularLesson(ctx, trailID, lessonID, &lesson); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lesson)
	}
}

func (tc *controller) PatchRegularLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")
		lessonID := sanitizeString(c.Param("identifier"))

		lesson, err := tc.Service.FindRegularLesson(ctx, trailID, lessonID)
		if err != nil {
			return err
		}

		var newLesson content.Lesson
		if err = c.Bind(&newLesson); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err = lesson.PrepareUpdate(); err != nil {
			return err
		}

		if err = tc.Service.UpdateRegularLesson(ctx, trailID, lessonID, lesson); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lesson)
	}
}

func (tc *controller) DeleteRegularLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		trailID := c.Param("id")
		lessonID := sanitizeString(c.Param("identifier"))

		if err := tc.Service.DeleteRegularLesson(ctx, trailID, lessonID); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}
