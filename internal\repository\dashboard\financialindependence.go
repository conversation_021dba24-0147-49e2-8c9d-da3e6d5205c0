package dashboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (m *mongoDB) FindFinancialIndependence(ctx context.Context, userID string) (*dashboard.FinancialIndependence, error) {
	var fi dashboard.FinancialIndependence
	err := m.financialIndependenceCollection.FindOne(ctx, bson.M{"userID": userID}).Decode(&fi)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "financial independence not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find financial independence", errors.Internal, err)
	}

	fi.ID = fi.ObjectID.Hex()
	return &fi, nil
}

func (m *mongoDB) FindFinancialIndependenceByUser(ctx context.Context, userID string) (*dashboard.FinancialIndependence, error) {
	var fi dashboard.FinancialIndependence
	err := m.financialIndependenceCollection.FindOne(ctx, bson.M{"userID": userID}).Decode(&fi)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "financial independence not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find financial independence", errors.Internal, err)
	}

	fi.ID = fi.ObjectID.Hex()
	return &fi, nil
}

func (m *mongoDB) CreateFinancialIndependence(ctx context.Context, fi *dashboard.FinancialIndependence) error {
	result, err := m.financialIndependenceCollection.InsertOne(ctx, fi)
	if err != nil {
		return errors.New(errors.Repository, "failed to create financial independence", errors.Internal, err)
	}

	fi.ObjectID = result.InsertedID.(primitive.ObjectID)
	fi.ID = fi.ObjectID.Hex()
	return nil
}

func (m *mongoDB) UpdateFinancialIndependence(ctx context.Context, fi *dashboard.FinancialIndependence) error {
	filter := bson.M{"userID": fi.UserID}
	update := bson.M{"$set": fi}

	opts := options.Update().SetUpsert(true)
	result, err := m.financialIndependenceCollection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return errors.New(errors.Repository, "failed to update financial independence", errors.Internal, err)
	}

	// If it was an upsert and a new document was created
	if result.UpsertedID != nil {
		fi.ObjectID = result.UpsertedID.(primitive.ObjectID)
		fi.ID = fi.ObjectID.Hex()
	}

	return nil
}

func (m *mongoDB) DeleteFinancialIndependence(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.financialIndependenceCollection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete financial independence", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "financial independence not found", errors.NotFound, nil)
	}

	return nil
}
