package auth

// Role represents a user role in the system
type Role string

// Core user roles in the system (ordered from highest to lowest privilege)
const (
	// RoleAdmin has full system access with no restrictions (Level 5)
	RoleAdmin Role = "admin"

	// RoleDirector has department/organization-wide access (Level 4)
	RoleDirector Role = "director"

	// RoleHumanResources has access to HR-related data within their company scope (Level 3)
	RoleHumanResources Role = "human_resources"

	// RoleTeacher has access to own records and assigned students (Level 2)
	RoleTeacher Role = "teacher"

	// Level 1 roles - equal privilege but different contexts:
	// RoleStudent has access to own records only (educational context)
	RoleStudent Role = "student"
	// RoleUser has access to own records only (general platform user)
	RoleUser Role = "user"
)

// Company-specific HR roles for granular access control
const (
	// RoleHRCompanyPrefix is used to create company-specific HR roles
	// Format: "hr_company_{companyId}" (e.g., "hr_company_123")
	RoleHRCompanyPrefix = "hr_company_"
)

// RoleHierarchy defines the hierarchical relationship between roles
// Higher index means higher privilege level (ordered from highest to lowest)
var RoleHierarchy = map[Role]int{
	RoleAdmin:          5, // Highest privilege
	RoleDirector:       4,
	RoleHumanResources: 3,
	RoleTeacher:        2,
	RoleStudent:        1, // Educational context users
	RoleUser:           1, // General platform users (same level as student)
}

// AllRoles returns all valid roles in the system (ordered from highest to lowest privilege)
func AllRoles() []Role {
	return []Role{
		RoleAdmin,          // Level 5
		RoleDirector,       // Level 4
		RoleHumanResources, // Level 3
		RoleTeacher,        // Level 2
		RoleStudent,        // Level 1 (educational context)
		RoleUser,           // Level 1 (general platform)
	}
}

// IsValidRole checks if a role string is valid
func IsValidRole(role string) bool {
	r := Role(role)
	for _, validRole := range AllRoles() {
		if r == validRole {
			return true
		}
	}

	// Check if it's a company-specific HR role
	if len(role) > len(RoleHRCompanyPrefix) && role[:len(RoleHRCompanyPrefix)] == RoleHRCompanyPrefix {
		return true
	}

	return false
}

// GetRoleLevel returns the hierarchy level of a role
func GetRoleLevel(role Role) int {
	if level, exists := RoleHierarchy[role]; exists {
		return level
	}

	// Company-specific HR roles have the same level as base HR role
	roleStr := string(role)
	if len(roleStr) > len(RoleHRCompanyPrefix) && roleStr[:len(RoleHRCompanyPrefix)] == RoleHRCompanyPrefix {
		return RoleHierarchy[RoleHumanResources]
	}

	return 0 // Unknown role
}

// HasRole checks if a user has a specific role
func HasRole(userRoles []string, targetRole Role) bool {
	targetRoleStr := string(targetRole)
	for _, role := range userRoles {
		if role == targetRoleStr {
			return true
		}
	}
	return false
}

// HasAnyRole checks if a user has any of the specified roles
func HasAnyRole(userRoles []string, targetRoles ...Role) bool {
	for _, targetRole := range targetRoles {
		if HasRole(userRoles, targetRole) {
			return true
		}
	}
	return false
}

// HasMinimumRole checks if a user has at least the minimum required role level
func HasMinimumRole(userRoles []string, minimumRole Role) bool {
	minimumLevel := GetRoleLevel(minimumRole)

	for _, roleStr := range userRoles {
		role := Role(roleStr)
		if GetRoleLevel(role) >= minimumLevel {
			return true
		}
	}

	return false
}

// Individual role checker functions (ordered from highest to lowest privilege)

// IsAdmin checks if a user has admin role
func IsAdmin(userRoles []string) bool {
	return HasRole(userRoles, RoleAdmin)
}

// IsDirector checks if a user has director role
func IsDirector(userRoles []string) bool {
	return HasRole(userRoles, RoleDirector)
}

// IsHumanResources checks if a user has any HR role (base or company-specific)
func IsHumanResources(userRoles []string) bool {
	for _, roleStr := range userRoles {
		if roleStr == string(RoleHumanResources) {
			return true
		}
		// Check for company-specific HR roles
		if len(roleStr) > len(RoleHRCompanyPrefix) && roleStr[:len(RoleHRCompanyPrefix)] == RoleHRCompanyPrefix {
			return true
		}
	}
	return false
}

// IsTeacher checks if a user has teacher role
func IsTeacher(userRoles []string) bool {
	return HasRole(userRoles, RoleTeacher)
}

// Level 1 role checkers (equal privilege, different contexts):

// IsStudent checks if a user has student role
func IsStudent(userRoles []string) bool {
	return HasRole(userRoles, RoleStudent)
}

// IsUser checks if a user has user role
func IsUser(userRoles []string) bool {
	return HasRole(userRoles, RoleUser)
}

// GetHRCompanyIDs extracts company IDs from HR company-specific roles
func GetHRCompanyIDs(userRoles []string) []string {
	var companyIDs []string

	for _, roleStr := range userRoles {
		if len(roleStr) > len(RoleHRCompanyPrefix) && roleStr[:len(RoleHRCompanyPrefix)] == RoleHRCompanyPrefix {
			companyID := roleStr[len(RoleHRCompanyPrefix):]
			if companyID != "" {
				companyIDs = append(companyIDs, companyID)
			}
		}
	}

	return companyIDs
}

// CreateHRCompanyRole creates a company-specific HR role
func CreateHRCompanyRole(companyID string) Role {
	return Role(RoleHRCompanyPrefix + companyID)
}

// RolePermissions defines what each role can access
type RolePermissions struct {
	CanAccessAllData     bool     // Admin only
	CanAccessDepartment  bool     // Director and above
	CanAccessOwnStudents bool     // Teacher and above
	CanAccessOwnData     bool     // All roles
	CanAccessCompanyData bool     // HR roles
	CompanyIDs           []string // For HR company-specific roles
}

// GetPermissions returns the permissions for a user based on their roles
// Checks roles in hierarchical order (highest to lowest privilege)
func GetPermissions(userRoles []string) RolePermissions {
	permissions := RolePermissions{
		CanAccessOwnData: true, // All users can access their own data
	}

	// Check for admin role first (highest privilege - Level 5)
	if IsAdmin(userRoles) {
		permissions.CanAccessAllData = true
		permissions.CanAccessDepartment = true
		permissions.CanAccessOwnStudents = true
		permissions.CanAccessCompanyData = true
		return permissions
	}

	// Check for director role (Level 4)
	if IsDirector(userRoles) {
		permissions.CanAccessDepartment = true
		permissions.CanAccessOwnStudents = true
	}

	// Check for HR roles (Level 3)
	if IsHumanResources(userRoles) {
		permissions.CanAccessCompanyData = true
		permissions.CompanyIDs = GetHRCompanyIDs(userRoles)
	}

	// Check for teacher role (Level 2)
	if IsTeacher(userRoles) {
		permissions.CanAccessOwnStudents = true
	}

	// Level 1 roles (Student/User) only have CanAccessOwnData = true (set above)

	return permissions
}
