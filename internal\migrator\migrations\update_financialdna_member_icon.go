package migrations

import (
	"context"
	"log"
	"strings"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// UpdateFinancialDNAMemberIcon implements the Migration interface for updating the icon property in Financial DNA members.
type UpdateFinancialDNAMemberIcon struct {
	db *mongo.Database
}

func NewUpdateFinancialDNAMemberIcon(db *mongo.Database) *UpdateFinancialDNAMemberIcon {
	return &UpdateFinancialDNAMemberIcon{
		db: db,
	}
}

func (m *UpdateFinancialDNAMemberIcon) Name() string {
	// Use current date for uniqueness
	return "update_financialdna_member_icon_20250502_fix000"
}

// Helper function to determine the correct icon based on member relationship and name hints.
func (m *UpdateFinancialDNAMemberIcon) getCorrectIcon(member *financialdna.FamilyMember, meMember *financialdna.FamilyMember, memberMap map[string]*financialdna.FamilyMember) financialdna.FamilyMemberIcon {
	if meMember == nil {
		log.Printf("Warning: meMember is nil, cannot determine relationship for member %s", member.ID)
		return "" // Cannot determine without the root node
	}

	// 1. Check if it's the "me" member
	if member.ID == meMember.ID {
		return financialdna.FamilyMemberIconMe
	}

	// --- Check Ancestors ("me" is the descendant) ---

	// 2. Check if member is a Parent of "me"
	isParent := false
	for _, childID := range member.ChildrenIDs {
		if childID == meMember.ID {
			isParent = true
			break
		}
	}
	if isParent {
		// Distinguish Father/Mother based on name hint if possible
		if strings.HasPrefix(member.Name, "Pai") {
			return financialdna.FamilyMemberIconFather
		}
		if strings.HasPrefix(member.Name, "Mãe") {
			return financialdna.FamilyMemberIconMother
		}
		// Fallback if name doesn't help (e.g., user renamed "Pai" to "John")
		log.Printf("Info: Member %s is a parent of 'me', but name '%s' doesn't match 'Pai' or 'Mãe'. Defaulting to Father icon.", member.ID, member.Name)
		return financialdna.FamilyMemberIconFather // Default to Father if name is ambiguous
	}

	// 3. Check if member is a Grandparent of "me"
	isGrandparent := false
	for _, childID := range member.ChildrenIDs { // childID is a potential parent of "me"
		// Check if this childID is listed in meMember's ParentIDs
		for _, meParentID := range meMember.ParentIDs {
			if childID == meParentID {
				isGrandparent = true
				break
			}
		}
		if isGrandparent {
			break
		}
	}
	if isGrandparent {
		// Distinguish Grandfather/Grandmother based on name hint
		if strings.HasPrefix(member.Name, "Avô") {
			return financialdna.FamilyMemberIconGrandfather
		}
		if strings.HasPrefix(member.Name, "Avó") {
			return financialdna.FamilyMemberIconGrandmother
		}
		log.Printf("Info: Member %s is a grandparent of 'me', but name '%s' doesn't match 'Avô' or 'Avó'. Defaulting to Grandfather icon.", member.ID, member.Name)
		return financialdna.FamilyMemberIconGrandfather // Default to Grandfather
	}

	// 4. Check if member is a Great-Grandparent of "me"
	isGreatGrandparent := false
	for _, childID := range member.ChildrenIDs { // childID is a potential grandparent of "me"
		grandparentCandidate, exists := memberMap[childID]
		if !exists {
			continue // Skip if this child isn't in the map
		}
		// Check if this grandparentCandidate is actually a grandparent (using the logic from step 3)
		isActualGrandparent := false
		for _, gpChildID := range grandparentCandidate.ChildrenIDs { // gpChildID is a potential parent of "me"
			for _, meParentID := range meMember.ParentIDs {
				if gpChildID == meParentID {
					isActualGrandparent = true
					break
				}
			}
			if isActualGrandparent {
				break
			}
		}
		if isActualGrandparent {
			isGreatGrandparent = true
			break
		}
	}
	if isGreatGrandparent {
		// Distinguish Great-Grandfather/Great-Grandmother based on name hint
		if strings.HasPrefix(member.Name, "Bisavô") {
			return financialdna.FamilyMemberIconGreatgrandfather
		}
		if strings.HasPrefix(member.Name, "Bisavó") {
			return financialdna.FamilyMemberIconGreatgrandmother
		}
		log.Printf("Info: Member %s is a great-grandparent of 'me', but name '%s' doesn't match 'Bisavô' or 'Bisavó'. Defaulting to Great-grandfather icon.", member.ID, member.Name)
		return financialdna.FamilyMemberIconGreatgrandfather // Default to Great-grandfather
	}

	// --- Check Descendants ("me" is the ancestor) ---

	// 5. Check if member is a Child of "me"
	isChild := false
	for _, parentID := range member.ParentIDs {
		if parentID == meMember.ID {
			isChild = true
			break
		}
	}
	if isChild {
		return financialdna.FamilyMemberIconChild
	}

	// 6. Check if member is a Grandchild of "me"
	isGrandChild := false
	for _, parentID := range member.ParentIDs { // parentID is a potential child of "me"
		// Check if this parentID is listed in meMember's ChildrenIDs
		for _, meChildID := range meMember.ChildrenIDs {
			if parentID == meChildID {
				isGrandChild = true
				break
			}
		}
		if isGrandChild {
			break
		}
	}
	if isGrandChild {
		return financialdna.FamilyMemberIconGrandChild
	}

	// 7. Fallback: Relationship unknown relative to "me" within 3 generations up/down
	log.Printf("Warning: Could not determine relationship icon for member '%s' (ID: %s) relative to 'me'. Icon will not be updated.", member.Name, member.ID)
	return "" // Return empty string to indicate no update should happen
}

func (m *UpdateFinancialDNAMemberIcon) Up(ctx context.Context) error {
	financialDNACollection := m.db.Collection(repository.FINANCIAL_DNA_COLLECTION)

	cursor, err := financialDNACollection.Find(ctx, bson.D{})
	if err != nil {
		return errors.New(errors.Migrator, "failed to find financial dna trees", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	log.Println("Starting migration to update member icons in financial dna trees...")

	var updatedCount int64
	var matchedCount int64 // Track members checked

	for cursor.Next(ctx) {
		var tree financialdna.FinancialDNATree
		if err := cursor.Decode(&tree); err != nil {
			// Log the raw BSON if decoding fails
			var raw bson.Raw
			if decodeErr := cursor.Decode(&raw); decodeErr == nil {
				log.Printf("Error decoding financial dna tree raw data: %s. Error: %v. Skipping.", raw.String(), err)
			} else {
				log.Printf("Error decoding financial dna tree and failed to get raw data. Error: %v. Skipping.", err)
			}
			continue // Skip this tree
		}

		// Ensure ObjectID is populated if it's zero (might happen with older data)
		if tree.ObjectID.IsZero() && tree.ID != "" {
			objID, idErr := primitive.ObjectIDFromHex(tree.ID)
			if idErr == nil {
				tree.ObjectID = objID
			} else {
				log.Printf("Error converting tree ID %s to ObjectID: %v. Skipping tree.", tree.ID, idErr)
				continue
			}
		} else if tree.ObjectID.IsZero() {
			log.Printf("Tree found with zero ObjectID and empty ID string. Skipping.")
			continue
		}

		if len(tree.Members) == 0 {
			log.Printf("Financial DNA Tree %s has no members. Skipping.", tree.ObjectID.Hex())
			continue
		}

		// Build a map for quick member lookup by ID within this tree
		memberMap := make(map[string]*financialdna.FamilyMember)
		var meMember *financialdna.FamilyMember // Typically members[0], but let's verify

		for i, member := range tree.Members {
			// Populate ID from ObjectID if necessary
			if member.ID == "" && !member.ObjectID.IsZero() {
				member.ID = member.ObjectID.Hex()
			} else if member.ID == "" && member.ObjectID.IsZero() {
				// Attempt to generate ObjectID from Hex if ID exists but ObjectID is zero
				if member.ID != "" {
					objID, idErr := primitive.ObjectIDFromHex(member.ID)
					if idErr == nil {
						member.ObjectID = objID
					} else {
						log.Printf("Warning: Member found with empty ObjectID but valid-looking ID '%s' in tree %s at index %d. Conversion failed: %v. Skipping member.", member.ID, tree.ObjectID.Hex(), i, idErr)
						continue
					}
				} else {
					log.Printf("Warning: Member found with empty ID and zero ObjectID in tree %s at index %d. Skipping member.", tree.ObjectID.Hex(), i)
					continue // Skip this malformed member
				}
			}
			memberMap[member.ID] = member
			// Identify the "me" member (usually the first one, confirm by name if possible)
			if i == 0 { // Assume the first member is "me"
				meMember = member
				// Optional: Add a check for name == "Eu" for robustness
				// if member.Name != "Eu" {
				//  log.Printf("Warning: First member in tree %s is not named 'Eu'. Assuming it's still the root.", tree.ObjectID.Hex())
				// }
			}
		}

		if meMember == nil && len(tree.Members) > 0 {
			log.Printf("Warning: Could not identify 'me' member based on index[0] for tree %s. Attempting to find by name 'Eu'.", tree.ObjectID.Hex())
			// As a fallback, maybe try finding by name "Eu" if the first element wasn't it.
			foundMeByName := false
			for _, member := range tree.Members {
				if member.Name == "Eu" {
					meMember = member
					log.Printf("Found 'me' member by name for tree %s.", tree.ObjectID.Hex())
					foundMeByName = true
					break
				}
			}
			if !foundMeByName {
				log.Printf("Error: Still could not identify 'me' member for tree %s. Skipping icon updates for this tree.", tree.ObjectID.Hex())
				continue
			}
		} else if meMember == nil { // Handles case where tree.Members was empty and loop didn't run
			log.Printf("Tree %s has members array but failed to identify 'me' member. Skipping.", tree.ObjectID.Hex())
			continue
		}

		session, err := financialDNACollection.Database().Client().StartSession()
		if err != nil {
			log.Printf("Failed to start session for tree %s: %v. Skipping.", tree.ObjectID.Hex(), err)
			continue // Skip this tree
		}

		// Process updates within a transaction
		callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
			var transactionUpdatedCount int64
			var transactionMatchedCount int64 // Members processed in this transaction

			for _, member := range tree.Members {
				// Ensure member has ObjectID for filtering (redundant check, but safe)
				if member.ObjectID.IsZero() {
					objID, idErr := primitive.ObjectIDFromHex(member.ID)
					if idErr != nil {
						log.Printf("Error converting member ID %s to ObjectID in tree %s during transaction. Skipping member update.", member.ID, tree.ObjectID.Hex())
						continue
					}
					member.ObjectID = objID
				}

				transactionMatchedCount++ // Count every member processed
				correctIcon := m.getCorrectIcon(member, meMember, memberMap)

				// Only update if the icon is missing or incorrect, and we determined a correct one
				if correctIcon != "" && member.Icon != correctIcon {
					log.Printf("Updating icon for member '%s' (ID: %s) in tree %s from '%s' to '%s'", member.Name, member.ID, tree.ObjectID.Hex(), member.Icon, correctIcon)

					filter := bson.M{"_id": tree.ObjectID}
					arrayFilter := options.ArrayFilters{
						Filters: []interface{}{bson.M{"elem._id": member.ObjectID}}, // Use ObjectID for matching array elements
					}
					update := bson.M{
						"$set": bson.M{
							"members.$[elem].icon": correctIcon, // Use the determined correct icon
							"updatedAt":            time.Now(),
						},
					}

					result, err := financialDNACollection.UpdateOne(
						sessCtx,
						filter,
						update,
						options.Update().SetArrayFilters(arrayFilter),
					)
					if err != nil {
						log.Printf("Error updating icon for member %s in tree %s: %v", member.ID, tree.ObjectID.Hex(), err)
						// Decide whether to abort or continue with other members
						// Aborting transaction on first error:
						return nil, errors.New(errors.Repository, "failed to update member icon", errors.Internal, err)
					}

					if result.ModifiedCount > 0 {
						transactionUpdatedCount++
						// Log success inside loop can be verbose, consider logging only failures/warnings or summary per tree
						// log.Printf("Successfully updated icon for member '%s' in tree %s", member.Name, tree.ObjectID.Hex())
					} else if result.MatchedCount > 0 {
						// This case might happen if the filter matched the tree but not the specific element,
						// or if the update didn't actually change the value (because member.Icon == correctIcon already).
						// Since we check member.Icon != correctIcon, this branch implies the element filter failed.
						log.Printf("Warning: Matched tree %s but did not modify member '%s' (ID: %s). Element filter likely failed.", tree.ObjectID.Hex(), member.Name, member.ID)
					} else {
						log.Printf("Warning: No match found for tree %s when trying to update member '%s' (ID: %s).", tree.ObjectID.Hex(), member.Name, member.ID)
					}
				} else if correctIcon == "" {
					// Logged inside getCorrectIcon
					// log.Printf("Skipping update for member '%s' (ID: %s) in tree %s as correct icon could not be determined.", member.Name, member.ID, tree.ObjectID.Hex())
				} else {
					// Icon is already correct
					// log.Printf("Icon for member '%s' (ID: %s) in tree %s is already correct ('%s').", member.Name, member.ID, tree.ObjectID.Hex(), member.Icon)
				}
			}
			// Return counts for logging outside the transaction
			return bson.M{"matched": transactionMatchedCount, "updated": transactionUpdatedCount}, nil
		}

		resultData, err := session.WithTransaction(ctx, callback)
		if err != nil {
			log.Printf("Transaction failed for tree %s: %v", tree.ObjectID.Hex(), err)
			session.EndSession(ctx)
			continue // Continue with the next tree
		}

		// Log results from the transaction
		if resultData != nil {
			if data, ok := resultData.(bson.M); ok {
				mCount := data["matched"].(int64)
				uCount := data["updated"].(int64)
				matchedCount += mCount // Add members processed in this transaction to total
				updatedCount += uCount
				if uCount > 0 { // Only log if updates occurred for this tree
					log.Printf("Transaction successful for tree %s. Members processed: %d, Icons updated: %d", tree.ObjectID.Hex(), mCount, uCount)
				}
			}
		}

		session.EndSession(ctx) // End session successfully
	}

	if err := cursor.Err(); err != nil {
		log.Printf("Cursor error after iteration: %v", err)
		return errors.New(errors.Migrator, "cursor error after iterating financial dna trees", errors.Internal, err)
	}

	log.Printf("Financial DNA member icon update migration completed. Total members processed: %d, Total icons updated: %d", matchedCount, updatedCount)
	return nil // Migration completed successfully
}

// Down migration is optional. Reverting icon changes might be complex.
func (m *UpdateFinancialDNAMemberIcon) Down(ctx context.Context) error {
	log.Println("Executing Down migration for UpdateFinancialDNAMemberIcon - No automatic revert implemented.")
	// Potentially set all icons back to empty string or a default value.
	// This is generally risky without knowing the previous state.
	// Example: Set all icons to empty
	// financialDNACollection := m.db.Collection(repository.FINANCIAL_DNA_COLLECTION)
	// _, err := financialDNACollection.UpdateMany(
	// 	ctx,
	// 	bson.D{}, // Empty filter matches all documents
	// 	bson.M{"$set": bson.M{"members.$[].icon": ""}}, // Update icon for all elements in members array
	// )
	// if err != nil {
	// 	log.Printf("Error during Down migration trying to clear icons: %v", err)
	// 	return errors.New(errors.Migrator, "failed to clear member icons", errors.Internal, err)
	// }
	// log.Println("Down migration attempted to clear icons (actual implementation might vary).")
	return nil // Or return an error if Down is not supported/implemented
}
