import os
import sys
from PIL import Image, ImageSequence
import argparse
import concurrent.futures
import time  # For timing

# --- Configuration --- (Keep as before)
WHITE_RGB = (255, 255, 255)
BORDER_TOLERANCE = 100
NUM_PASSES = 2
MIN_OPAQUE_ALPHA = 1
# --- End Configuration ---

# --- Helper Functions (Keep as before) ---
def is_close_to_white(rgb, tolerance):
    # ... (same implementation) ...
    if not isinstance(rgb, tuple) or len(rgb) != 3: return False
    if tolerance == 0: return rgb == WHITE_RGB
    effective_tolerance = min(tolerance, 765)
    diff = sum(abs(p - w) for p, w in zip(rgb, WHITE_RGB))
    return diff <= effective_tolerance

def has_transparent_neighbor(x, y, width, height, pixel_data):
    # ... (same implementation) ...
    for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            idx = ny * width + nx
            if isinstance(pixel_data[idx], tuple) and len(pixel_data[idx]) >= 4:
                if pixel_data[idx][3] < MIN_OPAQUE_ALPHA:
                    return True
    return False

# --- Core Processing Function ---
# Takes all necessary parameters now
def process_single_gif(input_path, output_path, tolerance, passes):
    """
    Processes a single GIF file. (Previously multi_pass_remove)
    Returns True on success, False on failure.
    """
    try:
        print(f"Starting: {os.path.basename(input_path)} (Tol: {tolerance}, Passes: {passes})")
        original_gif = Image.open(input_path)

        processed_frames = []
        durations = []
        disposals = []
        is_animated = getattr(original_gif, "is_animated", False)
        frame_count = getattr(original_gif, "n_frames", 1)
        
        if frame_count > 500: # Add a check for very long GIFs
             print(f"Warning: {os.path.basename(input_path)} has {frame_count} frames, processing may be slow.")

        for i, frame_image in enumerate(ImageSequence.Iterator(original_gif)):
            # Optional: Add progress within a long file if needed
            # if frame_count > 50 and i % (frame_count // 10) == 0:
            #      print(f"  Progress {os.path.basename(input_path)}: Frame {i}/{frame_count}")
                 
            duration = frame_image.info.get('duration', 100)
            durations.append(duration)
            disposal = frame_image.info.get('disposal', 2)
            disposals.append(disposal)

            if frame_image.mode != 'RGBA':
                frame_rgba = frame_image.convert("RGBA")
            else:
                frame_rgba = frame_image.copy()

            width, height = frame_rgba.size
            current_pixels = list(frame_rgba.getdata()) # Start with current frame pixels
            total_pixels_changed_in_frame = 0

            # Multi-Pass Loop
            for p in range(passes):
                pixels_to_change_this_pass = []
                pass_pixel_data = list(current_pixels) # Use data from end of *previous* pass

                for y in range(height):
                    for x in range(width):
                        index = y * width + x
                        if not isinstance(pass_pixel_data[index], tuple) or len(pass_pixel_data[index]) < 4: continue
                        r, g, b, a = pass_pixel_data[index]

                        if a >= MIN_OPAQUE_ALPHA and \
                           is_close_to_white((r, g, b), tolerance) and \
                           has_transparent_neighbor(x, y, width, height, pass_pixel_data):
                            pixels_to_change_this_pass.append(index)

                if pixels_to_change_this_pass:
                    pixels_changed_this_pass = len(pixels_to_change_this_pass)
                    total_pixels_changed_in_frame += pixels_changed_this_pass
                    # Reduce verbosity for multiprocessing:
                    # print(f"  Frame {i}, Pass {p+1}: Clearing {pixels_changed_this_pass} border pixels.")
                    
                    temp_pixel_list = list(pass_pixel_data)
                    for index in pixels_to_change_this_pass:
                        r_orig, g_orig, b_orig, a_orig = temp_pixel_list[index]
                        temp_pixel_list[index] = (r_orig, g_orig, b_orig, 0)
                    current_pixels = tuple(temp_pixel_list) # Use result for next pass
                else:
                    # print(f"  Frame {i}, Pass {p+1}: No border pixels detected/changed.")
                    break # No changes, break passes for this frame

            if total_pixels_changed_in_frame > 0:
                frame_rgba.putdata(current_pixels)

            processed_frames.append(frame_rgba)

        # Saving Logic
        if not processed_frames:
            print(f"Warning: No frames processed for {input_path}")
            original_gif.close()
            return False

        loop_count = original_gif.info.get('loop', 0) if is_animated else 0
        processed_frames[0].save(
            output_path, save_all=True, append_images=processed_frames[1:],
            duration=durations if is_animated else 0, loop=loop_count,
            disposal=disposals if is_animated else 2, optimize=False, transparency=0
        )
        print(f"Finished: {os.path.basename(input_path)}")
        return True

    except FileNotFoundError:
        print(f"Error: Input file not found: {input_path}")
        return False
    except Exception as e:
        print(f"Error processing {input_path}: {e}")
        # import traceback # Optional detailed trace
        # traceback.print_exc()
        return False
    finally:
        if 'original_gif' in locals() and hasattr(original_gif, 'close'):
            original_gif.close()

# --- Main Execution Logic ---
def main():
    # Read initial config - these are passed to worker processes
    border_tolerance = BORDER_TOLERANCE
    num_passes = NUM_PASSES

    parser = argparse.ArgumentParser(description="Remove light borders (multi-pass) using multiple processes.")
    parser.add_argument("input_dir", help="Path to folder containing GIFs with borders.")
    parser.add_argument("output_dir", help="Path to folder where cleaned GIFs will be saved.")
    parser.add_argument("-t", "--tolerance", type=int, default=border_tolerance,
                        help=f"Color tolerance (closeness to white). Default: {border_tolerance}")
    parser.add_argument("-p", "--passes", type=int, default=num_passes,
                        help=f"Number of removal passes per frame. Default: {num_passes}")
    parser.add_argument("-w", "--workers", type=int, default=None, # Default lets Python decide (usually num cores)
                        help="Number of worker processes to use. Default: All available cores.")

    args = parser.parse_args()
    input_folder = args.input_dir
    output_folder = args.output_dir
    # Use args values
    border_tolerance = args.tolerance
    num_passes = args.passes
    max_workers = args.workers # Can be None

    if num_passes <= 0: sys.exit("Error: Number of passes must be positive.")
    if not os.path.isdir(input_folder): sys.exit(f"Error: Input directory not found: {input_folder}")

    os.makedirs(output_folder, exist_ok=True)
    print(f"Processing GIFs from: {input_folder}")
    print(f"Saving results to:  {output_folder}")
    print(f"Using border tolerance: {border_tolerance}, Passes: {num_passes}")
    print(f"Max worker processes: {'All available' if max_workers is None else max_workers}")

    start_time = time.time()
    tasks = []
    # Prepare list of tasks (input path, output path, parameters)
    for filename in os.listdir(input_folder):
        if filename.lower().endswith(".gif"):
            input_path = os.path.join(input_folder, filename)
            output_path = os.path.join(output_folder, filename) # Keep original name
            tasks.append((input_path, output_path, border_tolerance, num_passes))

    if not tasks:
        print("No GIF files found in the input directory.")
        return

    processed_count = 0
    error_count = 0

    # Use ProcessPoolExecutor for parallelism
    # max_workers=None uses os.cpu_count()
    with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit tasks
        future_to_task = {executor.submit(process_single_gif, *task): task for task in tasks}

        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_task):
            task_input_path = future_to_task[future][0]
            try:
                success = future.result() # Get result (True/False)
                if success:
                    processed_count += 1
                else:
                    error_count += 1
            except Exception as exc:
                print(f"Error generating result for {os.path.basename(task_input_path)}: {exc}")
                error_count += 1

    end_time = time.time()
    print("\n--- Multi-Pass Edge Removal Summary ---")
    print(f"Successfully processed: {processed_count}")
    print(f"Errors encountered:   {error_count}")
    print(f"Total files attempted: {len(tasks)}")
    print(f"Total execution time: {end_time - start_time:.2f} seconds")
    print("-------------------------------------")

if __name__ == "__main__":
    # This check is important for multiprocessing on some platforms (Windows)
    main()