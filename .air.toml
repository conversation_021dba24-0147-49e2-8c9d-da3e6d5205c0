root = "."
tmp_dir = "tmp"

[build]
  cmd = "go build -o ./tmp/main.exe cmd/dinbora/main.go"
  bin = "./tmp/main.exe"
  delay = 1000 # ms
  exclude_dir = ["tmp", "vendor", "node_modules", ".git"]
  exclude_file = []
  exclude_regex = ["_test.go"]
  follow_symlink = true
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html"]
  kill_delay = 500  # ms
  log = "air_build.log"
  poll = false
  stop_on_error = true

[log]
  main_prefix = "[MAIN]"
  runner_prefix = "[RUNNER]"
  timestamp = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"