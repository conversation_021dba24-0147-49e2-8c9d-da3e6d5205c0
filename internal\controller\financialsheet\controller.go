package financialsheet

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	_financialsheet "github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	Find() echo.HandlerFunc
	FindByUser() echo.HandlerFunc
	Update() echo.HandlerFunc
	Delete() echo.HandlerFunc

	// Category CRUD
	CreateCategory() echo.HandlerFunc
	FindCategory() echo.HandlerFunc
	FindAllCategories() echo.HandlerFunc
	UpdateCategory() echo.HandlerFunc
	DeleteCategory() echo.HandlerFunc

	// Transaction Management
	CreateTransaction() echo.HandlerFunc
	CreateRecurringTransaction() echo.HandlerFunc
	FindTransaction() echo.HandlerFunc
	FindAllTransactions() echo.HandlerFunc
	UpdateTransaction() echo.HandlerFunc
	DeleteTransaction() echo.HandlerFunc
	NoTransactions() echo.HandlerFunc

	// League System - These are now handled by the league controller
	// LeagueStatus() echo.HandlerFunc
	// StartNewSeason() echo.HandlerFunc
	// GetLeagueRanking() echo.HandlerFunc

	// Utility
	Initialize() echo.HandlerFunc
}

type controller struct {
	Service _financialsheet.Service
}

func New(service _financialsheet.Service) Controller {
	return &controller{
		Service: service,
	}
}

func (fc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	financialsheetsGroup := currentGroup.Group("/financialsheets", middlewares.AuthGuard())

	// CRUD
	// financeGroup.POST("", fc.Create(), middlewares.AdminGuard())
	financialsheetsGroup.GET("/:id", fc.Find(), middlewares.AdminGuard())
	financialsheetsGroup.GET("/me", fc.FindByUser())
	financialsheetsGroup.PUT("/:id", fc.Update(), middlewares.AdminGuard())
	financialsheetsGroup.DELETE("/:id", fc.Delete(), middlewares.AdminGuard())

	// Category CRUD
	categoriesGroup := financialsheetsGroup.Group("/categories")
	categoriesGroup.POST("", fc.CreateCategory())
	categoriesGroup.GET("/:id", fc.FindCategory(), middlewares.AdminGuard())
	categoriesGroup.GET("", fc.FindAllCategories())
	categoriesGroup.PUT("/:id", fc.UpdateCategory(), middlewares.AdminGuard())
	categoriesGroup.DELETE("/:id", fc.DeleteCategory())

	// Transaction CRUD
	transactionsGroup := financialsheetsGroup.Group("/transactions")
	transactionsGroup.POST("", fc.CreateTransaction())
	transactionsGroup.POST("/recurring", fc.CreateRecurringTransaction())
	transactionsGroup.POST("/dreams", fc.CreateDreamTransaction())
	transactionsGroup.GET("/:id", fc.FindTransaction())
	transactionsGroup.GET("", fc.FindAllTransactions())
	transactionsGroup.PUT("/:id", fc.UpdateTransaction(), middlewares.AdminGuard())
	transactionsGroup.DELETE("/:id", fc.DeleteTransaction())

	transactionsGroup.POST("/no", fc.NoTransactions())

	// League System - Routes removed as they are now handled by the league controller
	// leaguesGroup := financialsheetsGroup.Group("/leagues")
	// leaguesGroup.GET("", fc.LeagueStatus())
	// leaguesGroup.POST("/season/start", fc.StartNewSeason(), middlewares.AdminGuard())
	// leaguesGroup.GET("/ranking", fc.GetLeagueRanking())

	// Utility
	financialsheetsGroup.POST("/initialize", fc.Initialize(), middlewares.AdminGuard())
}

// CRUD
func (fc *controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := strings.TrimSpace(c.Param("id"))
		if id == "" {
			return errors.New(errors.Controller, "invalid financial sheet ID", errors.Validation, nil)
		}

		record, err := fc.Service.Find(ctx, id)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, record)
	}
}

func (fc *controller) FindByUser() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Parse optional month/year filters
		monthStr := c.QueryParam("month")
		yearStr := c.QueryParam("year")

		var month, year int
		if monthStr != "" {
			if m, err := strconv.Atoi(monthStr); err != nil || m < 1 || m > 12 {
				return errors.New(errors.Controller, "invalid month parameter", errors.Validation, nil)
			} else {
				month = m
			}
		}

		if yearStr != "" {
			if y, err := strconv.Atoi(yearStr); err != nil {
				return errors.New(errors.Controller, "invalid year parameter", errors.Validation, nil)
			} else {
				year = y
			}
		}

		// Parse flatten yearData case (month and year provided)
		flatten := false
		if monthStr != "" && yearStr != "" {
			flatten = true
		}

		// Parse planning mode
		planning := false
		if c.QueryParam("planning") == "true" {
			planning = true
		}

		record, err := fc.Service.FindByUserAndPeriod(ctx, userToken.Uid, year, month, flatten, planning)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, record)
	}
}

func (fc *controller) Update() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := strings.TrimSpace(c.Param("id"))
		if id == "" {
			return errors.New(errors.Controller, "invalid financial sheet ID", errors.Validation, nil)
		}

		var record financialsheet.Record
		if err := c.Bind(&record); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		record.ID = id
		if err := fc.Service.Update(ctx, &record); err != nil {
			return err
		}

		// Return updated record
		updatedRecord, err := fc.Service.Find(ctx, id)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedRecord)
	}
}

func (fc *controller) Delete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := strings.TrimSpace(c.Param("id"))
		if id == "" {
			return errors.New(errors.Controller, "invalid financial sheet ID", errors.Validation, nil)
		}

		if err := fc.Service.Delete(ctx, id); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// Category CRUD
func (fc *controller) CreateCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var category financialsheet.Category
		if err := c.Bind(&category); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		createdCategoryID, err := fc.Service.CreateCategory(ctx, &category, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, createdCategoryID)
	}
}

func (fc *controller) FindCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := strings.TrimSpace(c.Param("id"))
		if id == "" {
			return errors.New(errors.Controller, "invalid category ID", errors.Validation, nil)
		}

		category, err := fc.Service.FindCategory(ctx, id)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, category)
	}
}

func (fc *controller) FindAllCategories() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Check for identifier query param
		identifier := strings.TrimSpace(c.QueryParam("identifier"))
		// If identifier provided, return the category by identifier according to the user ID
		if identifier != "" {
			//category, err := fc.Service.FindCategoryByIdentifier(ctx, financialsheet.CategoryIdentifier(identifier))
			category, err := fc.Service.FindCategoryByUserAndIdentifier(ctx, userToken.Uid, financialsheet.CategoryIdentifier(identifier))
			if err != nil {
				return err
			}
			return c.JSON(http.StatusOK, category)
		}

		// No identifier provided, return all categories
		categories, err := fc.Service.FindAllCategories(ctx)
		if err != nil {
			return err
		}
		return c.JSON(http.StatusOK, categories)
	}
}

func (fc *controller) UpdateCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := strings.TrimSpace(c.Param("id"))
		if id == "" {
			return errors.New(errors.Controller, "invalid category ID", errors.Validation, nil)
		}

		var category financialsheet.Category
		if err := c.Bind(&category); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		category.ID = id
		if err := fc.Service.UpdateCategory(ctx, &category); err != nil {
			return err
		}

		// Confirm update by returning updated category
		updatedCategory, err := fc.Service.FindCategory(ctx, id)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedCategory)
	}
}

func (fc *controller) DeleteCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		id := strings.TrimSpace(c.Param("id"))
		if id == "" {
			return errors.New(errors.Controller, "invalid category ID", errors.Validation, nil)
		}

		if err := fc.Service.DeleteCategory(ctx, id, userToken.Uid); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// Transaction Management
func (fc *controller) CreateTransaction() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		record, err := fc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var transaction financialsheet.Transaction
		if err := c.Bind(&transaction); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		updatedRecord, err := fc.Service.CreateTransaction(ctx, record, &transaction, false)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, updatedRecord)
	}
}

// CreateRecurringTransaction creates a transaction with recurring instances
func (fc *controller) CreateRecurringTransaction() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		record, err := fc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var request RecurringTransactionRequest
		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		if err := c.Validate(&request); err != nil {
			return errors.New(errors.Controller, "validation failed", errors.Validation, err)
		}

		// Convert request to transaction
		transaction := financialsheet.Transaction{
			Category:        request.Category,
			MoneySource:     request.MoneySource,
			Value:           request.Value,
			Date:            request.Date,
			PaymentMethod:   request.PaymentMethod,
			Type:            request.Type,
			AttachedDreamID: request.AttachedDreamID,
		}

		updatedRecord, err := fc.Service.CreateRecurringTransaction(ctx, record, &transaction, request.RecurrenceMonths)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, updatedRecord)
	}
}

// CreateDreamTransaction creates a transaction for a dream
func (fc *controller) CreateDreamTransaction() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		record, err := fc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var dreamtransaction DreamTransactionRequest
		if err := c.Bind(&dreamtransaction); err != nil {
			return errors.New(errors.Controller, "dream transaction input failed", errors.Validation, err)
		}
		if err := c.Validate(&dreamtransaction); err != nil {
			return errors.New(errors.Controller, "dream transaction validation failed", errors.Validation, err)
		}

		// Create the Transaction in the FinancialSheet
		transaction := financialsheet.Transaction{
			Category:          "dreams",
			MoneySource:       4, // Others is the default
			Value:             dreamtransaction.Value,
			Date:              dreamtransaction.Date,
			PaymentMethod:     dreamtransaction.PaymentMethod,
			Type:              "costs_of_living",        // Dreams are always costs of living
			AttachedDreamID:   dreamtransaction.DreamID, // Use the dream ID as the description
			AttachedDreamName: dreamtransaction.DreamName,
		}

		updatedRecord, err := fc.Service.CreateTransaction(ctx, record, &transaction, false)
		if err != nil {
			return err
		}

		// Create the Transaction in the Dreamboard
		if err := fc.Service.CreateDreamTransaction(ctx, userToken.Uid, dreamtransaction.DreamID, dreamtransaction.Value); err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, updatedRecord)
	}
}

// FindTransaction finds a transaction by ID
func (fc *controller) FindTransaction() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		record, err := fc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		transactionID := strings.TrimSpace(c.Param("id"))
		if transactionID == "" {
			return errors.New(errors.Controller, "invalid transaction ID", errors.Validation, nil)
		}

		transaction, err := fc.Service.FindTransaction(ctx, record.ID, transactionID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, transaction)
	}
}

// FindAllTransactions returns all transactions for the authenticated user with optional category, month, and year filtering
func (fc *controller) FindAllTransactions() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get optional category type filter from query parameter
		typeParam := strings.TrimSpace(c.QueryParam("type"))
		var categoryType financialsheet.CategoryType
		if typeParam != "" {
			categoryType = financialsheet.CategoryType(typeParam)
		}

		// Parse optional month/year filters
		monthStr := c.QueryParam("month")
		yearStr := c.QueryParam("year")

		var month, year int
		if monthStr != "" {
			if m, err := strconv.Atoi(monthStr); err != nil || m < 1 || m > 12 {
				return errors.New(errors.Controller, "invalid month parameter", errors.Validation, nil)
			} else {
				month = m
			}
		}

		if yearStr != "" {
			if y, err := strconv.Atoi(yearStr); err != nil {
				return errors.New(errors.Controller, "invalid year parameter", errors.Validation, nil)
			} else {
				year = y
			}
		}

		// Parse planning mode
		planning := false
		if c.QueryParam("planning") == "true" {
			planning = true
		}

		transactions, err := fc.Service.FindAllTransactions(ctx, userToken.Uid, categoryType, year, month, planning)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, transactions)
	}
}

// UpdateTransaction updates an existing transaction
func (fc *controller) UpdateTransaction() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		record, err := fc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		transactionID := strings.TrimSpace(c.Param("id"))
		if transactionID == "" {
			return errors.New(errors.Controller, "invalid transaction ID", errors.Validation, nil)
		}

		var transaction financialsheet.Transaction
		if err := c.Bind(&transaction); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		// Ensure the transaction ID matches the route parameter
		transaction.ID = transactionID

		updatedRecord, err := fc.Service.UpdateTransaction(ctx, record, &transaction)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedRecord)
	}
}

// DeleteTransaction deletes a transaction
func (fc *controller) DeleteTransaction() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		record, err := fc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		transactionID := strings.TrimSpace(c.Param("id"))
		if transactionID == "" {
			return errors.New(errors.Controller, "invalid transaction ID", errors.Validation, nil)
		}

		updatedRecord, err := fc.Service.DeleteTransaction(ctx, record, transactionID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedRecord)
	}
}

// NoTransactions is a utility function to give a sequence to the user by register no transactions
func (fc *controller) NoTransactions() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		record, err := fc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		if err := fc.Service.NoTransactions(ctx, record); err != nil {
			// If error is a conflict from the service layer, than the user already marked no transactions for today
			// Gracefully return a success response
			if domainErr, ok := err.(*errors.DomainError); ok {
				if domainErr.Kind() == errors.Conflict && domainErr.Layer() == errors.Service {
					return c.JSON(http.StatusOK, NoTransactionsResponse{
						Message: "No transactions already marked for today",
						Success: true,
					})
				}
			}
			// Otherwise, return the error as is
			return err
		}

		// Return updated record with current streak information
		updatedRecord, err := fc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			// If we can't fetch the updated record, still return success
			// since the NoTransactions operation completed successfully
			return c.JSON(http.StatusOK, NoTransactionsResponse{
				Message: "No transactions marked successfully",
				Success: true,
			})
		}

		return c.JSON(http.StatusOK, NoTransactionsResponse{
			Message:       "No transactions marked successfully",
			Success:       true,
			CurrentStreak: updatedRecord.Points.Current,
			BestStreak:    updatedRecord.Points.Best,
		})
	}
}

// League System - Method implementations removed as they are now part of league.Controller

// Utility
func (fc *controller) Initialize() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var user model.User
		if err := c.Bind(&user); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		if err := fc.Service.Initialize(ctx, user.ID, user.Name); err != nil {
			return err
		}

		// Return the newly created record
		record, err := fc.Service.FindByUser(ctx, user.ID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, record)
	}
}
