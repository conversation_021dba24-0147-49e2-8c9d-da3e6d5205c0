package migrations

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// UpdateDreamboardAchievable implements the Migration interface for updating achievable field in existing dreamboard records
type UpdateDreamboardAchievable struct {
	db *mongo.Database
}

func NewUpdateDreamboardAchievable(db *mongo.Database) *UpdateDreamboardAchievable {
	return &UpdateDreamboardAchievable{
		db: db,
	}
}

func (m *UpdateDreamboardAchievable) Name() string {
	return "update_dreamboard_achievable_2025_08_11"
}

func (m *UpdateDreamboardAchievable) Up(ctx context.Context) error {
	dreamboardCollection := m.db.Collection(repository.DREAMBOARDS_COLLECTION)

	cursor, err := dreamboardCollection.Find(ctx, bson.D{})
	if err != nil {
		return errors.New(errors.Migrator, "failed to find dreamboards", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	processedCount := 0
	updatedCount := 0

	for cursor.Next(ctx) {
		var board dreamboard.Dreamboard
		if err := cursor.Decode(&board); err != nil {
			log.Printf("Warning: failed to decode dreamboard %s: %v", board.ObjectID.Hex(), err)
			continue
		}

		processedCount++
		needsUpdate := false

		// Recalculate achievable field for each dream
		for i, dream := range board.Dreams {
			if dream == nil {
				continue
			}

			// Calculate the new achievable value using the IsAchievable function
			newAchievable := dream.IsAchievable()

			// Check if the achievable field needs to be updated
			if dream.Achievable != newAchievable {
				board.Dreams[i].Achievable = newAchievable
				needsUpdate = true
			}
		}

		// Only update the document if there are changes
		if needsUpdate {
			// Prepare the update document with only the dreams array
			updateDoc := bson.M{
				"$set": bson.M{
					"dreams": board.Dreams,
				},
			}

			result, err := dreamboardCollection.UpdateOne(ctx,
				bson.D{primitive.E{Key: "_id", Value: board.ObjectID}},
				updateDoc,
			)

			if err != nil {
				if mongo.IsDuplicateKeyError(err) {
					return errors.New(errors.Migrator, errors.DreamboardConflictUpdate, errors.Conflict, err)
				}
				return errors.New(errors.Migrator, errors.DreamboardUpdateFailed, errors.Internal, err)
			}

			if result.MatchedCount <= 0 {
				log.Printf("Warning: dreamboard %s not found during update", board.ObjectID.Hex())
				continue
			}

			updatedCount++
			log.Printf("Updated dreamboard %s with recalculated achievable fields", board.ObjectID.Hex())
		}
	}

	if err := cursor.Err(); err != nil {
		return errors.New(errors.Migrator, "cursor iteration failed", errors.Internal, err)
	}

	log.Printf("Migration completed: processed %d dreamboards, updated %d dreamboards with achievable field changes", processedCount, updatedCount)
	return nil
}
