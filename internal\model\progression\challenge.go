package progression

import (
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/content"
)

type Challenge struct {
	Identifier string            `json:"identifier,omitempty" bson:"identifier"`
	Phases     []*ChallengePhase `json:"phases,omitempty" bson:"phases"`
	Current    string            `json:"current,omitempty" bson:"current"`
	Completed  bool              `json:"completed,omitempty" bson:"completed"`
	Total      uint8             `json:"total" bson:"total"`
	Available  bool              `json:"available"`
	Rewarded   bool              `json:"rewarded" bson:"rewarded"`
}

type ChallengePhase struct {
	Identifier    string              `json:"identifier,omitempty" bson:"identifier"`
	Path          []*ChallengeContent `json:"path,omitempty" bson:"path"`
	Current       string              `json:"current,omitempty" bson:"current"`
	Completed     bool                `json:"completed,omitempty" bson:"completed"`
	Available     bool                `json:"available"`
	Rewarded      bool                `json:"rewarded" bson:"rewarded"`
	CurrentPoints int                 `json:"currentPoints" bson:"currentPoints"`
}

// ResetPoints resets the current points of the challenge phase to 0 if they are greater than 0.
func (cp *ChallengePhase) ResetPoints() {
	if cp.CurrentPoints > 0 {
		cp.CurrentPoints = 0
	}
}

type ChallengeContent struct {
	Type       string               `json:"type,omitempty" bson:"type,omitempty"`
	Identifier string               `json:"identifier,omitempty" bson:"identifier,omitempty"`
	Choice     *ModuleContentChoice `json:"choice,omitempty" bson:"choice,omitempty"`
	Timestamp  time.Time            `json:"timestamp" bson:"timestamp"`
}

type ChallengeContentChoice struct {
	Identifier string      `json:"identifier" bson:"identifier"`
	Value      interface{} `json:"value,omitempty" bson:"value,omitempty"`
	Next       string      `json:"next" bson:"next"`
}

func (cp *ChallengePhase) ExistsContent(identifier string) bool {
	for _, progressContent := range cp.Path {
		if progressContent.Identifier == identifier {
			return true
		}
	}

	return false
}

type ChallengeCard struct {
	Name        string `json:"name" bson:"name"`
	Identifier  string `json:"identifier" bson:"identifier"`
	Description string `json:"description" bson:"description"`
	Type        string `json:"type" bson:"type"`
	Logo        string `json:"logo" bson:"logo"`
	Color       string `json:"color" bson:"color"`
	Order       uint8  `json:"order,omitempty" bson:"order,omitempty"`
	Completed   bool   `json:"completed" bson:"completed"`
	Total       uint8  `json:"total" bson:"total"`
	Available   bool   `json:"available"`
	Current     string `json:"current,omitempty" bson:"current,omitempty"`
	Rewarded    bool   `json:"rewarded" bson:"rewarded"`
}

type ChallengePhaseCard struct {
	Name          string `json:"name" bson:"name"`
	Identifier    string `json:"identifier" bson:"identifier"`
	Logo          string `json:"logo" bson:"logo"`
	Color         string `json:"color" bson:"color"`
	Order         uint8  `json:"order,omitempty" bson:"order,omitempty"`
	Completed     bool   `json:"completed" bson:"completed"`
	Available     bool   `json:"available"`
	Current       string `json:"current,omitempty" bson:"current,omitempty"`
	Rewarded      bool   `json:"rewarded" bson:"rewarded"`
	CurrentPoints int    `json:"currentPoints" bson:"currentPoints"`
}

func (c *Challenge) ToCard(challenge *content.Challenge) *ChallengeCard {
	return &ChallengeCard{
		Name:        challenge.Name,
		Identifier:  challenge.Identifier,
		Type:        "CHALLENGE",
		Description: challenge.Description,
		Logo:        challenge.Logo,
		Color:       challenge.Color,
		Total:       c.Total,
		Completed:   c.Completed,
		Available:   c.Available,
		Current:     c.Current,
		Rewarded:    c.Rewarded,
	}
}

func (cp *ChallengePhase) ToCard(phase *content.ChallengePhase) *ChallengePhaseCard {
	return &ChallengePhaseCard{
		Name:          phase.Name,
		Identifier:    phase.Identifier,
		Logo:          phase.Logo,
		Color:         phase.Color,
		Order:         phase.Order,
		Completed:     cp.Completed,
		Available:     cp.Available,
		Current:       cp.Current,
		Rewarded:      cp.Rewarded,
		CurrentPoints: cp.CurrentPoints,
	}
}

func (c *Challenge) GetPhase(identifier string) *ChallengePhase {
	for _, phase := range c.Phases {
		if phase.Identifier == identifier {
			return phase
		}
	}
	return nil
}

func (c *Challenge) checkPhasesRequirement(phaseContent *content.ChallengePhase, progression *Progression, trailId string) error {
	if phaseContent != nil {
		if len(phaseContent.Requirements) > 0 {
			for _, requirement := range phaseContent.Requirements {
				// First check if the requirement is a challenge phase
				challengePhaseProgression := c.GetPhase(requirement)
				if challengePhaseProgression != nil {
					if !challengePhaseProgression.Completed {
						log.Printf("User tried a challenge phase without completing required phase: %s", requirement)
						return ErrInvalidChallengeProgress
					}
				} else {
					// If not a challenge phase, check if it's a lesson
					lessonProgression := progression.GetLessonProgression(trailId, requirement)
					if lessonProgression == nil || !lessonProgression.Completed {
						log.Printf("User tried a challenge phase without completing required lesson: %s", requirement)
						return ErrInvalidChallengeProgress
					}
				}
			}
		}
	} else {
		return ErrInvalidLesson
	}

	return nil
}

func (c *Challenge) checkPhaseNextProgression(body *ProgressionBody, phaseContent *content.ChallengePhase, challengePhaseProgression *ChallengePhase) error {
	pcContent := phaseContent.GetContent(body.Content)
	if pcContent == nil {
		return ErrInvalidContent
	}

	isChoice := isChallengeContentChoice(pcContent.Choices, body.Choice.Identifier)

	if challengePhaseProgression.Current != body.Choice.Identifier && !isChoice && !challengePhaseProgression.ExistsContent(body.Choice.Identifier) {
		log.Printf("User tried a challenge phase content without requirements (challenge phase content jump) - challenge_phase_progression_current")
		return ErrInvalidChallengePhaseProgress
	}

	if phaseContent.GetNext(body.Content, body.Choice.Identifier) != body.Choice.Next {
		log.Printf("User tried a challenge phase content without requirements (challenge phase content jump) - phase_content_getnext")
		return ErrInvalidChallengePhaseProgress
	}

	return nil
}

func (c *Challenge) ValidateChallengeProgression(body *ProgressionBody, challengeContent *content.Challenge, trailContent *content.Trail, progression *Progression) error {
	if len(c.Phases) <= 0 {
		if !IsFirstPhaseOfChallenge(trailContent.Challenge, body.Module) {
			log.Printf("User tried a challenge content without requirements (challenge phase jump) - is_first_phase_of_challenge_validate_challenge_progression")
			return ErrInvalidChallengePhaseProgress
		}
	} else {
		phaseContent := challengeContent.GetPhase(body.Module)
		challengePhaseProgression := c.GetPhase(body.Module)

		if err := c.checkPhasesRequirement(phaseContent, progression, body.Trail); err != nil {
			return err
		}

		if challengePhaseProgression != nil {
			if err := c.checkPhaseNextProgression(body, phaseContent, challengePhaseProgression); err != nil {
				return err
			}

		}
		// Remove this because was stopping the challenges with more than one phase to progress. Need to be fully tested.
		//else if !IsFirstChallengePhaseContent(phaseContent, body.Choice.Identifier) {
		//	log.Printf("User tried a challenge phase content without requirements (challenge phase content jump) - is_first_challenge_phase_content")
		//	return ErrInvalidChallengePhaseProgress
		//}
	}

	return nil
}
