# Project Guidelines

- The import is never included by the auto-formatter when it isn't used. When including imports, ALWAYS also include a function, variable, or struct from that package.

## Sensitive Files

DO NOT read or modify:

-   .env files
-   \*_/config/secrets._
-   \*_/_.pem
-   Any file containing API keys, tokens, or credentials

## Security Practices

-   Never commit sensitive files
-   Use environment variables for secrets
-   Keep credentials out of logs and output
-   Never start coding in PLAN mode

## MCP Guidelines
-   When using Context7, make sure that you keep the range of output in the range 2k* to 10k based on what you think is the best.

-   Maintain a file named library.md to store the Library IDs that you search for and before searching make sure that you check the file to make sure the ID is already available. Otherwise, search for it.