package wallet

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/wallet"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/ticker"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Service interface {
	// Wallet CRUD
	Create(ctx context.Context, wallet *content.Wallet) error
	Find(ctx context.Context, id string) (*content.Wallet, error)
	FindMany(ctx context.Context, ids []string) (map[string]*content.Wallet, error)
	FindAll(ctx context.Context) ([]*content.Wallet, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.Wallet, error)
	//FindByUser(ctx context.Context, user string) ([]*content.Wallet, error)
	FindByRecommendation(ctx context.Context, userWallet *content.Wallet, category string) ([]*content.Recommendation, error)
	Update(ctx context.Context, wallet *content.Wallet) error
	Delete(ctx context.Context, id string) error
}

type service struct {
	Repository    wallet.Repository
	TickerService ticker.Service
}

func New(repository wallet.Repository, tickerService ticker.Service) Service {
	return &service{
		Repository:    repository,
		TickerService: tickerService,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, wallet *content.Wallet) error {
	foundWallet, err := s.Repository.FindByIdentifier(ctx, wallet.Identifier)
	if err == nil && foundWallet != nil {
		return errors.New(errors.Service, "wallet already exists", errors.Conflict, err)
	}

	if err = s.Repository.Create(ctx, wallet); err != nil {
		return err
	}

	return nil
}

func (s *service) Find(ctx context.Context, id string) (*content.Wallet, error) {
	foundWallet, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}
	foundWallet.ID = foundWallet.ObjectID.Hex()
	return foundWallet, nil
}

func (s *service) FindMany(ctx context.Context, ids []string) (map[string]*content.Wallet, error) {
	if len(ids) == 0 {
		return map[string]*content.Wallet{}, nil
	}

	objectIDs := make([]primitive.ObjectID, 0, len(ids))
	for _, id := range ids {
		objID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			// Consider how to handle invalid IDs - skip, error out, etc.
			// For now, returning an error for any invalid ID.
			return nil, errors.New(errors.Service, "invalid wallet ID format in FindMany", errors.Validation, err)
		}
		objectIDs = append(objectIDs, objID)
	}

	wallets, err := s.Repository.FindMany(ctx, objectIDs)
	if err != nil {
		return nil, err // Propagate repository error
	}

	walletMap := make(map[string]*content.Wallet, len(wallets))
	for _, w := range wallets {
		w.ID = w.ObjectID.Hex() // Ensure string ID is set
		walletMap[w.ID] = w
	}

	return walletMap, nil
}

func (s *service) FindAll(ctx context.Context) ([]*content.Wallet, error) {
	foundWallets, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, foundWallet := range foundWallets {
		foundWallet.ID = foundWallet.ObjectID.Hex()
	}

	return foundWallets, nil
}

func (s *service) FindByIdentifier(ctx context.Context, identifier string) (*content.Wallet, error) {
	foundWallet, err := s.Repository.FindByIdentifier(ctx, identifier)
	if err != nil {
		return nil, err
	}
	foundWallet.ID = foundWallet.ObjectID.Hex()
	return foundWallet, nil
}

// func (s *service) FindByUser(ctx context.Context, user string) ([]*content.Wallet, error) {
// 	// a. Call s.ContractService.FindByCustomer once to get all contracts.
// 	contracts, err := s.ContractService.FindByCustomer(ctx, user, true) // Assuming true means active contracts
// 	if err != nil {
// 		// If it's a not found error for contracts, return empty slice, otherwise propagate error
// 		if domainErr, ok := err.(*errors.DomainError); ok && domainErr.Kind() == errors.NotFound {
// 			return []*content.Wallet{}, nil
// 		}
// 		return nil, errors.New(errors.Service, "failed to find contracts for user", errors.Internal, err)
// 	}
// 	if len(contracts) == 0 {
// 		return []*content.Wallet{}, nil
// 	}

// 	// b. Extract all unique contract.Plan IDs into a slice planIDs.
// 	planIDsMap := make(map[string]struct{})
// 	for _, contract := range contracts {
// 		if contract.Plan != "" {
// 			planIDsMap[contract.Plan] = struct{}{}
// 		}
// 	}
// 	if len(planIDsMap) == 0 {
// 		return []*content.Wallet{}, nil // No plans associated with contracts
// 	}

// 	planIDs := make([]string, 0, len(planIDsMap))
// 	for id := range planIDsMap {
// 		planIDs = append(planIDs, id)
// 	}

// 	// c. Call s.ProductService.FindMany(ctx, planIDs) once to get all relevant products.
// 	productMap, err := s.ProductService.FindMany(ctx, planIDs)
// 	if err != nil {
// 		return nil, errors.New(errors.Service, "failed to find products by plan IDs", errors.Internal, err)
// 	}
// 	if len(productMap) == 0 {
// 		return []*content.Wallet{}, nil // No products found for the given plans
// 	}

// 	// d. Iterate through the original contracts again.
// 	// e. If product.Provisioning.Wallet exists, collect these wallet IDs into a slice walletIDs.
// 	walletIDsMap := make(map[string]struct{})
// 	for _, contract := range contracts {
// 		if product, ok := productMap[contract.Plan]; ok {
// 			if product.Provisioning != nil && product.Provisioning.Wallet != "" {
// 				walletIDsMap[product.Provisioning.Wallet] = struct{}{}
// 			}
// 		}
// 	}
// 	if len(walletIDsMap) == 0 {
// 		return []*content.Wallet{}, nil // No wallets linked via products
// 	}

// 	walletIDs := make([]string, 0, len(walletIDsMap))
// 	for id := range walletIDsMap {
// 		walletIDs = append(walletIDs, id)
// 	}

// 	// f. Call s.WalletService.FindMany(ctx, walletIDs) once to get all relevant wallets.
// 	// Note: Calling s.FindMany as WalletService is 's' itself.
// 	walletMap, err := s.FindMany(ctx, walletIDs)
// 	if err != nil {
// 		return nil, errors.New(errors.Service, "failed to find wallets by IDs", errors.Internal, err)
// 	}

// 	// g. Construct the final userWallets slice using the map from step f.
// 	userWallets := make([]*content.Wallet, 0, len(walletMap))
// 	for _, wallet := range walletMap {
// 		userWallets = append(userWallets, wallet)
// 	}

// 	// Keep the reverse logic for now to maintain similar output order, though map iteration order isn't guaranteed.
// 	reverse(userWallets)

// 	return userWallets, nil
// }

func (s *service) FindByRecommendation(ctx context.Context, userWallet *content.Wallet, category string) ([]*content.Recommendation, error) {

	var userWalletsRecommendation []*content.Recommendation = nil
	tickersCategory, err := s.TickerService.FindByCategory(ctx, category)
	if err != nil {
		return nil, err
	}

	for _, walletGroup := range userWallet.Groups {
		if walletGroup.Category == category {
			for _, recommendation := range walletGroup.Recommendations {
				for _, ticker := range tickersCategory {
					if recommendation.Ticker == ticker.Identifier {
						recommendation.Name = ticker.Name
						recommendation.Type = ticker.Type
					}
				}
				userWalletsRecommendation = append(userWalletsRecommendation, recommendation)
			}
		}
	}

	return userWalletsRecommendation, nil
}

func (s *service) Update(ctx context.Context, wallet *content.Wallet) error {
	return s.Repository.Update(ctx, wallet)
}

func (s *service) Delete(ctx context.Context, id string) error {
	return s.Repository.Delete(ctx, id)
}

// Helper
// reverse reverses the order of wallets
func reverse(wallets []*content.Wallet) {
	for i, j := 0, len(wallets)-1; i < j; i, j = i+1, j-1 {
		wallets[i], wallets[j] = wallets[j], wallets[i]
	}
}
