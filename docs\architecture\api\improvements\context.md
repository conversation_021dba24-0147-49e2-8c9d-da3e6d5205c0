# Context Values Improvements

Caption: This document outlines architectural suggestions for improving the usage of context values in the codebase.
⏳ - Pending
✅ - Completed
🚧 - In Progress

## Context Values - ⏳
Issue: Some request context value are being passed around as parameters leading to unnecessary complexity.
- Each function that needs to do something based on the user requesting it, needs to pass which userId made the request.
- This leads to a lot of boilerplate code and makes it hard to follow the flow of the code.
- Solution: Use context values to pass around the userId and other request-specific values.
- This will reduce the number of parameters in functions and make the code cleaner.
- Use a context key type to avoid collisions with other context values.
- This will also make it easier to add more context values in the future without changing the function signatures.
- Consider using a context package that provides a way to set and get context values in a type-safe manner.
- Example: 
```go
type contextKey string

const userIDKey contextKey = "userID"
func GetUserID(ctx context.Context) (string, bool) {
    userID, ok := ctx.Value(userIDKey).(string)
    return userID, ok
}

func SetUserID(ctx context.Context, userID string) context.Context {
    return context.WithValue(ctx, userIDKey, userID)
}

func Handler(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    userID := r.Header.Get("X-User-ID")
    ctx = SetUserID(ctx, userID)
    // Pass ctx to other functions
}

func SomeFunction(ctx context.Context) {
    userID, ok := GetUserID(ctx)
    if !ok {
        // Handle error
    }
    // Use userID
}
