package firebase

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/firebase"
	_firebase "github.com/dsoplabs/dinbora-backend/internal/repository/firebase"
)

type Service interface {
	// CRUD
	Create(ctx context.Context, userID, fcmToken string) error
	FindByUserID(ctx context.Context, userID string) (*firebase.FCMToken, error)
	FindByToken(ctx context.Context, fcmToken string) (*firebase.FCMToken, error)
	Update(ctx context.Context, userID, fcmToken string) error
	Delete(ctx context.Context, userID string) error
}

type service struct {
	Repository _firebase.Repository
}

func New(repository _firebase.Repository) Service {
	return &service{
		Repository: repository,
	}
}

func (s *service) Create(ctx context.Context, userID, fcmToken string) error {
	existing, err := s.Repository.FindByUserID(ctx, userID)
	if domainErr, ok := err.(*errors.DomainError); ok {
		if domainErr.Kind() != errors.NotFound {
			return err
		}
	}

	if existing != nil {
		if existing.FCMToken != fcmToken {
			return s.Repository.Update(ctx, userID, fcmToken)
		}
	} else {
		return s.Repository.Create(ctx, userID, fcmToken)
	}

	return nil
}

func (s *service) FindByUserID(ctx context.Context, userID string) (*firebase.FCMToken, error) {
	return s.Repository.FindByUserID(ctx, userID)
}

func (s *service) FindByToken(ctx context.Context, fcmToken string) (*firebase.FCMToken, error) {
	return s.Repository.FindByToken(ctx, fcmToken)
}

func (s *service) Update(ctx context.Context, userID, fcmToken string) error {
	return s.Repository.Update(ctx, userID, fcmToken)
}

func (s *service) Delete(ctx context.Context, userID string) error {
	return s.Repository.Delete(ctx, userID)
}
