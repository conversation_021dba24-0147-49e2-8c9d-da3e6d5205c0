package financialdna

import (
	"math"
)

// TreeProgress represents the completion metrics of a family tree
type TreeProgress struct {
	Progress          uint8 `json:"progress"`
	RegisteredMembers uint8 `json:"registeredMembers"`
	Members           uint8 `json:"members"`
}

// CalculateTreeProgress computes the progress metrics for the family tree and updates the TreeProgress field.
func (tree *FinancialDNATree) CalculateTreeProgress() {
	var progress TreeProgress
	totalMembers := len(tree.Members)
	var registeredMembers int

	for _, member := range tree.Members {
		if member.FinancialStatus.IsValid() && member.FinancialStatus != FinancialStatusUndefined {
			registeredMembers++
		}
	}

	progress.Members = uint8(totalMembers)
	progress.RegisteredMembers = uint8(registeredMembers)

	if totalMembers > 0 {
		progress.Progress = uint8(math.Round((float64(registeredMembers) / float64(totalMembers)) * 100))
	} else {
		progress.Progress = 0
	}

	tree.TreeProgress = progress
}

// ProbabilityCategory represents the category of probability for BreakCycles
type ProbabilityCategory string

const (
	// ProbabilityCategoryHigh represents high probability (≥ 70%)
	ProbabilityCategoryHigh ProbabilityCategory = "high"
	// ProbabilityCategoryMedium represents medium probability (40% ≤ x < 70%)
	ProbabilityCategoryMedium ProbabilityCategory = "medium"
	// ProbabilityCategoryLow represents low probability (< 40%)
	ProbabilityCategoryLow ProbabilityCategory = "low"
)

// BreakCycles represents the calculated probability of becoming an investor
type BreakCycles struct {
	Probability uint8               `json:"probability"`
	Category    ProbabilityCategory `json:"category"`
}

// CalculateBreakCycles computes the probability of becoming an investor based on the family tree structure.
func (tree *FinancialDNATree) CalculateBreakCycles() error {
	totalMembers := len(tree.Members)

	const (
		// Weights for each generation (multipliers by degree of relationship)
		parentWeight            = 2.0 // Changed from 0.5 to 2.0
		grandparentsWeight      = 1.0 // Changed from 0.25 to 1.0
		greatGrandparentsWeight = 0.5 // Changed from 0.125 to 0.5
	)

	me := tree.Members[0]

	allParentsGenerations, err := tree.FindAllParents(me.ID)
	if err != nil {
		return err
	}

	score := 0.0
	// Iterate through all parents and their generations and adds in score
	for _, parentMap := range allParentsGenerations {
		for generation, parent := range parentMap {
			switch generation {
			case 1:
				score += getStatusScore(parent.FinancialStatus) * parentWeight
			case 2:
				score += getStatusScore(parent.FinancialStatus) * grandparentsWeight
			case 3:
				score += getStatusScore(parent.FinancialStatus) * greatGrandparentsWeight
			}
		}
	}

	var breakCycles BreakCycles
	if totalMembers > 0 {
		// New formula: 50% + (score * 10%)
		probability := 50.0 + (score * 10.0)

		// Apply limits: minimum 10%, maximum 95%
		if probability < 10.0 {
			probability = 10.0
		} else if probability > 95.0 {
			probability = 95.0
		}

		breakCycles.Probability = uint8(math.Round(probability))
	} else {
		breakCycles.Probability = 0
	}

	// Set the appropriate category based on the probability range
	if breakCycles.Probability >= 70 {
		breakCycles.Category = ProbabilityCategoryHigh
	} else if breakCycles.Probability >= 40 {
		breakCycles.Category = ProbabilityCategoryMedium
	} else {
		breakCycles.Category = ProbabilityCategoryLow
	}

	tree.BreakCycles = breakCycles

	return nil
}

// getStatusScore returns the score for a given financial status
func getStatusScore(status FinancialStatus) float64 {
	switch status {
	case FinancialStatusInvestor:
		return 2.0 // Changed from 1.0 to 2.0
	case FinancialStatusBalanced:
		return 1.0 // Changed from 0.5 to 1.0
	case FinancialStatusIndebted:
		return -1.0 // Changed from 0.25 to -1.0
	case FinancialStatusOverindebted:
		return -2.0 // Changed from 0.0 to -2.0
	default:
		return 0.0 // Default to 0 for unknown status
	}
}

// FinancialDistribution represents the distribution of financial statuses in a family tree
type FinancialDistribution struct {
	Investor  uint8 `json:"investor"`
	Balanced  uint8 `json:"balanced"`
	Indebted  uint8 `json:"indebted"`
	Overindeb uint8 `json:"overindebted"`
}

// CalculateFinancialDistribution computes the distribution of financial statuses in the family tree.
func (tree *FinancialDNATree) CalculateFinancialDistribution() {
	var distribution FinancialDistribution
	totalMembers := len(tree.Members)

	for _, member := range tree.Members {
		switch member.FinancialStatus {
		case FinancialStatusInvestor:
			distribution.Investor++
		case FinancialStatusBalanced:
			distribution.Balanced++
		case FinancialStatusIndebted:
			distribution.Indebted++
		case FinancialStatusOverindebted:
			distribution.Overindeb++
		}
	}

	if totalMembers > 0 {
		distribution.Investor = uint8(math.Round((float64(distribution.Investor) / float64(totalMembers)) * 100))
		distribution.Balanced = uint8(math.Round((float64(distribution.Balanced) / float64(totalMembers)) * 100))
		distribution.Indebted = uint8(math.Round((float64(distribution.Indebted) / float64(totalMembers)) * 100))
		distribution.Overindeb = uint8(math.Round((float64(distribution.Overindeb) / float64(totalMembers)) * 100))
	} else {
		distribution.Investor = 0
		distribution.Balanced = 0
		distribution.Indebted = 0
		distribution.Overindeb = 0
	}

	tree.FinancialDistribution = distribution
}
