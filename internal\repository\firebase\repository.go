package firebase

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/firebase"
)

type Reader interface {
	FindByUserID(ctx context.Context, userID string) (*firebase.FCMToken, error)
	FindByToken(ctx context.Context, fcmToken string) (*firebase.FCMToken, error)
}

type Writer interface {
	Create(ctx context.Context, userID, fcmToken string) error
	Update(ctx context.Context, userID, fcmToken string) error
	Delete(ctx context.Context, userID string) error
}

type Repository interface {
	Reader
	Writer
}
