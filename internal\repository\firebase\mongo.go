package firebase

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"io"
	"log"
	"os"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/firebase"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// mongoDB implements the Repository interface.
type mongoDB struct {
	collection *mongo.Collection
}

// New creates a new instance of the repository.
func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.FCM_TOKENS_COLLECTION),
	}

	// Create unique index on user field
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "userID", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on fcm_tokens.userID field")
		db.Client().Disconnect(context.Background())
	}

	// Create unique index on fcmToken field
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "fcm", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on fcm_tokens.fcmToken field")
		db.Client().Disconnect(context.Background())
	}

	return repo
}

// Create stores a new FCM token for a user.
func (r *mongoDB) Create(ctx context.Context, userID, fcmToken string) error {
	now := time.Now()
	encryptedToken, err := encryptToken(fcmToken)
	if err != nil {
		return err
	}

	_, err = r.collection.InsertOne(ctx, bson.M{
		"userID":    userID,
		"fcm":       encryptedToken,
		"createdAt": now,
		"updatedAt": now,
	})
	return err
}

// Update modifies an existing FCM token for a user.
func (r *mongoDB) Update(ctx context.Context, userID, fcmToken string) error {
	now := time.Now()
	encryptedToken, err := encryptToken(fcmToken)
	if err != nil {
		return err
	}

	filter := bson.M{"userID": userID}
	update := bson.M{
		"$set": bson.M{
			"fcm":       encryptedToken,
			"updatedAt": now,
		},
	}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "no document found to update", errors.NotFound, nil)
	}
	return nil
}

// Delete removes an FCM token for a user.
func (r *mongoDB) Delete(ctx context.Context, userID string) error {
	filter := bson.M{"userID": userID}
	_, err := r.collection.DeleteOne(ctx, filter)
	return err
}

// FindByUserID retrieves an FCM token by user ID.
func (r *mongoDB) FindByUserID(ctx context.Context, userID string) (*firebase.FCMToken, error) {
	filter := bson.M{"userID": userID}
	var result firebase.FCMToken
	err := r.collection.FindOne(ctx, filter).Decode(&result)
	if err != nil {
		return nil, err
	}

	decryptedToken, err := decryptToken(result.FCMToken)
	if err != nil {
		return nil, err
	}
	result.FCMToken = decryptedToken
	return &result, nil
}

// FindByToken retrieves a user by FCM token.
func (r *mongoDB) FindByToken(ctx context.Context, fcmToken string) (*firebase.FCMToken, error) {
	encryptedToken, err := encryptToken(fcmToken)
	if err != nil {
		return nil, err
	}

	filter := bson.M{"fcm": encryptedToken}
	var result firebase.FCMToken
	err = r.collection.FindOne(ctx, filter).Decode(&result)
	if err != nil {
		return nil, err
	}

	decryptedToken, err := decryptToken(result.FCMToken)
	if err != nil {
		return nil, err
	}
	result.FCMToken = decryptedToken
	return &result, nil
}

// encryptToken encrypts the FCM token for secure storage.
func encryptToken(token string) (string, error) {
	key := []byte(os.Getenv("FIREBASE_ENCRYPTION_KEY")) // Replace with a secure key
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(token), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decryptToken decrypts the FCM token for usage.
func decryptToken(token string) (string, error) {
	key := []byte(os.Getenv("FIREBASE_ENCRYPTION_KEY")) // Replace with a secure key
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	data, err := base64.StdEncoding.DecodeString(token)
	if err != nil {
		return "", err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", errors.New(errors.Repository, "ciphertext too short", errors.Internal, nil)
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}
