package financialdna

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// FamilyMember represents a member in the family tree with their relationships and financial status.
type FamilyMember struct {
	ObjectID        primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID              string             `json:"id" bson:"-"`
	Name            string             `json:"name" bson:"name"`
	Icon            FamilyMemberIcon   `json:"icon" bson:"icon"`
	ParentIDs       []string           `json:"parentIds" bson:"parentIds"`
	ChildrenIDs     []string           `json:"childrenIds" bson:"childrenIds"`
	FinancialStatus FinancialStatus    `json:"financialStatus" bson:"financialStatus"`
	CreatedAt       time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt       time.Time          `json:"updatedAt" bson:"updatedAt"`
}

type FamilyMemberIcon string

const CategoryIconCDN = "https://images.dinbora.com.br/dna-financeiro"

const (
	FamilyMemberIconMe               FamilyMemberIcon = CategoryIconCDN + "/eu.png"
	FamilyMemberIconFather           FamilyMemberIcon = CategoryIconCDN + "/pai.png"
	FamilyMemberIconMother           FamilyMemberIcon = CategoryIconCDN + "/mae.png"
	FamilyMemberIconGrandfather      FamilyMemberIcon = CategoryIconCDN + "/avo-masc.png"
	FamilyMemberIconGrandmother      FamilyMemberIcon = CategoryIconCDN + "/avo-fem.png"
	FamilyMemberIconGreatgrandfather FamilyMemberIcon = CategoryIconCDN + "/bisavo-masc.png"
	FamilyMemberIconGreatgrandmother FamilyMemberIcon = CategoryIconCDN + "/bisavo-fem.png"
	FamilyMemberIconChild            FamilyMemberIcon = CategoryIconCDN + "/filho.png"
	FamilyMemberIconGrandChild       FamilyMemberIcon = CategoryIconCDN + "/neto.png"
)
