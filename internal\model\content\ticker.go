package content

import (
	"strings"
	"time"

	"github.com/imdario/mergo"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Ticker struct {
	ObjectID   primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID         string             `json:"_id,omitempty" bson:"-"`
	Name       string             `json:"name" bson:"name"`
	Identifier string             `json:"identifier" bson:"identifier"`
	Group      string             `json:"group" bson:"group"`
	Type       string             `json:"type" bson:"type"`
	Category   string             `json:"category" bson:"category"`
	CreatedAt  time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt  time.Time          `json:"updatedAt" bson:"updatedAt"`
}

func (t *Ticker) Sanitize() *Ticker {
	t.ID = t.ObjectID.Hex()

	return t
}

func (t *Ticker) PrepareCreate() error {
	t.Identifier = strings.TrimSpace(strings.ToUpper(t.Identifier))

	t.CreatedAt = time.Now()
	t.UpdatedAt = t.CreatedAt

	return t.ValidateCreate()
}

func (t *Ticker) ValidateCreate() error {
	if t.Name == "" {
		return ErrTickerRequiredName
	}

	if t.Identifier == "" {
		return ErrTickerRequiredIdentifier
	}

	if t.Type == "" {
		return ErrTickerRequiredType
	}

	if t.Group == "" {
		return ErrTickerRequiredGroup
	}

	if t.Category == "" {
		return ErrTickerRequiredCategory
	}

	return nil
}

func (t *Ticker) PrepareUpdate(newTicker *Ticker) error {
	if err := mergo.Merge(t, newTicker, mergo.WithOverride); err != nil {
		return err
	}

	t.Identifier = strings.TrimSpace(strings.ToUpper(t.Identifier))

	t.UpdatedAt = time.Now()

	if t.ID == "" {
		if !t.ObjectID.IsZero() {
			t.ID = t.ObjectID.Hex()
		} else {
			return ErrTickerInvalidId
		}
	} else {
		tickerObjectId, err := primitive.ObjectIDFromHex(t.ID)
		if err != nil {
			return err
		}
		t.ObjectID = tickerObjectId
	}

	return t.ValidateUpdate()
}

func (t *Ticker) ValidateUpdate() error {
	return t.PrepareCreate()
}
