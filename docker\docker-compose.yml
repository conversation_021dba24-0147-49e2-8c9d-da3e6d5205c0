services:
  dinbora:
    container_name: dinbora-app
    build:
      context: ..
      dockerfile: docker/Dockerfile
    ports:
      - "8080:8080"
    env_file:
      - ../.env
    depends_on:
      dinbora-database:
        condition: service_healthy
      dinbora-redis:
        condition: service_healthy
    networks:
      - backend
    command: ["/app/dinbora"]
    healthcheck:
      #test: ["CMD-SHELL", "wget -q -O - http://localhost:8080/v1/health/ || exit 1"]
      test: ["CMD-SHELL", "/health-check.sh"]
      interval: 1h
      timeout: 2s
      retries: 2
      start_period: 30s  # Gives 10 seconds before starting health checks

  dinbora-database:
    image: mongo:5.0
    container_name: dinbora-mongo
    volumes:
      - dinbora-mongo:/data/db
    ports:
      - 27017-27019:27017-27019
    environment:
      - MONGO_INITDB_DATABASE=dinbora
      - MONGO_INITDB_ROOT_USERNAME=dinbora-mongo-db
      - MONGO_INITDB_ROOT_PASSWORD=secret-db-pass
    networks:
      backend:
        aliases:
          - database
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.runCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  dinbora-redis:
    image: redis:alpine
    container_name: dinbora-redis
    volumes:
      - dinbora-redis:/data
    ports:
      - "6379:6379"
    environment:
      - REDIS_DSN=localhost:6379
    networks:
      backend:
        aliases:
          - dss
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  dinbora-mongo:
  dinbora-redis:

networks:
  backend: