package s3

import (
	"context"
	"fmt"
	"log"
	"mime/multipart"
	"net/url" // Added for URL unescaping
	"path/filepath"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/google/uuid"
)

// UploadFile uploads a file to S3 and returns the URL
func (s *service) UploadFile(ctx context.Context, file *multipart.FileHeader, folder string) (string, error) {
	// Create AWS configuration
	cfg, err := s.getAWSConfig(ctx)
	if err != nil {
		return "", errors.New(errors.Service, "failed to create AWS config", errors.Internal, err)
	}

	// Create S3 client
	client := s3.NewFromConfig(cfg)

	// Open the file
	src, err := file.Open()
	if err != nil {
		return "", errors.New(errors.Service, "failed to open file", errors.Internal, err)
	}
	defer src.Close()

	// Generate a unique filename
	ext := filepath.Ext(file.Filename)
	filename := fmt.Sprintf("%s/%s%s", folder, uuid.New().String(), ext)

	// Upload the file to S3
	_, err = client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:      aws.String(s.bucketName),
		Key:         aws.String(filename),
		Body:        src,
		ContentType: aws.String(file.Header.Get("Content-Type")),
	})
	if err != nil {
		return "", errors.New(errors.Service, "failed to upload file to S3", errors.Internal, err)
	}

	// Generate the URL
	var url string
	if s.endpoint != "" {
		// Custom endpoint (e.g., for local development or non-AWS S3 compatible services)
		url = fmt.Sprintf("%s/%s/%s", s.endpoint, s.bucketName, filename)
	} else {
		// Standard AWS S3 URL
		//url = fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", s.bucketName, s.region, filename)
		// Fixed URL with the CDN
		url = fmt.Sprintf("https://images.dinbora.com.br/%s", filename)
	}

	return url, nil
}

// DeleteFile deletes a file from S3
func (s *service) DeleteFile(ctx context.Context, fileURL string) error {
	// Create AWS configuration
	cfg, err := s.getAWSConfig(ctx)
	if err != nil {
		return errors.New(errors.Service, "failed to create AWS config", errors.Internal, err)
	}

	// Create S3 client
	client := s3.NewFromConfig(cfg)

	// Extract the key from the URL
	key := s.extractKeyFromURL(fileURL)
	if key == "" {
		return errors.New(errors.Service, "invalid file URL", errors.Validation, nil)
	}

	// Delete the file from S3
	_, err = client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(s.bucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		return errors.New(errors.Service, "failed to delete file from S3", errors.Internal, err)
	}

	return nil
}

// getAWSConfig creates an AWS configuration
func (s *service) getAWSConfig(ctx context.Context) (aws.Config, error) {
	// Load AWS credentials from environment variables
	// AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, and AWS_REGION
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion(s.region))
	if err != nil {
		return aws.Config{}, err
	}

	return cfg, nil
}

// extractKeyFromURL extracts the S3 key from a URL
func (s *service) extractKeyFromURL(fileURL string) string {
	var rawObjectKey string

	cloudFrontPrefix := "https://images.dinbora.com.br/"
	s3DirectPrefix := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/", s.bucketName, s.region)

	if strings.HasPrefix(fileURL, cloudFrontPrefix) {
		rawObjectKey = strings.TrimPrefix(fileURL, cloudFrontPrefix)
	} else if strings.HasPrefix(fileURL, s3DirectPrefix) {
		rawObjectKey = strings.TrimPrefix(fileURL, s3DirectPrefix)
	} else if s.endpoint != "" {
		customEndpointPrefix := fmt.Sprintf("%s/%s/", s.endpoint, s.bucketName)
		if strings.HasPrefix(fileURL, customEndpointPrefix) {
			rawObjectKey = strings.TrimPrefix(fileURL, customEndpointPrefix)
		}
	}

	if rawObjectKey == "" {
		return ""
	}

	// URL-decode the object key, as S3 expects the canonical key
	unescapedKey, err := url.PathUnescape(rawObjectKey)
	if err != nil {
		// If unescaping fails, log the error and return the raw key as a fallback.
		// This might still cause issues with S3 if the key contains characters
		// that need to be unescaped, but it's better than returning an empty string.
		log.Printf("Error unescaping object key '%s': %v. Falling back to raw key.", rawObjectKey, err)
		return rawObjectKey
	}

	return unescapedKey
}
