package progression

import "github.com/dsoplabs/dinbora-backend/internal/model/content"

type Trail struct {
	ID                 string     `json:"id" bson:"id"`
	Lessons            []*Lesson  `json:"lessons,omitempty" bson:"lessons,omitempty"`
	Challenge          *Challenge `json:"challenge,omitempty" bson:"challenge,omitempty"`
	Current            string     `json:"current,omitempty" bson:"current,omitempty"`
	Total              uint8      `json:"total" bson:"total,omitempty"`
	Available          bool       `json:"available" bson:"available"`
	LessonsCompleted   bool       `json:"lessonsCompleted" bson:"lessonsCompleted"`
	ChallengeCompleted bool       `json:"challengeCompleted" bson:"challengeCompleted"`
	Rewarded           bool       `json:"rewarded" bson:"rewarded"`
}

type TrailCard struct {
	ID         string `json:"id" bson:"id"`
	Name       string `json:"name" bson:"name"`
	Identifier string `json:"identifier" bson:"identifier"`
	Logo       string `json:"logo" bson:"logo"`
	Level      uint8  `json:"level" bson:"level"`
	Color      string `json:"color" bson:"color"`
	Total      uint8  `json:"total" bson:"total"`
	Available  bool   `json:"available"`
	Current    string `json:"current,omitempty" bson:"current,omitempty"`
}

func (t *Trail) ToCard(content *content.Trail) *TrailCard {
	return &TrailCard{
		ID:         content.ID,
		Name:       content.Name,
		Identifier: content.Identifier,
		Logo:       content.Logo,
		Level:      content.Level,
		Color:      content.Color,
		Total:      t.Total,
		Available:  t.Available,
		Current:    t.Current,
	}
}
