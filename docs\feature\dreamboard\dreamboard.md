## 1. Visão Geral

O Quadro dos Sonhos é uma aplicação web que permite aos usuários gerenciar e acompanhar seus sonhos e objetivos de vida de forma organizada e visual, dividindo-os em diferentes pilares e fornecendo um planejamento financeiro para sua realização.

## 2. Pilares da Vida

A aplicação organiza os sonhos em 10 pilares fundamentais:

1. **Profissional** 💼
    
    - Carreira, desenvolvimento profissional e objetivos de trabalho
    - Cor: Azul (#3B82F6)
2. **Financeiro** 💰
    
    - Metas financeiras, investimentos e patrimônio
    - Cor: Verde (#22C55E)
3. **Lazer** 🎉
    
    - Viagens, hobbies e atividades recreativas
    - Cor: <PERSON><PERSON> (#EAB308)
4. **Emocional** ❤️
    
    - Bem-estar emocional e saúde mental
    - Cor: Vermelho (#EF4444)
5. **Intelectual** 📚
    
    - Educação, aprendizado e desenvolvimento pessoal
    - Cor: <PERSON>ndi<PERSON> (#6366F1)
6. **Espiritual** 🧘‍♀️
    
    - Crescimento espiritual e práticas contemplativas
    - Cor: Roxo (#A855F7)
7. **Físico** 🏋️‍♂️
    
    - Saúde, exercícios e bem-estar físico
    - Cor: Laranja (#F97316)
8. **Íntimo** 💑
    
    - Relacionamentos amorosos e vida pessoal
    - Cor: Rosa (#EC4899)
9. **Social** 🤝
    
    - Amizades e conexões sociais
    - Cor: Teal (#14B8A6)
10. **Familiar** 👨‍👩‍👧
    
    - Relacionamentos familiares e vida em família
    - Cor: Ciano (#06B6D4)

## 3. Funcionalidades Principais

### 3.1. Onboarding

- Tutorial interativo na primeira visita
- Três etapas de apresentação:
    1. Boas-vindas e introdução
    2. Explicação do funcionamento
    3. Chamada para ação

### 3.2. Cadastro de Sonhos

- Processo em três etapas:
    1. **Informações Básicas**
        - Título do sonho
        - Prazo de realização (curto: 6 meses, médio: 1 ano, longo: +1 ano)
    2. **Planejamento Financeiro**
        - Custo estimado
        - Valor mensal para economia
    3. **Fonte dos Recursos**
        - Origem do dinheiro (salário, investimentos, freelance, negócio próprio, venda de bens, outro)
        - Campo personalizado para outras fontes

### 3.3. Visualização dos Sonhos

- Grid de cards organizados por pilar
- Informações exibidas por card:
    - Emoji do pilar
    - Título do sonho
    - Valor total
    - Valor mensal
    - Barra de progresso
    - Tempo estimado para realização
    - Status de conclusão
    - Indicador de prazo excedido

### 3.4. Resumo Financeiro

- Painel com métricas principais:
    - Custo total dos sonhos
    - Valor já guardado
    - Valor necessário por mês
    - Valor restante para atingir o objetivo

## 4. Regras de Negócio

### 4.1. Prazos

- **Curto prazo**: 6 meses
- **Médio prazo**: 1 ano
- **Longo prazo**: mais de 1 ano
- Data limite calculada automaticamente com base no prazo selecionado

### 4.2. Cálculos Financeiros

- Valor total = soma do custo de todos os sonhos
- Valor restante = valor total - valor guardado
- Valor mensal necessário = soma dos valores mensais de cada sonho
- Prazo excedido = tempo necessário > prazo definido

### 4.3. Status dos Sonhos

- **Em andamento**: sonho ainda não realizado
- **Concluído**: sonho marcado como realizado
- **Atrasado**: quando o tempo necessário excede o prazo definido

## 5. Especificações Técnicas

### 5.1. Tecnologias

- React com TypeScript
- Tailwind CSS para estilização
- Lucide React para ícones
- LocalStorage para persistência de dados

### 5.2. Responsividade

- Mobile: 1 coluna
- Tablet: 2 colunas
- Desktop: 3 colunas para cards de sonhos
- Desktop: 5 colunas para pilares

### 5.3. Animações

- Fade in para modais
- Escala em hover nos cards
- Animação de progresso nas barras
- Transições suaves em interações

### 5.4. Persistência

- Armazenamento local dos sonhos
- Controle de primeira visita
- Salvamento automático de alterações

## 6. Métricas de Sucesso

- Número de sonhos cadastrados
- Taxa de conclusão dos sonhos
- Tempo médio para realização
- Engajamento com diferentes pilares
- Retorno dos usuários à plataforma


# Novas Funcionalidades

## 1. Visão Geral

A funcionalidade de Sonhos Compartilhados permite que usuários dividam a realização de um sonho com outras pessoas, distribuindo responsabilidades financeiras e criando uma experiência colaborativa.

## 2. Objetivos

- Permitir que usuários realizem sonhos em conjunto
- Facilitar a divisão de custos entre participantes
- Criar engajamento social na plataforma
- Tornar sonhos mais acessíveis através da colaboração

## 3. Regras de Negócio

### 3.1. Criação do Sonho Compartilhado

- O criador do sonho é automaticamente um participante
- Durante a criação do sonho o criador pode escolher entre compartilhar o sonho ou mantê-lo pessoal
- Caso o criador escolha compartilhar, o sistema deve gerar um link com código único para que seja possível compartilhar com outros usuários
- O link expira em 7 dias
- O usuário que receber o link terá informação do nome do criador, nome do sonho, valor total (custo) do sonho e a duração estimada para realização
- O usuário que receber o link deverá informar a contribuição mensal
- O sistema deve calcular automaticamente a duração estimada para realização com base nas novas contribuições mensais informadas, sendo o valor total do sonhos dividido pelo total de contribuições mensais do criador somado com as contribuições mensais dos participantes
- O sonho só é ativado quando todos os participantes aceitam o convite
- O sonho pode ser cancelado a qualquer momento antes de todos aceitarem o convite somente pelo criador
- O sistema deve informar ao criador e aos participantes o valor total do sonho, meses de duração, o valor arrecadado até o presente momento (dado atualizado pelo Financialsheet system), o valor que falta para o sonho se tornar realidade, a lista dos contribuintes apontando quem é o criador, e aos movimentações de cada um dos participantes (dado atualizado pelo Financialsheet system).
- Na lista dos contribuintes, devemos ter o valor que cada participante se comprometeu a contribuir mensalmente, o valor que ele contribuiu esse mês (dado atualizado pelo Financialsheet system), e a porcentagem e o valor que ele representa no valor que ele se comprometeu a contribuir.
- Na lista dos contribuintes, indicar os participantes que aceitaram, recusaram ou estão aguardando resposta.
- A lista das movimentações de cada um dos participantes deve conter, o nome do participante, o valor que ele contribuiu, a data da contribuição.

### 3.2. Convites e Participação

- Convites são compartilhados pelo criador através de um link gerado pelo sistema.
- Status possíveis do convite:
    - Pendente
    - Aceito
    - Recusado
- O sonho só é ativado quando todos aceitam
- Participantes podem visualizar o sonho

## 4. Fluxo de Criação

1. **Definição do Sonho**
    
    - Nome do sonho
    - Categoria (pilar)
    - Prazo
2. **Planejamento Financeiro**
    
    - Valor total do sonho
    - Valor mensal total
3. **Fonte dos Recursos**
    
    - Origem do dinheiro
    - Detalhes adicionais
4. **Compartilhamento**
    
    - Escolha entre sonho pessoal ou compartilhado
    - Se compartilhado:
        - Adicionar participantes
        - Definir distribuição (valor ou porcentagem)
        - Revisar distribuição

## 5. Validações

### 5.1. Distribuição

- Total de participações = 100%
- Valor total mensal = soma das contribuições
- Emails únicos e válidos
- Mínimo 2 participantes
- Máximo 5 participantes

### 5.2. Financeiras

- Valor mínimo por participante > R$ 0
- Prazo realista baseado nas contribuições
- Valor total atingível com as contribuições mensais

## 6. Métricas de Sucesso

- Taxa de aceitação de convites
- Número médio de participantes por sonho
- Taxa de conclusão de sonhos compartilhados
- Tempo médio para atingir o objetivo
- Satisfação dos usuários

## 7. Considerações de Segurança

- Apenas participantes podem ver detalhes do sonho
- Alterações precisam de aprovação de todos
- Dados financeiros são privados
- Convites expiram em 7 dias

## 8. Próximas Iterações

- Chat entre participantes
- Notificações de progresso
- Histórico de contribuições
- Metas intermediárias
- Compartilhamento em redes sociais
- Modelos de divisão pré-definidos