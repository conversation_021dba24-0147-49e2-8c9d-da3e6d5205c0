package dashboard

import (
	"net/http"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/mock"
)

func (suite *DashboardControllerTestSuite) TestFindFinancialStress_WithNilExpenseAnalysis() {
	// Create a financial stress response with nil ExpenseAnalysis (simulating zero monthly income)
	financialStress := &dashboard.FinancialStress{
		StressScore:        nil,
		CommitmentAnalysis: nil,
		ExpenseAnalysis:    nil, // This is nil when monthly income is 0
		PaymentMethods:     []*dashboard.PaymentMethodSummary{},
		SpendingVariation: &dashboard.SpendingVariation{
			TotalChange: dashboard.VariationDetail{
				Amount:     0,
				Percentage: 0,
				Direction:  "stable",
			},
			FixedChange: dashboard.VariationDetail{
				Amount:     0,
				Percentage: 0,
				Direction:  "stable",
			},
			VariableChange: dashboard.VariationDetail{
				Amount:     0,
				Percentage: 0,
				Direction:  "stable",
			},
			DebtChange: dashboard.VariationDetail{
				Amount:     0,
				Percentage: 0,
				Direction:  "stable",
			},
			TopIncreases:  []*dashboard.MoneySourceVariation{},
			TopReductions: []*dashboard.MoneySourceVariation{},
		},
		Period:       "30d",
		AnalysisDate: time.Now(),
	}

	suite.mockService.On("FindFinancialStress", mock.Anything, suite.testUserID, "30d").Return(financialStress, nil)

	req, rec := suite.createRequestWithAuth("GET", "/dashboard/financialstress?period=30d", nil)
	c := suite.echo.NewContext(req, rec)

	// Execute
	handler := suite.controller.FindFinancialStress()
	err := handler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	// Verify that the response doesn't cause a panic and returns valid JSON
	suite.Contains(rec.Body.String(), `"expenseAnalysis":[]`)
	suite.Contains(rec.Body.String(), `"topIncreases":[]`)
	suite.Contains(rec.Body.String(), `"topReductions":[]`)
	suite.Contains(rec.Body.String(), `"period":"30d"`)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *DashboardControllerTestSuite) TestFindFinancialStress_WithNilSpendingVariation() {
	// Create a financial stress response with nil SpendingVariation
	financialStress := &dashboard.FinancialStress{
		StressScore: &dashboard.StressScore{
			Score:   25.5,
			Level:   "low",
			Label:   "Low Stress",
			Insight: "Your financial situation is stable",
		},
		CommitmentAnalysis: &dashboard.CommitmentAnalysis{
			FixedExpense: dashboard.CommitmentDetail{
				Amount:     monetary.Amount(1000),
				Percentage: 50.0,
			},
			VariableExpense: dashboard.CommitmentDetail{
				Amount:     monetary.Amount(500),
				Percentage: 25.0,
			},
			Debt: dashboard.CommitmentDetail{
				Amount:     monetary.Amount(200),
				Percentage: 10.0,
			},
			AvailableForSavings: monetary.Amount(300),
		},
		ExpenseAnalysis: &dashboard.ExpenseAnalysis{
			Categories: []*dashboard.CategoryBreakdown{
				{
					CategoryIdentifier: "housing",
					CategoryName:       "Housing",
					Icon:               "🏠",
					Amount:             monetary.Amount(800),
					Percentage:         40.0,
				},
			},
		},
		PaymentMethods:    []*dashboard.PaymentMethodSummary{},
		SpendingVariation: nil, // This could potentially be nil
		Period:            "30d",
		AnalysisDate:      time.Now(),
	}

	suite.mockService.On("FindFinancialStress", mock.Anything, suite.testUserID, "30d").Return(financialStress, nil)

	req, rec := suite.createRequestWithAuth("GET", "/dashboard/financialstress?period=30d", nil)
	c := suite.echo.NewContext(req, rec)

	// Execute
	handler := suite.controller.FindFinancialStress()
	err := handler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	// Verify that the response doesn't cause a panic and returns valid JSON
	suite.Contains(rec.Body.String(), `"topIncreases":[]`)
	suite.Contains(rec.Body.String(), `"topReductions":[]`)
	suite.Contains(rec.Body.String(), `"period":"30d"`)

	suite.mockService.AssertExpectations(suite.T())
}
