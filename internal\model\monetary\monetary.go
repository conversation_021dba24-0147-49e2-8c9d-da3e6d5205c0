package monetary

import (
	"encoding/json"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

type Amount int64

// MarshalJSON implements json.Marshaler.
func (m Amount) MarshalJSON() ([]byte, error) {
	return json.Marshal(int64(m))
}

// UnmarshalJSON implements json.Unmarshaler.
func (m *Amount) UnmarshalJSON(data []byte) error {
	var value int64
	if err := json.Unmarshal(data, &value); err != nil {
		return errors.New(errors.Model, "monetary amount must be an integer", errors.Validation, err)
	}

	if value < 0 {
		return errors.New(errors.Model, "monetary amount cannot be negative", errors.Validation, nil)
	}

	*m = Amount(value)
	return nil
}
