package trail

import (
	"net/http"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/labstack/echo/v4"
)

// Tutorial CRUD

func (tc *controller) CreateTutorial() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var tutorial content.Tutorial
		if err := c.Bind(&tutorial); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := tutorial.PrepareCreate(); err != nil {
			return err
		}

		err := tc.Service.CreateTutorial(ctx, &tutorial)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, nil)
	}
}

func (tc *controller) FindTutorial() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		tutorial, err := tc.Service.FindTutorial(ctx, c.Param("id"))
		if err != nil {
			return err
		}

		return c.JSO<PERSON>(http.StatusOK, tutorial.Sanitize())
	}
}

func (tc *controller) FindAllTutorials() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		tutorials, err := tc.Service.FindAllTutorials(ctx)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, tutorials)
	}
}

func (tc *controller) FindTutorialByIdentifier() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		tutorial, err := tc.Service.FindTutorialByIdentifier(ctx, c.Param("identifier"))
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, tutorial.Sanitize())
	}
}

func (tc *controller) FindTutorialByTicker() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var body map[string]interface{}

		if err := c.Bind(&body); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		tickerParam := strings.ToUpper(strings.TrimSpace(body["ticker"].(string)))
		tutorial, err := tc.Service.FindTutorialByTicker(ctx, tickerParam)

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, tutorial.Sanitize())
	}
}

func (tc *controller) UpdateTutorial() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		tutorialID := c.Param("id")

		tutorial := &content.Tutorial{
			ID: tutorialID,
		}
		if err := c.Bind(&tutorial); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := tc.Service.UpdateTutorial(ctx, tutorial); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, tutorial.Sanitize())
	}
}

func (tc *controller) PatchTutorial() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		tutorialID := c.Param("id")

		tutorial, err := tc.Service.FindTutorial(ctx, tutorialID)
		if err != nil {
			return err
		}

		if err := c.Bind(&tutorial); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := tutorial.PrepareUpdate(tutorial); err != nil {
			return err
		}

		if err := tc.Service.UpdateTutorial(ctx, tutorial); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, tutorial.Sanitize())
	}
}

func (tc *controller) DeleteTutorial() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		tutorialID := c.Param("id")

		if err := tc.Service.DeleteTutorial(ctx, tutorialID); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Tutorial Lesson CRUD
func (tc *controller) CreateTutorialLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		tutorialID := c.Param("id")

		var lesson content.Lesson
		if err := c.Bind(&lesson); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := lesson.PrepareCreate(); err != nil {
			return err
		}

		err := tc.Service.CreateTutorialLesson(ctx, tutorialID, &lesson)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, nil)
	}
}

func (tc *controller) FindTutorialLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		tutorialID := c.Param("id")
		lessonID := c.Param("identifier")

		lesson, err := tc.Service.FindTutorialLesson(ctx, tutorialID, lessonID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lesson)
	}
}

func (tc *controller) UpdateTutorialLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		tutorialID := c.Param("id")
		lessonID := c.Param("identifier")

		var lesson content.Lesson
		if err := c.Bind(&lesson); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := lesson.PrepareUpdate(); err != nil {
			return err
		}

		if err := tc.Service.UpdateTutorialLesson(ctx, tutorialID, lessonID, &lesson); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, lesson)
	}
}

func (tc *controller) DeleteTutorialLesson() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		tutorialID := c.Param("id")
		lessonID := c.Param("identifier")

		if err := tc.Service.DeleteTutorialLesson(ctx, tutorialID, lessonID); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}
