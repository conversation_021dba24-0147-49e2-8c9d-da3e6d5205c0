package trail

import (
	"context"
	"fmt"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
)

// Regular Trail CRUD

func (s *service) CreateRegularTrail(ctx context.Context, trail *content.Trail) error {
	identifierKey := trailIdentifierKey(trail.Identifier)
	if _, found := s.Cache.Get(ctx, identifierKey); found {
		return errors.OldError("trail content already exists (cache hit)", errors.Conflict, nil)
	}

	foundContent, err := s.Repository.FindRegularTrailByIdentifier(ctx, trail.Identifier)
	if err == nil && foundContent != nil {
		foundContent.ID = foundContent.ObjectID.Hex()
		idKey := trailIDKey(foundContent.ID)
		_ = s.Cache.Set(ctx, identifierKey, foundContent, 0)
		_ = s.Cache.Set(ctx, idKey, foundContent, 0)
		return errors.OldError("trail content already exists (db hit)", errors.Conflict, err)
	}

	if err = s.Repository.CreateRegularTrail(ctx, trail); err != nil {
		return err
	}

	err = s.Cache.Delete(ctx, trailAllKey)
	if err != nil {
		log.Printf("Error invalidating FindAll cache after create: %v", err)
	} else {
		log.Println("Trail FindAll cache invalidated due to Create.")
	}

	// Invalidate regular trails cache
	err = s.Cache.Delete(ctx, "trail:regular:all")
	if err != nil {
		log.Printf("Error invalidating FindRegularTrails cache after create: %v", err)
	} else {
		log.Println("Regular trails cache invalidated due to Create.")
	}

	return nil
}

func (s *service) FindRegularTrail(ctx context.Context, id string) (*content.Trail, error) {
	cacheKey := fmt.Sprintf("trail:regular:id:%s", id)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if trail, ok := cached.(*content.Trail); ok {
			log.Printf("Cache hit for FindRegularTrail: %s", id)
			return trail, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to *content.Trail failed", cacheKey)
	}

	log.Printf("Cache miss for FindRegularTrail: %s. Fetching from database...", id)
	foundTrail, err := s.Repository.FindRegularTrail(ctx, id)
	if err != nil {
		return nil, err
	}
	foundTrail.ID = foundTrail.ObjectID.Hex()

	errSet := s.Cache.Set(ctx, cacheKey, foundTrail, 0)
	if errSet != nil {
		log.Printf("Error populating cache for regular trail ID %s: %v", id, errSet)
	} else {
		log.Printf("Cache populated for regular trail ID: %s", id)
	}

	return foundTrail, nil
}

func (s *service) FindAllRegularTrails(ctx context.Context) ([]*content.Trail, error) {
	cacheKey := "trail:regular:all"

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if trails, ok := cached.([]*content.Trail); ok {
			log.Println("Cache hit for FindRegularTrails.")
			return trails, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to []*content.Trail failed", cacheKey)
	}

	log.Println("Cache miss for FindAllRegularTrails. Fetching from database...")
	foundTrails, err := s.Repository.FindAllRegularTrails(ctx)
	if err != nil {
		return nil, err
	}

	processedTrails := make([]*content.Trail, len(foundTrails))
	for i, foundTrail := range foundTrails {
		foundTrail.ID = foundTrail.ObjectID.Hex()
		trailCopy := *foundTrail
		processedTrails[i] = &trailCopy
	}

	errSet := s.Cache.Set(ctx, cacheKey, processedTrails, 0)
	if errSet != nil {
		log.Printf("Error populating FindAllRegularTrails cache: %v", errSet)
	} else {
		log.Printf("FindAllRegularTrails Cache populated with %d trails.", len(processedTrails))
	}

	return processedTrails, nil
}

func (s *service) FindRegularTrailByIdentifier(ctx context.Context, identifier string) (*content.Trail, error) {
	cacheKey := trailIdentifierKey(identifier)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if trail, ok := cached.(*content.Trail); ok {
			log.Printf("Cache hit for FindRegularTrailByIdentifier: %s", identifier)
			return trail, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to *content.Trail failed", cacheKey)
	}

	log.Printf("Cache miss for FindRegularTrailByIdentifier: %s. Fetching from database...", identifier)
	foundTrail, err := s.Repository.FindRegularTrailByIdentifier(ctx, identifier)
	if err != nil {
		return nil, err
	}

	foundTrail.ID = foundTrail.ObjectID.Hex()

	errSet := s.Cache.Set(ctx, cacheKey, foundTrail, 0)
	if errSet != nil {
		log.Printf("Error populating FindRegularTrailByIdentifier cache: %v", errSet)
	} else {
		log.Printf("FindRegularTrailByIdentifier Cache populated for identifier: %s", identifier)
	}

	return foundTrail, nil
}

func (s *service) UpdateRegularTrail(ctx context.Context, trail *content.Trail) error {
	err := s.Repository.UpdateRegularTrail(ctx, trail)
	if err != nil {
		return err
	}

	hexID := trail.ObjectID.Hex()
	s.invalidateAllTrailCache(ctx, trail.Identifier, hexID)

	// Also invalidate regular-specific caches
	s.invalidateRegularTrailCache(ctx, hexID)

	return nil
}

func (s *service) DeleteRegularTrail(ctx context.Context, id string) error {
	var identifierToInvalidate string
	cacheKeyID := trailIDKey(id)

	if cached, found := s.Cache.Get(ctx, cacheKeyID); found {
		if trail, ok := cached.(*content.Trail); ok {
			identifierToInvalidate = trail.Identifier
		}
	}

	if identifierToInvalidate == "" {
		log.Printf("Trail ID %s not in cache before delete, fetching for invalidation info.", id)
		dbTrail, err := s.Repository.FindRegularTrail(ctx, id)

		if err != nil {
			if domainErr, ok := err.(*errors.DomainError); ok {
				if domainErr.Kind() != errors.NotFound {
					log.Printf("Error fetching trail %s before delete: %v", id, err)
				}
			} else {
				log.Printf("Unexpected error type fetching trail %s before delete: %v", id, err)
			}
		}

		if dbTrail != nil {
			identifierToInvalidate = dbTrail.Identifier
		}
	}

	err := s.Repository.DeleteRegularTrail(ctx, id)
	if err != nil {
		return err
	}

	if identifierToInvalidate == "" {
		log.Printf("Warning: Could not determine identifier for deleted trail ID %s for full cache invalidation.", id)
	}
	s.invalidateAllTrailCache(ctx, identifierToInvalidate, id)
	s.invalidateRegularTrailCache(ctx, id)

	return nil
}

func (s *service) FindRegularTrailCardData(ctx context.Context, id string) (*content.TrailCard, error) {
	cacheKey := fmt.Sprintf("trail:regular:card:%s", id)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if cardData, ok := cached.(*content.TrailCard); ok {
			log.Println("Cache hit for FindRegularTrailCardData.")
			return cardData, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion failed", cacheKey)
	}

	log.Println("Cache miss for FindRegularTrailCardData. Fetching from database...")
	cardData, err := s.Repository.FindRegularTrailCardData(ctx, id)
	if err != nil {
		return nil, err
	}

	errSet := s.Cache.Set(ctx, cacheKey, cardData, 0)
	if errSet != nil {
		log.Printf("Error populating FindRegularTrailCardData cache: %v", errSet)
	} else {
		log.Printf("FindRegularTrailCardData Cache populated for trail ID: %s", id)
	}

	return cardData, nil
}

func (s *service) FindAllRegularTrailsCardData(ctx context.Context) ([]*content.TrailCard, error) {
	cacheKey := "trail:regular:cards:all"

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if cardDataList, ok := cached.([]*content.TrailCard); ok {
			log.Println("Cache hit for FindAllRegularTrailsCardData.")
			return cardDataList, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion failed", cacheKey)
	}

	log.Println("Cache miss for FindAllRegularTrailsCardData. Fetching from database...")
	cardDataList, err := s.Repository.FindAllRegularTrailsCardData(ctx)
	if err != nil {
		return nil, err
	}

	errSet := s.Cache.Set(ctx, cacheKey, cardDataList, 0)
	if errSet != nil {
		log.Printf("Error populating FindAllRegularTrailsCardData cache: %v", errSet)
	} else {
		log.Printf("FindAllRegularTrailsCardData Cache populated with %d trail cards.", len(cardDataList))
	}

	return cardDataList, nil
}

// Regular Trail Lesson CRUD

func (s *service) CreateRegularLesson(ctx context.Context, trailID string, lesson *content.Lesson) error {
	trail, err := s.FindRegularTrail(ctx, trailID)
	if err != nil {
		return err
	}

	trail.Lessons = append(trail.Lessons, lesson)

	return s.UpdateRegularTrail(ctx, trail)
}

func (s *service) FindRegularLesson(ctx context.Context, trailID string, lessonID string) (*content.Lesson, error) {
	trail, err := s.FindRegularTrail(ctx, trailID)
	if err != nil {
		return nil, err
	}

	for _, lesson := range trail.Lessons {
		if lesson != nil && lesson.Identifier == lessonID {
			return lesson, nil
		}
	}

	return nil, errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
}

func (s *service) UpdateRegularLesson(ctx context.Context, trailID string, lessonID string, newLesson *content.Lesson) error {
	trail, err := s.FindRegularTrail(ctx, trailID)
	if err != nil {
		return err
	}

	found := false
	for i, lesson := range trail.Lessons {
		if lesson != nil && lesson.Identifier == lessonID {
			trail.Lessons[i] = newLesson
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
	}

	return s.UpdateRegularTrail(ctx, trail)
}

func (s *service) DeleteRegularLesson(ctx context.Context, trailID string, lessonID string) error {
	trail, err := s.FindRegularTrail(ctx, trailID)
	if err != nil {
		return err
	}

	found := false
	lessonIndex := -1
	for i, l := range trail.Lessons {
		if l != nil && l.Identifier == lessonID {
			lessonIndex = i
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
	}

	trail.Lessons = append(trail.Lessons[:lessonIndex], trail.Lessons[lessonIndex+1:]...)

	return s.UpdateRegularTrail(ctx, trail)
}

// Helper function to invalidate regular trail caches
func (s *service) invalidateRegularTrailCache(ctx context.Context, id string) {
	// Invalidate regular trails list cache
	errRegularAll := s.Cache.Delete(ctx, "trail:regular:all")
	if errRegularAll != nil {
		log.Printf("Error invalidating regular trails cache: %v", errRegularAll)
	} else {
		log.Println("Regular trails cache invalidated.")
	}

	// Invalidate specific regular trail cache
	regularIDKey := fmt.Sprintf("trail:regular:id:%s", id)
	errRegularID := s.Cache.Delete(ctx, regularIDKey)
	if errRegularID != nil {
		log.Printf("Error invalidating regular trail cache for ID %s: %v", id, errRegularID)
	} else {
		log.Printf("Regular trail cache invalidated for ID: %s", id)
	}

	// Invalidate regular trail cards cache
	errRegularCards := s.Cache.Delete(ctx, "trail:regular:cards:all")
	if errRegularCards != nil {
		log.Printf("Error invalidating regular trail cards cache: %v", errRegularCards)
	} else {
		log.Println("Regular trail cards cache invalidated.")
	}

	// Invalidate specific card cache
	cardKey := fmt.Sprintf("trail:regular:card:%s", id)
	errCard := s.Cache.Delete(ctx, cardKey)
	if errCard != nil {
		log.Printf("Error invalidating regular trail card cache for ID %s: %v", id, errCard)
	} else {
		log.Printf("Regular trail card cache invalidated for ID: %s", id)
	}
}
