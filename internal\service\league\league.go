package league

import (
	"context"
	"encoding/hex"
	"math/rand"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/league"
)

// League CRUD
func (s *service) CreateLeague(ctx context.Context, ownerUserID string, ownerUserName string, ownerPhotoURL string, leagueName string, startDate time.Time, endDate time.Time) (*league.League, error) {
	if leagueName == "" {
		return nil, errors.New(errors.Service, "league_name_required", errors.Validation, nil)
	}
	if ownerUserID == "" {
		return nil, errors.New(errors.Service, "owner_userid_required", errors.Validation, nil)
	}
	if startDate.IsZero() || endDate.IsZero() || endDate.Before(startDate) {
		return nil, errors.New(errors.Service, "invalid_season_dates_for_create", errors.Validation, nil)
	}

	inviteCode, err := generateRandomHex(8) // 16-character hex string
	if err != nil {
		return nil, errors.New(errors.Service, "failed_to_generate_invite_code", errors.Internal, err)
	}

	now := time.Now()

	newLeague := &league.League{
		Name:            leagueName,
		BackgroundColor: pickColor(),
		OwnerUserID:     ownerUserID,
		Code:            inviteCode,
		Members: []league.LeagueMember{
			{
				UserID:                 ownerUserID,
				UserName:               ownerUserName,
				PhotoURL:               ownerPhotoURL,
				JoinedAt:               now,
				TransactionStreak:      0,
				CurrentLeagueLevel:     league.BronzeLevel, // Owner starts at Bronze
				LastStreakActivityDate: time.Time{},        // Zero time
			},
		},
		CurrentSeason: league.Season{
			StartDate: startDate,
			EndDate:   endDate,
		},
		LevelRequirements: defaultLevelRequirements(),
	}

	leagueID, err := s.Repository.Create(ctx, newLeague)
	if err != nil {
		return nil, err // Already an errors.DomainError
	}
	newLeague.ID = leagueID
	return newLeague, nil
}

func (s *service) FindLeague(ctx context.Context, leagueID string, userID string) (*league.League, error) {
	if leagueID == "" {
		return nil, errors.New(errors.Service, "league_id_required", errors.Validation, nil)
	}
	if userID == "" {
		return nil, errors.New(errors.Service, "user_id_required", errors.Validation, nil)
	}
	return s.Repository.FindByIDLoggedUser(ctx, leagueID, userID)
}

func (s *service) FindAllLeagues(ctx context.Context, userID string) ([]*league.League, error) {
	if userID == "" {
		return nil, errors.New(errors.Service, "user_id_required_for_get_user_leagues", errors.Validation, nil)
	}
	return s.Repository.FindAllLeagues(ctx, userID)
}

func (s *service) PatchLeague(ctx context.Context, leagueID string, userID string, name *string) (*league.League, error) {
	// First check if the user has access to the league
	l, err := s.Repository.FindByIDLoggedUser(ctx, leagueID, userID)
	if err != nil {
		return nil, err
	}

	// Then check if the user is the owner (only owners can update)
	if l.OwnerUserID != userID {
		return nil, errors.New(errors.Service, "user_not_league_owner", errors.Forbidden, nil)
	}

	if name != nil {
		l.Name = *name
	}

	if err := s.Repository.Update(ctx, l); err != nil {
		return nil, err
	}
	return l, nil
}

func (s *service) DeleteLeague(ctx context.Context, leagueID string, userID string) error {
	// First check if the user has access to the league
	l, err := s.Repository.FindByIDLoggedUser(ctx, leagueID, userID)
	if err != nil {
		return err
	}

	// Then check if the user is the owner (only owners can delete)
	if l.OwnerUserID != userID {
		return errors.New(errors.Service, "user_not_league_owner_for_delete", errors.Forbidden, nil)
	}
	return s.Repository.Delete(ctx, l.ObjectID)
}

// Helper
// generateRandomHex generates a random hex string of a given byte length
func generateRandomHex(byteLength int) (string, error) {
	bytes := make([]byte, byteLength)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// defaultLevelRequirements provides a standard set of level requirements for a new league.
func defaultLevelRequirements() []league.LevelRequirement {
	return []league.LevelRequirement{
		{Level: league.BronzeLevel, TransactionStreakNeeded: 0, Emoji: "🥉", Color: "#CD7F32", Description: "Bronze Tier"},
		{Level: league.SilverLevel, TransactionStreakNeeded: 30, Emoji: "🥈", Color: "#C0C0C0", Description: "Silver Tier"},
		{Level: league.GoldLevel, TransactionStreakNeeded: 60, Emoji: "🥇", Color: "#FFD700", Description: "Gold Tier"},
		{Level: league.DiamondLevel, TransactionStreakNeeded: 90, Emoji: "💎", Color: "#B9F2FF", Description: "Diamond Tier"},
	}
}

// pickColor provides a random color from a predefined list for a new league.
func pickColor() string {
	colors := []string{"#E4E9FF", "#A4FFE6", "#F6E4FF", "#BEFFE1", "#E0FFF4", "#E6FFE7", "#FFD9D9", "#D0EAFF", "#D3FFDA", "#C7FFF0"}
	return colors[rand.Intn(len(colors))]
}
