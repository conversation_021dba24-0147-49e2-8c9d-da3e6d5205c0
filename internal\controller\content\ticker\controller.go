package ticker

import (
	"context"
	"net/http"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/ticker"

	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	Create() echo.HandlerFunc
	Find() echo.HandlerFunc
	FindByIdentifier() echo.HandlerFunc
	FindByCategory() echo.HandlerFunc
	FindAll() echo.HandlerFunc
	Update() echo.HandlerFunc
	Delete() echo.HandlerFunc
}

type controller struct {
	Service ticker.Service
}

func New(service ticker.Service) Controller {
	return &controller{
		Service: service,
	}
}

// Routes
func (tc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	tickerContentGroup := legacyGroup.Group("content/ticker/")

	// CRUD
	tickerContentGroup.POST("", tc.Create(), middlewares.AuthGuard(), middlewares.AdminGuard())
	tickerContentGroup.GET(":id/", tc.Find())
	tickerContentGroup.POST("findByIdentifier/", tc.FindByIdentifier())
	tickerContentGroup.POST("findByCategory/", tc.FindByCategory())
	tickerContentGroup.GET("findAll/", tc.FindAll())
	tickerContentGroup.PUT(":id/", tc.Update(), middlewares.AuthGuard(), middlewares.AdminGuard())
	tickerContentGroup.DELETE(":id/", tc.Delete(), middlewares.AuthGuard(), middlewares.AdminGuard())
}

// CRUD
func (tc *controller) Create() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var ticker content.Ticker
		if err := c.Bind(&ticker); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := tc.Service.Create(ctx, &ticker); err != nil {
			return err
		}

		createdTicker, err := tc.Service.FindByIdentifier(ctx, ticker.Identifier)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, createdTicker.Sanitize())
	}
}

func (tc *controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		ticker, err := tc.Service.Find(ctx, c.Param("id"))

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, ticker.Sanitize())
	}
}

func (tc *controller) FindByIdentifier() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var body map[string]interface{}

		if err := c.Bind(&body); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		identifierParam := strings.ToLower(strings.TrimSpace(body["identifier"].(string)))
		ticker, err := tc.Service.FindByIdentifier(ctx, identifierParam)

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, ticker.Sanitize())
	}
}

func (tc *controller) FindByCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var body map[string]interface{}

		if err := c.Bind(&body); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		categoryParam := strings.TrimSpace(body["category"].(string))
		category, err := tc.Service.FindByCategory(ctx, categoryParam)

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, category)
	}
}

func (tc *controller) FindAll() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		result, err := tc.Service.FindAll(ctx)
		if err != nil {
			return err
		}

		if result == nil {
			return c.JSON(http.StatusNoContent, nil)
		}

		for _, ticker := range result {
			ticker.Sanitize()
		}

		return c.JSON(http.StatusOK, result)
	}
}

func (tc *controller) Update() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		ticker, err := tc.Service.Find(ctx, sanitizeString(c.Param("id")))
		if err != nil {
			return err
		}

		var newTicker content.Ticker
		if err = c.Bind(&newTicker); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err = ticker.PrepareUpdate(&newTicker); err != nil {
			return err
		}

		if err = tc.Service.Update(ctx, ticker); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, ticker.Sanitize())
	}
}

func (tc *controller) Delete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		if err := tc.Service.Delete(ctx, sanitizeString(c.Param("id"))); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Helper
func sanitizeString(str string) string {
	return strings.ToLower(strings.TrimSpace(str))
}
