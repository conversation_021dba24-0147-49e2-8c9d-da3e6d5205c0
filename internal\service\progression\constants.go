package progression

// Reward amounts
const (
	// LessonCompletionReward is the number of coins awarded for completing a lesson
	LessonCompletionReward = 1

	// ChallengeCompletionReward is the number of coins awarded for completing a challenge
	ChallengeCompletionReward = 10

	// TrailCompletionReward is the number of coins awarded for completing a trail
	TrailCompletionReward = 1
)

// Minimum progress thresholds
const (
	// TrailCompletionThreshold is the minimum percentage required to consider a trail complete
	TrailCompletionThreshold = 100

	// TrailRequirementThreshold is the minimum percentage required to unlock a trail
	TrailRequirementThreshold = 90
)
