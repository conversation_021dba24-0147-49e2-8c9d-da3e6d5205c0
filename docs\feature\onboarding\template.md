# Upload de Fotos no Onboarding
**Solicitante**: <PERSON><PERSON>/UX
**Data**: 16/05/2025
**Prioridade**: Alta

**Persona Alvo**: Novos usuários durante o processo de onboarding que desejam personalizar seus perfis.
**Custo <PERSON>st<PERSON>**: [A ser definido]
**Prazo Desejado**: [A ser definido]

---

## **1. Objetivo**  
*Por que essa feature é necessária? Qual problema ela resolve?*  
Permitir que novos usuários personalizem seus perfis fazendo upload de fotos durante o processo de onboarding, visando aumentar o engajamento inicial e a sensação de pertencimento à plataforma.

---

## **2. Descrição da Feature**  
*Detalhe o que será implementado:*  
- **Funcionalidades principais**:
  - Interface para seleção e upload de arquivo de imagem (JPG, PNG).
  - Validação do tipo de arquivo e tamanho (ex: máximo de 5MB).
  - Preview da imagem selecionada antes do upload definitivo.
  - Armazenamento seguro da imagem (ex: S3).
  - Associação da imagem ao perfil do usuário.
  - Opção para o usuário pular esta etapa.
- **Fluxos do usuário**:
  1. Usuário chega à etapa de "Foto de Perfil" no fluxo de onboarding.
  2. Usuário visualiza a opção de fazer upload de uma foto ou pular.
  3. Se optar pelo upload, clica no botão "Fazer Upload" ou arrasta uma imagem para a área designada.
  4. Sistema abre o seletor de arquivos ou aceita o arquivo arrastado.
  5. Usuário seleciona uma imagem.
  6. Sistema exibe um preview da imagem. O usuário pode optar por trocar a imagem.
  7. Usuário confirma o upload.
  8. Sistema valida (formato, tamanho) e processa o upload para o S3.
  9. URL da imagem é associada ao perfil do usuário no banco de dados.
  10. Usuário prossegue no onboarding com a foto de perfil atualizada ou sem foto, caso tenha pulado.
- **Telas/interfaces envolvidas**:
  - Etapa específica de "Foto de Perfil" na tela de onboarding.
  - Componente de upload de imagem com preview.

---

## **3. Requisitos Técnicos**  
*Informações para a equipe de desenvolvimento:*  
- **APIs externas necessárias**:
  - AWS S3 (ou similar) para armazenamento de imagens.
- **Bibliotecas ou frameworks a serem usados**:
  - Frontend: Biblioteca/componente para manipulação de upload e preview de imagem.
  - Backend: AWS SDK para Go para interagir com S3.
- **Restrições de performance ou segurança**:
  - Upload deve ser eficiente e não bloquear o fluxo do usuário.
  - Armazenamento seguro das imagens.
  - Proteção contra upload de arquivos maliciosos (validação de tipo e conteúdo, se possível).
  - Limitar o tamanho do arquivo para evitar abuso de armazenamento e longos tempos de upload.

---

## **4. Critérios de Aceitação**  
*Condições que definem se a feature está pronta:*  
- [ ] Usuário consegue fazer upload de uma imagem JPG (ex: < 5MB) com sucesso.
- [ ] Usuário consegue fazer upload de uma imagem PNG (ex: < 5MB) com sucesso.
- [ ] Sistema rejeita arquivos que não são JPG/PNG, exibindo mensagem clara.
- [ ] Sistema rejeita arquivos maiores que o limite definido (ex: 5MB), exibindo mensagem clara.
- [ ] Preview da imagem é exibido corretamente antes da confirmação do upload.
- [ ] Usuário pode trocar a imagem selecionada no preview antes de confirmar.
- [ ] Imagem é corretamente armazenada no S3.
- [ ] URL da imagem é corretamente associada ao perfil do usuário no banco de dados.
- [ ] Erros durante o upload (ex: falha de rede, erro no S3) são comunicados claramente ao usuário.
- [ ] Usuário consegue pular a etapa de upload de foto e continuar o onboarding.
- [ ] Se o usuário pular, nenhuma foto é associada ao perfil.

---

## **5. Impacto Esperado**  
*Métricas ou resultados esperados:*  
- Aumentar a taxa de conclusão do onboarding em X%.
- Aumentar o percentual de perfis com foto em Y%.
- Melhorar o engajamento inicial dos usuários, medido por primeiras ações pós-onboarding.

---

## **6. Dependências**  
*O que precisa ser resolvido antes de iniciar?*  
- Configuração do bucket S3 e credenciais de acesso para o backend.
- Definição da estrutura no modelo de usuário para armazenar a URL da foto de perfil.
- API no backend para receber o arquivo, processar e salvar a URL no perfil do usuário.

---

## **7. Riscos Técnicos**
*Quais os tipos de riscos técnicos devemos avaliar?*  
- Complexidade na manipulação de uploads de arquivos no frontend (compatibilidade entre navegadores).
- Segurança no tratamento de arquivos enviados por usuários (evitar XSS ou outros ataques).
- Performance do processo de upload e redimensionamento (se aplicável) em conexões lentas.
- Custos de armazenamento e transferência de dados no S3.

---

## **8. Plano de Testes**
*Descreva o o planejamento de testes.*  
- **Testes Unitários**:
  - Validação de formato e tamanho do arquivo no backend.
  - Lógica de upload para S3.
  - Atualização do perfil do usuário.
- **Testes de Integração**:
  - Fluxo completo de upload do frontend para o backend e S3.
  - API de atualização do perfil do usuário com a URL da imagem.
- **Testes E2E**:
  - Simular o fluxo de onboarding completo, incluindo o upload (com sucesso e falha) e o ato de pular a etapa.
  - Verificar se a foto aparece no perfil do usuário após o onboarding.
- **Cenários de teste**:
  - Upload com sucesso: JPG, PNG (dentro dos limites).
  - Falha no upload: formato inválido, tamanho excedido, arquivo corrompido.
  - Erros de rede durante o upload.
  - Pular etapa de upload.
  - Trocar imagem no preview.
- **Dados de teste necessários**:
  - Imagens de teste: JPGs e PNGs válidos (pequenos, médios, no limite de tamanho).
  - Arquivos inválidos: GIFs, TXTs, arquivos executáveis.
  - Imagens válidas, mas acima do limite de tamanho.

---

## **9. Observações**  
*Links úteis, referências ou contexto adicional.*  
- Considerar a implementação de uma funcionalidade de recorte/redimensionamento básico no frontend.
- Definir política de retenção ou moderação de imagens, se aplicável no futuro.
- Referência: Funcionalidade de upload de avatar em `internal/controller/auth/controller.go` (V2 Register).
