package errors

import "fmt"

type DomainError struct {
	layer          Layer
	message        Message
	translationKey string
	kind           Kind
	original       error
}

func OldError(message Message, kind Kind, original error) *DomainError {
	return &DomainError{
		message:        message,
		translationKey: string(message), // Use message as translation key for backward compatibility
		kind:           kind,
		original:       original,
	}
}

func New(layer Layer, message Message, kind Kind, original error) *DomainError {
	return &DomainError{
		layer:          layer,
		message:        message,
		translationKey: string(message), // Use message as translation key for backward compatibility
		kind:           kind,
		original:       original,
	}
}

// NewWithTranslationKey creates a new DomainError with a specific translation key
func NewWithTranslationKey(layer Layer, message Message, translationKey string, kind Kind, original error) *DomainError {
	return &DomainError{
		layer:          layer,
		message:        message,
		translationKey: translationKey,
		kind:           kind,
		original:       original,
	}
}

func (e *DomainError) Error() string {
	if e.original != nil {
		return fmt.Sprintf("%s: %s (kind:%s) (cause: %v)", e.layer, e.message, e.kind, e.original)
	}
	return fmt.Sprintf("%s: %s (kind:%s)", e.layer, e.message, e.kind)
}

func (d *DomainError) Layer() Layer {
	return d.layer
}

func (d *DomainError) Kind() Kind {
	return d.kind
}

func (d *DomainError) Unwrap() error {
	return d.original
}

func (d *DomainError) TranslationKey() string {
	return d.translationKey
}

func (d *Message) GetMessage() string {
	return string(*d)
}

// Helper functions for creating errors with translation keys

// NewUserError creates a user-related error with translation key
func NewUserError(layer Layer, message Message, translationKey string, kind Kind, original error) *DomainError {
	return NewWithTranslationKey(layer, message, translationKey, kind, original)
}

// NewAuthError creates an authentication-related error with translation key
func NewAuthError(layer Layer, message Message, translationKey string, kind Kind, original error) *DomainError {
	return NewWithTranslationKey(layer, message, translationKey, kind, original)
}

// NewValidationError creates a validation error with translation key
func NewValidationError(layer Layer, message Message, translationKey string, original error) *DomainError {
	return NewWithTranslationKey(layer, message, translationKey, Validation, original)
}

// NewNotFoundError creates a not found error with translation key
func NewNotFoundError(layer Layer, message Message, translationKey string, original error) *DomainError {
	return NewWithTranslationKey(layer, message, translationKey, NotFound, original)
}

// NewConflictError creates a conflict error with translation key
func NewConflictError(layer Layer, message Message, translationKey string, original error) *DomainError {
	return NewWithTranslationKey(layer, message, translationKey, Conflict, original)
}

// NewInternalError creates an internal error with translation key
func NewInternalError(layer Layer, message Message, translationKey string, original error) *DomainError {
	return NewWithTranslationKey(layer, message, translationKey, Internal, original)
}

// NewUnauthorizedError creates an unauthorized error with translation key
func NewUnauthorizedError(layer Layer, message Message, translationKey string, original error) *DomainError {
	return NewWithTranslationKey(layer, message, translationKey, Unauthorized, original)
}

// NewForbiddenError creates a forbidden error with translation key
func NewForbiddenError(layer Layer, message Message, translationKey string, original error) *DomainError {
	return NewWithTranslationKey(layer, message, translationKey, Forbidden, original)
}
