package cache

import (
	"context"
	"time"
)

// CacheService defines the interface for a generic cache.
type CacheService interface {
	// Get retrieves an item from the cache.
	// It returns the item or nil, and a boolean indicating whether the key was found.
	Get(ctx context.Context, key string) (interface{}, bool)

	// Set adds an item to the cache with an expiration duration.
	// If duration is 0, the item never expires (or uses default expiration).
	Set(ctx context.Context, key string, value interface{}, duration time.Duration) error

	// Delete removes an item from the cache.
	Delete(ctx context.Context, key string) error

	// // Invalidate removes items based on a pattern or tag (optional, can be added later).
	// Invalidate(ctx context.Context, pattern string) error

	// // Clear removes all items from the cache (optional).
	// Clear(ctx context.Context) error
}
