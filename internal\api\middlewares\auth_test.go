package middlewares

import (
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"github.com/labstack/echo/v4"
)

func TestMain(m *testing.M) {
	// Set up test environment variables
	os.Setenv("API_ACCESS_JWT_KEY", "test-jwt-key")
	os.Setenv("API_REFRESH_JWT_KEY", "test-refresh-jwt-key")

	// Run tests
	code := m.Run()

	// Clean up
	os.Unsetenv("API_ACCESS_JWT_KEY")
	os.Unsetenv("API_REFRESH_JWT_KEY")

	os.Exit(code)
}

func createTestToken(userID string, roles []string) (string, error) {
	user := &model.User{
		ID:    userID,
		Roles: roles,
	}

	tokens, err := token.Create(user)
	if err != nil {
		return "", err
	}

	return tokens.Access, nil
}

func TestUserContextMiddleware(t *testing.T) {
	e := echo.New()

	// Create test token
	testToken, err := createTestToken("test-user-123", []string{"teacher", "student"})
	if err != nil {
		t.Fatalf("Failed to create test token: %v", err)
	}

	req := httptest.NewRequest(http.MethodGet, "/test", nil)
	req.Header.Set("Authorization", "Bearer "+testToken)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	// First apply AuthGuard to validate the token
	authMiddleware := AuthGuard()
	userContextMiddleware := UserContextMiddleware()

	handler := func(c echo.Context) error {
		// Test that user context is available
		userCtx, err := GetUserContext(c)
		if err != nil {
			return err
		}

		if userCtx.UserID != "test-user-123" {
			t.Errorf("Expected UserID to be 'test-user-123', got %s", userCtx.UserID)
		}

		if len(userCtx.Roles) != 2 {
			t.Errorf("Expected 2 roles, got %d", len(userCtx.Roles))
		}

		// Test individual context getters
		userID, err := GetUserID(c)
		if err != nil {
			return err
		}
		if userID != "test-user-123" {
			t.Errorf("GetUserID() = %s, want 'test-user-123'", userID)
		}

		roles, err := GetUserRoles(c)
		if err != nil {
			return err
		}
		if len(roles) != 2 {
			t.Errorf("GetUserRoles() returned %d roles, want 2", len(roles))
		}

		return c.String(http.StatusOK, "OK")
	}

	// Chain middlewares
	chainedHandler := authMiddleware(userContextMiddleware(handler))

	err = chainedHandler(c)
	if err != nil {
		t.Fatalf("Middleware chain failed: %v", err)
	}

	if rec.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", rec.Code)
	}
}

func TestRoleGuardWithRequiredRoles(t *testing.T) {
	e := echo.New()

	tests := []struct {
		name           string
		userRoles      []string
		requiredRoles  []auth.Role
		expectedStatus int
	}{
		{
			"Admin access with admin requirement",
			[]string{"admin"},
			[]auth.Role{auth.RoleAdmin},
			http.StatusOK,
		},
		{
			"Teacher access with teacher requirement",
			[]string{"teacher"},
			[]auth.Role{auth.RoleTeacher},
			http.StatusOK,
		},
		{
			"Teacher access with admin requirement (should fail)",
			[]string{"teacher"},
			[]auth.Role{auth.RoleAdmin},
			http.StatusForbidden,
		},
		{
			"Multiple roles with OR logic",
			[]string{"teacher"},
			[]auth.Role{auth.RoleAdmin, auth.RoleTeacher},
			http.StatusOK,
		},
		{
			"No matching roles",
			[]string{"student"},
			[]auth.Role{auth.RoleAdmin, auth.RoleTeacher},
			http.StatusForbidden,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test token
			testToken, err := createTestToken("test-user", tt.userRoles)
			if err != nil {
				t.Fatalf("Failed to create test token: %v", err)
			}

			req := httptest.NewRequest(http.MethodGet, "/test", nil)
			req.Header.Set("Authorization", "Bearer "+testToken)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			// Create role guard middleware
			roleGuard := RoleGuard(RoleGuardConfig{
				RequiredRoles: tt.requiredRoles,
			})

			handler := func(c echo.Context) error {
				return c.String(http.StatusOK, "OK")
			}

			// Chain middlewares: AuthGuard -> UserContext -> RoleGuard -> Handler
			chainedHandler := AuthGuard()(UserContextMiddleware()(roleGuard(handler)))

			err = chainedHandler(c)

			if tt.expectedStatus == http.StatusForbidden {
				if err == nil {
					t.Error("Expected error for forbidden access, got nil")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if rec.Code != tt.expectedStatus {
					t.Errorf("Expected status %d, got %d", tt.expectedStatus, rec.Code)
				}
			}
		})
	}
}

func TestRoleGuardWithMinimumRole(t *testing.T) {
	e := echo.New()

	tests := []struct {
		name           string
		userRoles      []string
		minimumRole    auth.Role
		expectedStatus int
	}{
		{
			"Admin meets director minimum",
			[]string{"admin"},
			auth.RoleDirector,
			http.StatusOK,
		},
		{
			"Director meets teacher minimum",
			[]string{"director"},
			auth.RoleTeacher,
			http.StatusOK,
		},
		{
			"Teacher meets student minimum",
			[]string{"teacher"},
			auth.RoleStudent,
			http.StatusOK,
		},
		{
			"Student does not meet teacher minimum",
			[]string{"student"},
			auth.RoleTeacher,
			http.StatusForbidden,
		},
		{
			"HR meets teacher minimum",
			[]string{"human_resources"},
			auth.RoleTeacher,
			http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test token
			testToken, err := createTestToken("test-user", tt.userRoles)
			if err != nil {
				t.Fatalf("Failed to create test token: %v", err)
			}

			req := httptest.NewRequest(http.MethodGet, "/test", nil)
			req.Header.Set("Authorization", "Bearer "+testToken)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			// Create role guard middleware
			roleGuard := RoleGuard(RoleGuardConfig{
				MinimumRole: tt.minimumRole,
			})

			handler := func(c echo.Context) error {
				return c.String(http.StatusOK, "OK")
			}

			// Chain middlewares
			chainedHandler := AuthGuard()(UserContextMiddleware()(roleGuard(handler)))

			err = chainedHandler(c)

			if tt.expectedStatus == http.StatusForbidden {
				if err == nil {
					t.Error("Expected error for forbidden access, got nil")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if rec.Code != tt.expectedStatus {
					t.Errorf("Expected status %d, got %d", tt.expectedStatus, rec.Code)
				}
			}
		})
	}
}

func TestRoleGuardWithSelfAccess(t *testing.T) {
	e := echo.New()

	tests := []struct {
		name           string
		userID         string
		paramValue     string
		userRoles      []string
		requiredRoles  []auth.Role
		allowSelf      bool
		expectedStatus int
	}{
		{
			"Self access allowed",
			"user-123",
			"user-123",
			[]string{"student"},
			[]auth.Role{auth.RoleAdmin},
			true,
			http.StatusOK,
		},
		{
			"Self access not allowed",
			"user-123",
			"user-123",
			[]string{"student"},
			[]auth.Role{auth.RoleAdmin},
			false,
			http.StatusForbidden,
		},
		{
			"Different user access denied",
			"user-123",
			"user-456",
			[]string{"student"},
			[]auth.Role{auth.RoleAdmin},
			true,
			http.StatusForbidden,
		},
		{
			"Admin access to other user",
			"user-123",
			"user-456",
			[]string{"admin"},
			[]auth.Role{auth.RoleAdmin},
			true,
			http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test token
			testToken, err := createTestToken(tt.userID, tt.userRoles)
			if err != nil {
				t.Fatalf("Failed to create test token: %v", err)
			}

			req := httptest.NewRequest(http.MethodGet, "/test/"+tt.paramValue, nil)
			req.Header.Set("Authorization", "Bearer "+testToken)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)
			c.SetParamNames("id")
			c.SetParamValues(tt.paramValue)

			// Create role guard middleware
			roleGuard := RoleGuard(RoleGuardConfig{
				RequiredRoles: tt.requiredRoles,
				AllowSelf:     tt.allowSelf,
				SelfParamName: "id",
			})

			handler := func(c echo.Context) error {
				return c.String(http.StatusOK, "OK")
			}

			// Chain middlewares
			chainedHandler := AuthGuard()(UserContextMiddleware()(roleGuard(handler)))

			err = chainedHandler(c)

			if tt.expectedStatus == http.StatusForbidden {
				if err == nil {
					t.Error("Expected error for forbidden access, got nil")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if rec.Code != tt.expectedStatus {
					t.Errorf("Expected status %d, got %d", tt.expectedStatus, rec.Code)
				}
			}
		})
	}
}

func TestConvenienceMiddlewares(t *testing.T) {
	e := echo.New()

	tests := []struct {
		name           string
		middleware     echo.MiddlewareFunc
		userRoles      []string
		expectedStatus int
	}{
		{"AdminOnly with admin", AdminOnly(), []string{"admin"}, http.StatusOK},
		{"AdminOnly with teacher", AdminOnly(), []string{"teacher"}, http.StatusForbidden},
		{"DirectorOrAbove with admin", DirectorOrAbove(), []string{"admin"}, http.StatusOK},
		{"DirectorOrAbove with director", DirectorOrAbove(), []string{"director"}, http.StatusOK},
		{"DirectorOrAbove with teacher", DirectorOrAbove(), []string{"teacher"}, http.StatusForbidden},
		{"TeacherOrAbove with teacher", TeacherOrAbove(), []string{"teacher"}, http.StatusOK},
		{"TeacherOrAbove with student", TeacherOrAbove(), []string{"student"}, http.StatusForbidden},
		{"HROnly with HR", HROnly(), []string{"human_resources"}, http.StatusOK},
		{"HROnly with company HR", HROnly(), []string{"hr_company_123"}, http.StatusOK},
		{"HROnly with teacher", HROnly(), []string{"teacher"}, http.StatusForbidden},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test token
			testToken, err := createTestToken("test-user", tt.userRoles)
			if err != nil {
				t.Fatalf("Failed to create test token: %v", err)
			}

			req := httptest.NewRequest(http.MethodGet, "/test", nil)
			req.Header.Set("Authorization", "Bearer "+testToken)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			handler := func(c echo.Context) error {
				return c.String(http.StatusOK, "OK")
			}

			// Chain middlewares
			chainedHandler := AuthGuard()(UserContextMiddleware()(tt.middleware(handler)))

			err = chainedHandler(c)

			if tt.expectedStatus == http.StatusForbidden {
				if err == nil {
					t.Error("Expected error for forbidden access, got nil")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if rec.Code != tt.expectedStatus {
					t.Errorf("Expected status %d, got %d", tt.expectedStatus, rec.Code)
				}
			}
		})
	}
}
