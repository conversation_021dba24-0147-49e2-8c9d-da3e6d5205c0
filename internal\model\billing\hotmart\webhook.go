package hotmart

import (
	"encoding/json"
	"time"
)

type Envelope struct {
	ID           string          `json:"id"`
	CreationDate int64           `json:"creation_date"`
	Event        string          `json:"event"`
	Version      string          `json:"version"`
	Data         json.RawMessage `json:"data"` // parse depois conforme Event
}

// ── STRUCTS ESPECÍFICOS ──────────────────────────────────────────────
// 1) PURCHASE_APPROVED   (exemplo encurtado)
type PurchaseData struct {
	Product      *Product          `json:"product,omitempty"`
	Affiliates   []Affiliate       `json:"affiliates,omitempty"`
	Buyer        *Buyer            `json:"buyer,omitempty"`
	Producer     *Producer         `json:"producer,omitempty"`
	Commissions  []Commission      `json:"commissions,omitempty"`
	Purchase     *Purchase         `json:"purchase,omitempty"`
	Subscription *SubscriptionInfo `json:"subscription,omitempty"`
}

// 2) SUBSCRIPTION_CANCELLATION (outro exemplo)
type SubscriptionCancellationData struct {
	DateNextCharge   int64           `json:"date_next_charge"`
	Product          ProductBasic    `json:"product"`
	ActualRecurrence json.Number     `json:"actual_recurrence_value"`
	Subscriber       Subscriber      `json:"subscriber"`
	Subscription     SubscriptionRef `json:"subscription"`
	CancellationDate int64           `json:"cancellation_date"`
}

// ── SUB‑TYPES REUTILIZÁVEIS ──────────────────────────────────────────
type Product struct {
	ID                int    `json:"id"`
	Ucode             string `json:"ucode"`
	Name              string `json:"name"`
	HasCoProduction   bool   `json:"has_co_production"`
	WarrantyDate      string `json:"warranty_date"`
	SupportEmail      string `json:"support_email"`
	IsPhysicalProduct bool   `json:"is_physical_product"`
	Content           *struct {
		HasPhysicalProducts bool       `json:"has_physical_products"`
		Products            []ProdItem `json:"products"`
	} `json:"content,omitempty"`
}

type ProdItem struct {
	ID                int    `json:"id"`
	Ucode             string `json:"ucode"`
	Name              string `json:"name"`
	IsPhysicalProduct bool   `json:"is_physical_product"`
}

type Affiliate struct {
	AffiliateCode string `json:"affiliate_code"`
	Name          string `json:"name"`
}

type Buyer struct {
	Email             string   `json:"email"`
	Name              string   `json:"name"`
	FirstName         string   `json:"first_name"`
	LastName          string   `json:"last_name"`
	CheckoutPhone     string   `json:"checkout_phone"`
	CheckoutPhoneCode string   `json:"checkout_phone_code"`
	Document          string   `json:"document"`
	DocumentType      string   `json:"document_type"`
	Address           *Address `json:"address,omitempty"`
}

type Address struct {
	Zipcode      string `json:"zipcode"`
	Country      string `json:"country"`
	Number       string `json:"number"`
	Address      string `json:"address"`
	City         string `json:"city"`
	State        string `json:"state"`
	Neighborhood string `json:"neighborhood"`
	Complement   string `json:"complement"`
	CountryISO   string `json:"country_iso"`
}

type Producer struct {
	Name        string `json:"name"`
	LegalNature string `json:"legal_nature"`
	Document    string `json:"document"`
}

type Commission struct {
	Value              json.Number `json:"value"`
	CurrencyValue      string      `json:"currency_value"`
	Source             string      `json:"source"`
	CurrencyConversion *struct {
		ConvertedValue      json.Number `json:"converted_value"`
		ConvertedToCurrency string      `json:"converted_to_currency"`
		ConversionRate      json.Number `json:"conversion_rate"`
	} `json:"currency_conversion,omitempty"`
}

type Purchase struct {
	ApprovedDate                     int64         `json:"approved_date"`
	FullPrice                        Money         `json:"full_price"`
	OriginalOfferPrice               Money         `json:"original_offer_price"`
	Price                            Money         `json:"price"`
	Offer                            *Offer        `json:"offer,omitempty"`
	RecurrenceNumber                 int           `json:"recurrence_number"`
	SubscriptionAnticipationPurchase bool          `json:"subscription_anticipation_purchase"`
	CheckoutCountry                  Country       `json:"checkout_country"`
	Origin                           Origin        `json:"origin"`
	OrderBump                        *OrderBump    `json:"order_bump,omitempty"`
	OrderDate                        int64         `json:"order_date"`
	DateNextCharge                   int64         `json:"date_next_charge"`
	Status                           string        `json:"status"`
	Transaction                      string        `json:"transaction"`
	Payment                          *Payment      `json:"payment,omitempty"`
	IsFunnel                         bool          `json:"is_funnel"`
	EventTickets                     *EventTickets `json:"event_tickets,omitempty"`
	BusinessModel                    string        `json:"business_model"`
}

type Country struct {
	Name string `json:"name"`
	ISO  string `json:"iso"`
}

type Origin struct {
	Xcod string `json:"xcod"`
}

type OrderBump struct {
	IsOrderBump               bool   `json:"is_order_bump"`
	ParentPurchaseTransaction string `json:"parent_purchase_transaction"`
}

type EventTickets struct {
	Amount int `json:"amount"`
}

type Money struct {
	Value         json.Number `json:"value"`
	CurrencyValue string      `json:"currency_value"`
}

type Offer struct {
	Code       string `json:"code"`
	CouponCode string `json:"coupon_code"`
}

type Payment struct {
	Type               string `json:"type"`
	BilletURL          string `json:"billet_url,omitempty"`
	InstallmentsNumber int    `json:"installments_number,omitempty"`
	// …
}

type SubscriptionInfo struct {
	Status     string `json:"status"`
	Plan       Plan   `json:"plan"`
	Subscriber struct {
		Code string `json:"code"`
	} `json:"subscriber"`
}

type Plan struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type ProductBasic struct {
	Name string `json:"name"`
	ID   int    `json:"id"`
}

type Subscriber struct {
	Code  string `json:"code"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

type SubscriptionRef struct {
	ID   int  `json:"id"`
	Plan Plan `json:"plan"`
}

// ── CONVERSÃO AUTOMÁTICA DE TIMESTAMP (ms) ──────────────────────────
type UnixMillis time.Time

func (t *UnixMillis) UnmarshalJSON(b []byte) error {
	var ms int64
	if err := json.Unmarshal(b, &ms); err != nil {
		return err
	}
	*t = UnixMillis(time.Unix(ms/1000, (ms%1000)*int64(time.Millisecond)))
	return nil
}

func (t UnixMillis) Time() time.Time {
	return time.Time(t)
}
