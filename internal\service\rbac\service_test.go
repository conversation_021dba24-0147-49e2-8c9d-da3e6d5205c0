package rbac

import (
	"context"
	"testing"

	"go.mongodb.org/mongo-driver/bson"
)

func TestNewService(t *testing.T) {
	service := New()
	if service == nil {
		t.Error("NewService() should return a non-nil service")
	}
}

func TestBuildUserFilter(t *testing.T) {
	service := New()
	ctx := context.Background()

	tests := []struct {
		name      string
		userID    string
		userRoles []string
		wantEmpty bool // true if filter should be empty (admin access)
	}{
		{
			"Admin user gets empty filter",
			"admin-user",
			[]string{"admin"},
			true,
		},
		{
			"Director user gets filtered access",
			"director-user",
			[]string{"director"},
			false,
		},
		{
			"Teacher user gets filtered access",
			"teacher-user",
			[]string{"teacher"},
			false,
		},
		{
			"Student user gets filtered access",
			"student-user",
			[]string{"student"},
			false,
		},
		{
			"HR user gets filtered access",
			"hr-user",
			[]string{"human_resources"},
			false,
		},
		{
			"Company HR user gets filtered access",
			"hr-company-user",
			[]string{"hr_company_123"},
			false,
		},
		{
			"Multiple roles",
			"multi-role-user",
			[]string{"teacher", "hr_company_123"},
			false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filter := service.BuildUserFilter(ctx, tt.userID, tt.userRoles)

			if tt.wantEmpty {
				if len(filter) != 0 {
					t.Errorf("Expected empty filter for admin, got %v", filter)
				}
			} else {
				if len(filter) == 0 {
					t.Error("Expected non-empty filter for non-admin user")
				}
			}
		})
	}
}

func TestBuildDepartmentFilter(t *testing.T) {
	service := New()
	ctx := context.Background()

	tests := []struct {
		name            string
		userID          string
		userRoles       []string
		departmentField string
		wantEmpty       bool
	}{
		{
			"Admin user gets empty filter",
			"admin-user",
			[]string{"admin"},
			"departmentId",
			true,
		},
		{
			"Director user gets department filter",
			"director-user",
			[]string{"director"},
			"departmentId",
			false,
		},
		{
			"Teacher user gets no department access",
			"teacher-user",
			[]string{"teacher"},
			"departmentId",
			false,
		},
		{
			"Student user gets no department access",
			"student-user",
			[]string{"student"},
			"departmentId",
			false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filter := service.BuildDepartmentFilter(ctx, tt.userID, tt.userRoles, tt.departmentField)

			if tt.wantEmpty {
				if len(filter) != 0 {
					t.Errorf("Expected empty filter for admin, got %v", filter)
				}
			} else {
				if len(filter) == 0 {
					t.Error("Expected non-empty filter for non-admin user")
				}
			}
		})
	}
}

func TestBuildCompanyFilter(t *testing.T) {
	service := New()
	ctx := context.Background()

	tests := []struct {
		name         string
		userID       string
		userRoles    []string
		companyField string
		wantEmpty    bool
	}{
		{
			"Admin user gets empty filter",
			"admin-user",
			[]string{"admin"},
			"companyId",
			true,
		},
		{
			"HR user gets company filter",
			"hr-user",
			[]string{"human_resources"},
			"companyId",
			false,
		},
		{
			"Company HR user gets specific company filter",
			"hr-company-user",
			[]string{"hr_company_123"},
			"companyId",
			false,
		},
		{
			"Teacher user gets no company access",
			"teacher-user",
			[]string{"teacher"},
			"companyId",
			false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filter := service.BuildCompanyFilter(ctx, tt.userID, tt.userRoles, tt.companyField)

			if tt.wantEmpty {
				if len(filter) != 0 {
					t.Errorf("Expected empty filter for admin, got %v", filter)
				}
			} else {
				if len(filter) == 0 {
					t.Error("Expected non-empty filter for non-admin user")
				}
			}
		})
	}
}

func TestBuildStudentFilter(t *testing.T) {
	service := New()
	ctx := context.Background()

	tests := []struct {
		name         string
		userID       string
		userRoles    []string
		teacherField string
		studentField string
		wantEmpty    bool
	}{
		{
			"Admin user gets empty filter",
			"admin-user",
			[]string{"admin"},
			"teacherId",
			"studentId",
			true,
		},
		{
			"Teacher user gets student filter",
			"teacher-user",
			[]string{"teacher"},
			"teacherId",
			"studentId",
			false,
		},
		{
			"Student user gets own data filter",
			"student-user",
			[]string{"student"},
			"teacherId",
			"studentId",
			false,
		},
		{
			"Director user gets department filter",
			"director-user",
			[]string{"director"},
			"teacherId",
			"studentId",
			false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filter := service.BuildStudentFilter(ctx, tt.userID, tt.userRoles, tt.teacherField, tt.studentField)

			if tt.wantEmpty {
				if len(filter) != 0 {
					t.Errorf("Expected empty filter for admin, got %v", filter)
				}
			} else {
				if len(filter) == 0 {
					t.Error("Expected non-empty filter for non-admin user")
				}
			}
		})
	}
}

func TestCanAccessResource(t *testing.T) {
	service := New()
	ctx := context.Background()

	tests := []struct {
		name            string
		userID          string
		userRoles       []string
		resourceOwnerID string
		expected        bool
	}{
		{
			"Admin can access any resource",
			"admin-user",
			[]string{"admin"},
			"other-user",
			true,
		},
		{
			"User can access own resource",
			"user-123",
			[]string{"student"},
			"user-123",
			true,
		},
		{
			"User cannot access other's resource without permission",
			"user-123",
			[]string{"student"},
			"user-456",
			false,
		},
		{
			"Director can access department resources",
			"director-user",
			[]string{"director"},
			"other-user",
			true,
		},
		{
			"Teacher can access student resources",
			"teacher-user",
			[]string{"teacher"},
			"student-user",
			true,
		},
		{
			"HR can access company resources",
			"hr-user",
			[]string{"human_resources"},
			"company-user",
			true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.CanAccessResource(ctx, tt.userID, tt.userRoles, tt.resourceOwnerID)
			if result != tt.expected {
				t.Errorf("CanAccessResource() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestGetAccessibleUserIDs(t *testing.T) {
	service := New()
	ctx := context.Background()

	tests := []struct {
		name      string
		userID    string
		userRoles []string
		wantEmpty bool // true if should return empty slice (indicating all users)
		wantSelf  bool // true if should include self
	}{
		{
			"Admin gets all users",
			"admin-user",
			[]string{"admin"},
			true,
			false,
		},
		{
			"Student gets only self",
			"student-user",
			[]string{"student"},
			false,
			true,
		},
		{
			"Teacher gets self",
			"teacher-user",
			[]string{"teacher"},
			false,
			true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.GetAccessibleUserIDs(ctx, tt.userID, tt.userRoles)
			if err != nil {
				t.Errorf("GetAccessibleUserIDs() error = %v", err)
				return
			}

			if tt.wantEmpty {
				if len(result) != 0 {
					t.Errorf("Expected empty slice for admin, got %v", result)
				}
			} else if tt.wantSelf {
				if len(result) != 1 || result[0] != tt.userID {
					t.Errorf("Expected [%s], got %v", tt.userID, result)
				}
			}
		})
	}
}

func TestBuildOwnershipFilter(t *testing.T) {
	tests := []struct {
		name       string
		userID     string
		ownerField string
		expected   int // expected number of conditions in $or array
	}{
		{
			"Valid ObjectID",
			"507f1f77bcf86cd799439011",
			"ownerId",
			2, // string and ObjectID conditions
		},
		{
			"Invalid ObjectID",
			"invalid-id",
			"ownerId",
			1, // only string condition
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filter := BuildOwnershipFilter(tt.userID, tt.ownerField)

			if orConditions, exists := filter["$or"]; exists {
				if conditions, ok := orConditions.([]bson.M); ok {
					if len(conditions) != tt.expected {
						t.Errorf("Expected %d conditions, got %d", tt.expected, len(conditions))
					}
				} else {
					t.Error("$or should contain array of conditions")
				}
			} else if tt.expected == 1 {
				// Single condition, no $or
				if len(filter) == 0 {
					t.Error("Expected non-empty filter")
				}
			} else {
				t.Error("Expected $or conditions")
			}
		})
	}
}

func TestBuildRoleBasedFilter(t *testing.T) {
	tests := []struct {
		name         string
		userRoles    []string
		roleField    string
		allowedRoles []string
		wantEmpty    bool
	}{
		{
			"Admin gets empty filter",
			[]string{"admin"},
			"role",
			[]string{"student", "teacher"},
			true,
		},
		{
			"Non-admin gets role filter",
			[]string{"teacher"},
			"role",
			[]string{"student"},
			false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filter := BuildRoleBasedFilter(tt.userRoles, tt.roleField, tt.allowedRoles)

			if tt.wantEmpty {
				if len(filter) != 0 {
					t.Errorf("Expected empty filter for admin, got %v", filter)
				}
			} else {
				if len(filter) == 0 {
					t.Error("Expected non-empty filter for non-admin user")
				}
			}
		})
	}
}

func TestCombineFilters(t *testing.T) {
	filter1 := bson.M{"field1": "value1"}
	filter2 := bson.M{"field2": "value2"}
	emptyFilter := bson.M{}

	tests := []struct {
		name     string
		filters  []bson.M
		expected int // expected number of conditions
	}{
		{
			"No filters",
			[]bson.M{},
			0,
		},
		{
			"Single filter",
			[]bson.M{filter1},
			1,
		},
		{
			"Multiple filters",
			[]bson.M{filter1, filter2},
			2,
		},
		{
			"Filters with empty",
			[]bson.M{filter1, emptyFilter, filter2},
			2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CombineFilters(tt.filters...)

			if tt.expected == 0 {
				if len(result) != 0 {
					t.Errorf("Expected empty filter, got %v", result)
				}
			} else if tt.expected == 1 {
				if len(result) == 0 {
					t.Error("Expected non-empty filter")
				}
				if _, hasAnd := result["$and"]; hasAnd {
					t.Error("Single filter should not have $and")
				}
			} else {
				if andConditions, exists := result["$and"]; exists {
					if conditions, ok := andConditions.([]bson.M); ok {
						if len(conditions) != tt.expected {
							t.Errorf("Expected %d conditions in $and, got %d", tt.expected, len(conditions))
						}
					} else {
						t.Error("$and should contain array of conditions")
					}
				} else {
					t.Error("Multiple filters should have $and")
				}
			}
		})
	}
}
