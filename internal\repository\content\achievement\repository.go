package achievement

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/content"
)

type Reader interface {
	Find(ctx context.Context, id string) (*content.Achievement, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.Achievement, error)
	FindAll(ctx context.Context) ([]*content.Achievement, error)
}

type Writer interface {
	Create(ctx context.Context, achievement *content.Achievement) error
	Update(ctx context.Context, achievement *content.Achievement) error
	Delete(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
