package billing

import (
	"time"
)

// Common billing constants and utilities

// Default subscription configuration
const (
	DefaultSubscriptionDurationMonths = 12
	DefaultTrialDays                  = 7
	DefaultCurrency                   = "BRL"
)

// Feature constants for plan features
const (
	FeatureBasicContent = "basic_content"
	FeatureDinboraPlus  = "dinbora_plus"
)

// Webhook event types
const (
	WebhookEventSubscriptionCreated   = "subscription.created"
	WebhookEventSubscriptionUpdated   = "subscription.updated"
	WebhookEventSubscriptionCancelled = "subscription.cancelled"
	WebhookEventSubscriptionExpired   = "subscription.expired"
	WebhookEventPaymentCompleted      = "payment.completed"
	WebhookEventPaymentFailed         = "payment.failed"
	WebhookEventPaymentRefunded       = "payment.refunded"
)

// SubscriptionHelper provides utility methods for subscription management
type SubscriptionHelper struct{}

// CalculateEndDate calculates the end date for a subscription based on start date and duration
func (sh *SubscriptionHelper) CalculateEndDate(startDate time.Time, durationMonths int) time.Time {
	return startDate.AddDate(0, durationMonths, 0)
}

// CalculateTrialEndDate calculates the trial end date based on start date and trial days
func (sh *SubscriptionHelper) CalculateTrialEndDate(startDate time.Time, trialDays int) *time.Time {
	if trialDays <= 0 {
		return nil
	}
	trialEnd := startDate.AddDate(0, 0, trialDays)
	return &trialEnd
}

// IsSubscriptionExpired checks if a subscription is expired based on end date
func (sh *SubscriptionHelper) IsSubscriptionExpired(endDate time.Time) bool {
	return time.Now().After(endDate)
}

// IsTrialExpired checks if a trial period is expired
func (sh *SubscriptionHelper) IsTrialExpired(trialEndDate *time.Time) bool {
	if trialEndDate == nil {
		return false
	}
	return time.Now().After(*trialEndDate)
}

// GetRemainingTrialDays calculates remaining trial days
func (sh *SubscriptionHelper) GetRemainingTrialDays(trialEndDate *time.Time) int {
	if trialEndDate == nil {
		return 0
	}

	now := time.Now()
	if now.After(*trialEndDate) {
		return 0
	}

	duration := trialEndDate.Sub(now)
	return int(duration.Hours() / 24)
}

// GetRemainingSubscriptionDays calculates remaining subscription days
func (sh *SubscriptionHelper) GetRemainingSubscriptionDays(endDate time.Time) int {
	now := time.Now()
	if now.After(endDate) {
		return 0
	}

	duration := endDate.Sub(now)
	return int(duration.Hours() / 24)
}

// GetDaysSinceStart calculates days since subscription start
func (sh *SubscriptionHelper) GetDaysSinceStart(startDate time.Time) int {
	now := time.Now()
	if now.Before(startDate) {
		return 0
	}

	duration := now.Sub(startDate)
	return int(duration.Hours() / 24)
}

// PlanHelper provides utility methods for plan management
type PlanHelper struct{}

// GetDefaultFeatures returns default features for a basic plan
func (ph *PlanHelper) GetDefaultFeatures() []string {
	return []string{
		FeatureBasicContent,
	}
}

// GetPremiumFeatures returns features for a premium plan
func (ph *PlanHelper) GetPremiumFeatures() []string {
	return []string{
		FeatureDinboraPlus,
	}
}

// PaymentHelper provides utility methods for payment management
type PaymentHelper struct{}

// IsPaymentSuccessful checks if a payment status indicates success
func (ph *PaymentHelper) IsPaymentSuccessful(status PaymentStatus) bool {
	return status == PaymentStatusCompleted
}

// IsPaymentFinal checks if a payment status is final (no further processing expected)
func (ph *PaymentHelper) IsPaymentFinal(status PaymentStatus) bool {
	switch status {
	case PaymentStatusCompleted, PaymentStatusFailed, PaymentStatusRefunded, PaymentStatusCancelled:
		return true
	default:
		return false
	}
}

// GetPaymentStatusFromProvider maps provider-specific status to our payment status
func (ph *PaymentHelper) GetPaymentStatusFromProvider(provider PaymentProvider, providerStatus string) PaymentStatus {
	switch provider {
	case PaymentProviderHotmart:
		return ph.mapHotmartStatus(providerStatus)
	case PaymentProviderApplePay:
		return ph.mapApplePayStatus(providerStatus)
	default:
		return PaymentStatusPending
	}
}

// mapHotmartStatus maps Hotmart status to our payment status
func (ph *PaymentHelper) mapHotmartStatus(status string) PaymentStatus {
	switch status {
	case "paid", "approved", "completed":
		return PaymentStatusCompleted
	case "cancelled", "canceled":
		return PaymentStatusCancelled
	case "refunded":
		return PaymentStatusRefunded
	case "failed", "declined", "rejected":
		return PaymentStatusFailed
	default:
		return PaymentStatusPending
	}
}

// mapApplePayStatus maps Apple Pay status to our payment status
func (ph *PaymentHelper) mapApplePayStatus(status string) PaymentStatus {
	switch status {
	case "PAID", "ACTIVE":
		return PaymentStatusCompleted
	case "CANCELLED", "CANCELED":
		return PaymentStatusCancelled
	case "REFUNDED":
		return PaymentStatusRefunded
	case "FAILED", "EXPIRED":
		return PaymentStatusFailed
	default:
		return PaymentStatusPending
	}
}

// Global helper instances
var (
	SubscriptionHelperInstance = &SubscriptionHelper{}
	PlanHelperInstance         = &PlanHelper{}
	PaymentHelperInstance      = &PaymentHelper{}
)
