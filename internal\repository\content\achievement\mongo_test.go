package achievement

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/config"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type MongoRepositoryTestSuite struct {
	suite.Suite
	client     *mongo.Client
	db         *mongo.Database
	repository Repository
	testIDs    []string
}

func (suite *MongoRepositoryTestSuite) SetupTest() {
	ctx := context.Background()

	// Initialize environment configuration
	err := config.EnvFromFile("../../../../.env")
	if err != nil {
		suite.T().Skipf("Failed to initialize environment configuration: %v - skipping integration tests", err)
		return
	}

	// Get database configuration
	dbURL := os.Getenv("DATABASE_URL")
	dbName := os.Getenv("DATABASE_NAME")

	if dbURL == "" || dbName == "" {
		suite.T().Skip("DATABASE_URL and DATABASE_NAME configuration not set - skipping integration tests")
		return
	}

	// Connect to MongoDB
	clientOptions := options.Client().ApplyURI(dbURL)
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		suite.T().Skipf("Failed to connect to MongoDB: %v - skipping integration tests", err)
		return
	}
	suite.client = client // Save client reference

	// Verify MongoDB connection
	err = suite.client.Ping(ctx, nil)
	if err != nil {
		suite.T().Skipf("Failed to ping MongoDB: %v - skipping integration tests", err)
		return
	}

	suite.db = suite.client.Database(dbName)
	suite.repository = New(suite.db)

	// Verify test collection exists
	collections, err := suite.db.ListCollectionNames(ctx, bson.M{})
	if err != nil {
		suite.T().Skipf("Failed to list collections: %v - skipping integration tests", err)
		return
	}

	var collectionExists bool
	for _, col := range collections {
		if col == repository.ACHIEVEMENTS_COLLECTION {
			collectionExists = true
			break
		}
	}

	if !collectionExists {
		suite.T().Skipf("Collection %s does not exist - skipping integration tests", repository.ACHIEVEMENTS_COLLECTION)
		return
	}
}

func (suite *MongoRepositoryTestSuite) TearDownTest() {
	ctx := context.Background()

	// Clean up test data
	if suite.db != nil && len(suite.testIDs) > 0 {
		for _, id := range suite.testIDs {
			objID, err := primitive.ObjectIDFromHex(id)
			if err == nil {
				suite.T().Logf("Deleting test achievement with ID: %s", id)
				res, err := suite.db.Collection(repository.ACHIEVEMENTS_COLLECTION).DeleteOne(ctx, bson.M{"_id": objID})
				suite.NoError(err)
				suite.T().Logf("Delete result: %d document(s) deleted", res.DeletedCount)
			}
		}
	}

	// Disconnect client
	if suite.client != nil {
		err := suite.client.Disconnect(ctx)
		if err != nil {
			suite.T().Errorf("Error disconnecting client: %v", err)
		}
	}
}

func TestMongoRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(MongoRepositoryTestSuite))
}

func (suite *MongoRepositoryTestSuite) TestIndexCreation() {
	ctx := context.Background()

	// Get indexes
	cursor, err := suite.db.Collection(repository.ACHIEVEMENTS_COLLECTION).Indexes().List(ctx)
	suite.Require().NoError(err)
	defer cursor.Close(ctx)

	var indexes []bson.M
	err = cursor.All(ctx, &indexes)
	suite.Require().NoError(err)

	// Find our specific index
	var found bool
	for _, index := range indexes {
		if index["name"] == "identifier" {
			found = true
			// Verify index properties
			key := index["key"].(bson.M)
			suite.Equal(int32(1), key["identifier"]) // MongoDB returns int32 for index value
			suite.Equal(true, index["unique"])
			break
		}
	}
	suite.True(found, "Identifier index not found")
}

func (suite *MongoRepositoryTestSuite) TestUniqueIdentifierConstraint() {
	ctx := context.Background()

	// Create first achievement
	now := time.Now()
	achievement1 := &content.Achievement{
		ObjectID:    primitive.NewObjectID(),
		Identifier:  "test_achievement_1",
		Title:       "Test Achievement 1 Title",
		Name:        "Test Achievement 1 Name",
		Description: "Test Achievement 1 Description",
		Level:       1,
		Logo:        "test-logo-1.png",
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	err := suite.repository.Create(ctx, achievement1)
	suite.Require().NoError(err)
	suite.testIDs = append(suite.testIDs, achievement1.ObjectID.Hex())

	// Try to create another achievement with same requirement
	achievement2 := &content.Achievement{
		ObjectID:    primitive.NewObjectID(),
		Identifier:  "test_achievement_1",
		Title:       "Test Achievement 2 Title",
		Name:        "Test Achievement 2 Name",
		Description: "Test Achievement 2 Description",
		Level:       1,
		Logo:        "test-logo-2.png",
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	err = suite.repository.Create(ctx, achievement2)
	suite.Error(err) // Should fail due to unique constraint
}

func (suite *MongoRepositoryTestSuite) TestCRUDOperations() {
	ctx := context.Background()

	// Create
	now := time.Now()
	achievement := &content.Achievement{
		ObjectID:    primitive.NewObjectID(),
		Identifier:  "test_achievement",
		Title:       "Test Achievement Title",
		Name:        "Test Achievement Name",
		Description: "Test Achievement Description",
		Level:       1,
		Logo:        "test-logo.png",
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	err := suite.repository.Create(ctx, achievement)
	suite.Require().NoError(err)
	suite.testIDs = append(suite.testIDs, achievement.ObjectID.Hex())

	// Find by ID
	found, err := suite.repository.Find(ctx, achievement.ObjectID.Hex())
	suite.Require().NoError(err)
	suite.Equal(achievement.Name, found.Name)

	// Find by identifier
	found, err = suite.repository.FindByIdentifier(ctx, achievement.Identifier)
	suite.Require().NoError(err)
	suite.Equal(achievement.Name, found.Name)

	// Update
	achievement.Name = "Updated Achievement"
	err = suite.repository.Update(ctx, achievement)
	suite.Require().NoError(err)

	// Verify update
	updated, err := suite.repository.Find(ctx, achievement.ObjectID.Hex())
	suite.Require().NoError(err)
	suite.Equal("Updated Achievement", updated.Name)

	// Delete
	err = suite.repository.Delete(ctx, achievement.ObjectID.Hex())
	suite.Require().NoError(err)

	// Verify deletion
	_, err = suite.repository.Find(ctx, achievement.ObjectID.Hex())
	suite.Error(err) // Should return not found error
}
