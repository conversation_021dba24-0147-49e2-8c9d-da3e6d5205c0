#!/bin/bash

# --- System Setup ---
echo "Updating system and installing base dependencies..."
yum update -y
# Install dependencies, including aws-cli, openssl, and tools needed for Fail2ban/Firewall
yum install -y awscli-2 ca-certificates openssl fail2ban iptables-services amazon-cloudwatch-agent

# --- Configure Base Firewall Rules (Before Starting iptables) ---
echo "Configuring base firewall rules in /etc/sysconfig/iptables..."
# Define the rule to add
RULE_TO_ADD="-A INPUT -p tcp -m state --state NEW -m tcp --dport 8080 -j ACCEPT"
# Define the line to insert before (the final REJECT rule is a good target)
INSERT_BEFORE_LINE="-A INPUT -j REJECT --reject-with icmp-host-prohibited"
# Use sed to insert the rule before the REJECT line.
# The exact REJECT line might differ slightly, adjust if needed based on default file content.
# We use a temporary file to avoid issues with direct inplace editing during boot potentially.
cp /etc/sysconfig/iptables /etc/sysconfig/iptables.bak
sed -i "/${INSERT_BEFORE_LINE}/i ${RULE_TO_ADD}" /etc/sysconfig/iptables

# --- CloudWatch Agent Configuration ---
echo "Configuring CloudWatch Agent..."
# Define the configuration in a heredoc. REMOVE comments from within the JSON.
cat <<EOF > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json
{
  "agent": {
    "metrics_collection_interval": 60,
    "run_as_user": "cwagent"
  },
  "metrics": {
    "append_dimensions": {
      "AutoScalingGroupName": "\${aws:AutoScalingGroupName}",
      "ImageId": "\${aws:ImageId}",
      "InstanceId": "\${aws:InstanceId}",
      "InstanceType": "\${aws:InstanceType}"
    },
    "metrics_collected": {
      "mem": {
        "measurement": [
          "mem_used_percent"
        ],
        "metrics_collection_interval": 60
      }
    }
  }
}
EOF

# --- Fail2ban Configuration ---
echo "Configuring Fail2ban..."

# 1. Create the filter for dinbora logs
# This filter looks for lines indicating a 404 error from your app and extracts the IP
cat <<EOF > /etc/fail2ban/filter.d/dinbora.local
[Definition]
# Example log line: {"time":"...","level":"ERROR","msg":"REQUEST_ERROR","uri":"/path","method":"GET","status":404,"remote_ip":"*******",...}
# Regex explanation:
# ^\s*\{.*             - Start of line, optional space, opening brace, anything
# "level":"ERROR"     - Match the error level
# .*?"status":404     - Match status 404 (non-greedy match before)
# .*?"remote_ip":"<HOST>" - Match remote_ip and capture the IP address (<HOST> is Fail2ban's placeholder)
# .*\}\s*$             - Match anything until the closing brace and end of line
#
failregex = ^\s*\{.*"level":"ERROR".*?"msg":"REQUEST_ERROR".*?"status":404.*?"remote_ip":"<HOST>".*\}\s*$
ignoreregex =

EOF

# 2. Create/Update the main jail configuration file (jail.local)
# We create jail.local to override/add to jail.conf
cat <<EOF > /etc/fail2ban/jail.local
[DEFAULT]
# Whitelist local IPs and the VPC CIDR Range for the ALB.
# This prevents accidentally banning the ALB nodes.
# --- IMPORTANT: Add any OTHER trusted IPs (office, VPN, monitoring) here too ---
ignoreip = 127.0.0.1/8 ::1 **********/16

# Default ban time (1 day) - can be overridden per jail
bantime = 86400
# Default find time (10 minutes) - can be overridden per jail
findtime = 600
# Default max retries (10 failures within findtime) - can be overridden per jail
maxretry = 10
# Default banning action uses iptables-multiport
banaction = iptables-multiport
# Use the simpler action (ban only) instead of action_ (ban + email report)
action = %(action_)s

# --- Custom Jail for Dinbora Application behind ALB ---
[dinbora-404]
enabled  = true
# Specify the port your application LISTENS on (target group port)
port     = 8080
filter   = dinbora
         # ^-- Use the filter defined in filter.d/dinbora.local (Comment on separate line)
logpath  = /var/log/dinbora.log
         # ^-- Monitor this log file (Comment on separate line)
# Stricter settings for web probing are often good
maxretry = 15
findtime = 1800
bantime  = 86400

# --- Standard SSH Jail (Recommended) ---
[sshd]
enabled = true
port    = ssh  # Default is 22
# Stricter rules for SSH:
maxretry = 5
findtime = 300 # 5 minutes
bantime = -1   # Permanent ban for SSH brute-force (use with caution, ensure you have other access methods or trusted IPs in ignoreip)
# logpath = %(sshd_log)s # Uses default sshd log path detection
# filter = %(sshd_filter)s # Uses default sshd filter

EOF

# --- Log File and Logrotate Setup
echo "Setting up log file and logrotate..."
# Create log file
touch /var/log/dinbora.log
# Fail2ban runs as root, needs read access. ec2-user needs write access.
chown ec2-user:root /var/log/dinbora.log
chmod 644 /var/log/dinbora.log # User write, Group read, Other read

# Configure logrotate for dinbora logs
cat <<EOF > /etc/logrotate.d/dinbora
/var/log/dinbora.log {
    weekly
    size 50M
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 ec2-user root # Consistent permissions
    postrotate
        # Reload fail2ban to ensure it picks up the new log file if rotated
        /bin/systemctl reload fail2ban > /dev/null 2>&1 || true
        # Restarting the app might not be needed if logging is handled robustly
        # /bin/systemctl restart dinbora >/dev/null 2>&1 || true
    endscript
}
EOF

# --- Enable and Start Services
echo "Enabling and starting services..."
systemctl enable iptables
systemctl enable fail2ban
# Enable the CloudWatch Agent service
systemctl enable amazon-cloudwatch-agent

# Start services - CloudWatch Agent needs to be started AFTER config is created
systemctl start iptables
systemctl start fail2ban
# Start CloudWatch Agent using the control script to load the config file
# Using '-a append-config' might be safer than '-a fetch-config' if default config exists
# But fetch-config is common for replacing whatever might be there.
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json -s

# --- Dinbora Application Setup ---
echo "Fetching environment variables..."
# Fetch environment variables from Parameter Store and write to a file
aws ssm get-parameters-by-path \
  --path "/dinbora/" \
  --with-decryption \
  --query "Parameters[*].[Name,Value]" \
  --output text | while read -r name value; do
    var_name=$(basename "$name")
    echo "$var_name='$value'" >> /etc/dinbora.env
done
chmod 644 /etc/dinbora.env
chown root:ec2-user /etc/dinbora.env # Keep root owner, allow ec2-user group read

echo "Creating systemd service for Dinbora..."
# Create a systemd service for the dinbora backend
cat <<EOF > /etc/systemd/system/dinbora.service
[Unit]
Description=Dinbora Back-end
After=network.target fail2ban.service # Ensure network and fail2ban are up

[Service]
ExecStart=/bin/bash -c '/usr/local/bin/dinbora 2>&1 | tee -a /var/log/dinbora.log'
Restart=always
RestartSec=5
User=ec2-user
Environment="PATH=/usr/local/bin:/usr/bin"
EnvironmentFile=/etc/dinbora.env
WorkingDirectory=/home/<USER>
StandardOutput=journal+console # Log to journal AND console (picked up by tee)
StandardError=journal+console  # Log to journal AND console (picked up by tee)

[Install]
WantedBy=multi-user.target
EOF

echo "Downloading Dinbora binary from S3..."
# Check if the dinbora binary exists in S3 (with retries)
echo "Checking if dinbora binary exists in S3..."
for i in {1..10}; do
    if aws s3 ls s3://dinbora-backend-deployment-bucket/dinbora-latest; then
        echo "Binary found in S3, proceeding with download..."
        break
    fi
    echo "Binary not found in S3, retrying ($i/10)..."
    sleep 30
    if [ $i -eq 10 ]; then
        echo "Failed to find dinbora binary in S3 after 10 attempts. Exiting."
        systemctl stop fail2ban # Stop fail2ban if setup fails
        exit 1
    fi
done

# Download the dinbora binary from S3 with retries
for i in {1..5}; do
    aws s3 cp s3://dinbora-backend-deployment-bucket/dinbora-latest /usr/local/bin/dinbora && break
    echo "Failed to download dinbora, retrying ($i/5)..."
    sleep 10
done

# Verify the download
if [ ! -f /usr/local/bin/dinbora ]; then
    echo "Failed to download dinbora binary after retries. Exiting."
    systemctl stop fail2ban # Stop fail2ban if setup fails
    exit 1
fi

# Set permissions for the binary
chmod +x /usr/local/bin/dinbora

echo "Downloading migration files from S3..."
# Check if the migration folder exists in S3 (with retries)
echo "Checking if migration folder exists in S3..."
for i in {1..10}; do
    # Check for objects within the prefix to ensure it's not just an empty placeholder
    if aws s3 ls s3://dinbora-backend-deployment-bucket/migration-latest/ --recursive | head -n 1 > /dev/null; then
        echo "Migration folder content found in S3, proceeding with download..."
        break
    fi
    echo "Migration folder not found or empty in S3, retrying ($i/10)..."
    sleep 30
    if [ $i -eq 10 ]; then
        echo "Failed to find migration folder content in S3 after 10 attempts. Exiting."
        systemctl stop fail2ban # Stop fail2ban if setup fails
        exit 1
    fi
done

# Download the migration folder from S3 with retries
mkdir -p /usr/local/dinbora/migration
for i in {1..5}; do
    aws s3 sync s3://dinbora-backend-deployment-bucket/migration-latest/ /usr/local/dinbora/migration/ && break
    echo "Failed to download migration folder, retrying ($i/5)..."
    sleep 10
done

# Verify the download
if [ -z "$(ls -A /usr/local/dinbora/migration)" ]; then
    echo "Failed to download migration folder after retries or folder is empty. Exiting."
    systemctl stop fail2ban # Stop fail2ban if setup fails
    exit 1
fi

# Set permissions for the migration folder
chown -R ec2-user:ec2-user /usr/local/dinbora/migration
chmod -R 755 /usr/local/dinbora/migration

# --- MongoDB Atlas IP Allowlisting ---
echo "Fetching MongoDB Atlas credentials from Parameter Store..."

# Define Parameter Store names
PARAM_ATLAS_PUBLIC_KEY="/dinbora/atlas/ATLAS_PUBLIC_KEY"
PARAM_ATLAS_PRIVATE_KEY="/dinbora/atlas/ATLAS_PRIVATE_KEY" # Stored as SecureString
PARAM_ATLAS_PROJECT_ID="/dinbora/atlas/PROJECT_ID"

# Fetch credentials using aws ssm get-parameter
# IMPORTANT: Ensure the EC2 Instance Role has ssm:GetParameter permissions for these parameters!
ATLAS_PUBLIC_KEY=$(aws ssm get-parameter --name "$PARAM_ATLAS_PUBLIC_KEY" --query Parameter.Value --output text)
if [ -z "$ATLAS_PUBLIC_KEY" ]; then
    echo "ERROR: Failed to fetch ATLAS_PUBLIC_KEY from Parameter Store ($PARAM_ATLAS_PUBLIC_KEY). Check parameter existence and IAM permissions."
    exit 1
fi

ATLAS_PRIVATE_KEY=$(aws ssm get-parameter --name "$PARAM_ATLAS_PRIVATE_KEY" --with-decryption --query Parameter.Value --output text)
if [ -z "$ATLAS_PRIVATE_KEY" ]; then
    echo "ERROR: Failed to fetch ATLAS_PRIVATE_KEY from Parameter Store ($PARAM_ATLAS_PRIVATE_KEY). Check parameter existence, type (SecureString), and IAM permissions (kms:Decrypt might be needed depending on key used)."
    exit 1
fi

PROJECT_ID=$(aws ssm get-parameter --name "$PARAM_ATLAS_PROJECT_ID" --query Parameter.Value --output text)
if [ -z "$PROJECT_ID" ]; then
    echo "ERROR: Failed to fetch PROJECT_ID from Parameter Store ($PARAM_ATLAS_PROJECT_ID). Check parameter existence and IAM permissions."
    exit 1
fi

echo "Successfully fetched Atlas credentials."
echo "Adding instance IP to MongoDB Atlas IP Access List..."

# Get instance public IP using IMDSv2
TOKEN=$(curl -s -X PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")
if [ -z "$TOKEN" ]; then
    echo "ERROR: Failed to retrieve IMDSv2 token."
    exit 1
fi
PUBLIC_IP=$(curl -s -H "X-aws-ec2-metadata-token: $TOKEN" http://***************/latest/meta-data/public-ipv4)
if [ -z "$PUBLIC_IP" ]; then
    echo "ERROR: Failed to retrieve public IP via IMDSv2."
    exit 1
fi

ATLAS_API_URL="https://cloud.mongodb.com/api/atlas/v1.0/groups/$PROJECT_ID/accessList"

# Check if IP already exists to avoid API errors (optional but good practice)
EXISTING_IPS=$(curl --user "$ATLAS_PUBLIC_KEY:$ATLAS_PRIVATE_KEY" --digest -s --header 'Accept: application/json' "$ATLAS_API_URL?pretty=false&itemsPerPage=500" | grep -o '"ipAddress" : "[^"]*"' | grep -o '[0-9\.]*')
CURL_EXIT_CODE=$? # Check exit code of the curl command

if [ $CURL_EXIT_CODE -ne 0 ]; then
    echo "WARNING: Failed to check existing IPs in Atlas (Exit Code: $CURL_EXIT_CODE). Proceeding with adding IP anyway."
    EXISTING_IPS="" # Assume IP doesn't exist if check failed
fi

if echo "$EXISTING_IPS" | grep -q "^${PUBLIC_IP}$"; then
  echo "IP $PUBLIC_IP already exists in Atlas Access List."
else
  echo "Adding IP $PUBLIC_IP/32 to Atlas Access List..."
  # Add timestamp to comment for better tracking
  COMMENT="EC2 dinbora via launch-script $(date +%Y-%m-%d_%H:%M:%S)"
  DATA_PAYLOAD="[{\"ipAddress\": \"$PUBLIC_IP/32\", \"comment\": \"$COMMENT\"}]"

  ADD_RESULT=$(curl --user "$ATLAS_PUBLIC_KEY:$ATLAS_PRIVATE_KEY" --digest \
    --header 'Accept: application/json' \
    --header 'Content-Type: application/json' \
    --request POST "$ATLAS_API_URL?itemsPerPage=1" \
    --data "$DATA_PAYLOAD" --write-out "\nHTTP_STATUS_CODE:%{http_code}" -s)

  HTTP_STATUS=$(echo "$ADD_RESULT" | sed -n 's/.*HTTP_STATUS_CODE://p')
  RESPONSE_BODY=$(echo "$ADD_RESULT" | sed 's/HTTP_STATUS_CODE:.*//')

  # Check HTTP status codes indicating success or known non-fatal issues
  if [[ "$HTTP_STATUS" -eq 201 ]]; then
      echo "Successfully added IP $PUBLIC_IP to Atlas Access List."
  elif [[ "$HTTP_STATUS" -eq 400 ]] && [[ "$RESPONSE_BODY" == *"DUPLICATE_ENTRY"* ]]; then
      echo "IP $PUBLIC_IP seems to already exist in Atlas (Duplicate Entry error - non-fatal)."
  elif [[ "$HTTP_STATUS" -eq 409 ]] && [[ "$RESPONSE_BODY" == *"IP_ADDRESS_IS_ALREADY_IN_ACCESS_LIST"* ]]; then
       echo "IP $PUBLIC_IP is already in the access list (Conflict error - non-fatal)."
  elif [[ "$HTTP_STATUS" -eq 401 ]]; then
       echo "ERROR: Authentication failed when adding IP to Atlas (HTTP 401). Check Atlas API Key validity and permissions."
       # Decide if this should be a fatal error for your setup
       # exit 1
  else
      echo "WARNING: Potentially failed adding IP $PUBLIC_IP to Atlas Access List. Status: $HTTP_STATUS"
      echo "Response Body: $RESPONSE_BODY"
      # Decide if this should be a fatal error
      # exit 1
  fi
fi

echo "Starting Dinbora service..."
# Reload systemd to pick up changes, enable, and start the dinbora service
systemctl daemon-reload
systemctl enable dinbora
systemctl start dinbora

echo "Instance setup complete. Dinbora and Fail2ban are running."

# --- Useful Commands for Debugging ---
# sudo systemctl status dinbora
# sudo systemctl daemon-reload
# sudo systemctl restart dinbora
# journalctl -u dinbora -f
# sudo cat /var/log/dinbora.log | tail -100
# sudo fail2ban-client status
# sudo fail2ban-client status dinbora-404
# sudo iptables -L -n --line-numbers | grep f2b-dinbora-404
# journalctl -u dinbora -f | grep -v '"uri":"/v2/health"'
# journalctl -u dinbora -f | grep -v '"user_agent":"ELB-HealthChecker/2.0"'
# journalctl -o cat -u dinbora -f | grep -v '"uri":"/v2/health"'
# sudo cat /var/log/cloud-init-output.log

# Verify insertion (optional, good for debugging logs)
#echo "--- /etc/sysconfig/iptables after modification ---"
#cat /etc/sysconfig/iptables
#echo "--------------------------------------------------"

# --- Useful Commands for Fail2ban ---
#Service Status (Focus on Fail2ban):
# sudo systemctl status iptables
# sudo systemctl status fail2ban

#Check Fail2ban Jails Active:
# sudo fail2ban-client status # Should list Jail list: dinbora-404, sshd
# sudo fail2ban-client status dinbora-404 # Should show 0 banned currently
# sudo fail2ban-client status sshd        # Should show 0 banned currently