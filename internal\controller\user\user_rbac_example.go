package user

import (
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"github.com/labstack/echo/v4"
)

// Example of how to integrate role-based filtering with existing endpoints
// This file demonstrates the updated user controller methods that use the new RBAC system

// FindWithRoleFilter demonstrates how to implement role-based filtering for user lookup
func (uc *controller) FindWithRoleFilter() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Get target user ID from URL parameter
		targetUserID := c.Param("id")
		if targetUserID == "" {
			// If no ID provided, return current user's data
			targetUserID = userCtx.UserID
		}

		// Use the new role-based filtering method
		user, err := uc.Service.FindWithRoleFilter(ctx, targetUserID, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, user.Sanitize())
	}
}

// FindAllWithRoleFilter demonstrates how to list users with role-based filtering
func (uc *controller) FindAllWithRoleFilter() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Use the new role-based filtering method
		users, err := uc.Service.FindAllWithRoleFilter(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		// Sanitize all users before returning
		sanitizedUsers := make([]interface{}, len(users))
		for i, user := range users {
			sanitizedUsers[i] = user.Sanitize()
		}

		return c.JSON(http.StatusOK, sanitizedUsers)
	}
}

// UpdateWithRoleCheck demonstrates role-based access control for user updates
func (uc *controller) UpdateWithRoleCheck() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		targetUserID := c.Param("id")

		// Check if user can access the target user using service method
		canAccess, err := uc.Service.CanAccessUser(ctx, userCtx.UserID, userCtx.Roles, targetUserID)
		if err != nil {
			return err
		}

		if !canAccess {
			return errors.New(errors.Controller, "access denied", errors.Forbidden, nil)
		}

		// Get the target user
		targetUser, err := uc.Service.Find(ctx, targetUserID)
		if err != nil {
			return err
		}

		// Bind update data
		var updateData map[string]interface{}
		if err := c.Bind(&updateData); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		// Role-based restrictions on what can be updated
		if roles, exists := updateData["roles"]; exists {
			// Only admins can modify roles - check using auth package
			if !auth.IsAdmin(userCtx.Roles) {
				return errors.New(errors.Controller, "insufficient permissions to modify roles", errors.Forbidden, nil)
			}

			// Additional validation for role changes could go here
			_ = roles // Use the roles data for validation
		}

		// Perform the update (implementation would depend on your update logic)
		// This is a simplified example
		if err := uc.Service.Update(ctx, targetUser); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, targetUser.Sanitize())
	}
}

// DeleteWithRoleCheck demonstrates role-based access control for user deletion
func (uc *controller) DeleteWithRoleCheck() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		targetUserID := c.Param("id")

		// Get user context from middleware
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Check if user can access the target user using service method
		canAccess, err := uc.Service.CanAccessUser(ctx, userCtx.UserID, userCtx.Roles, targetUserID)
		if err != nil {
			return err
		}

		if !canAccess {
			return errors.New(errors.Controller, "access denied", errors.Forbidden, nil)
		}

		// Users can delete their own account, admins can delete any account
		if userCtx.UserID != targetUserID {
			if !auth.IsAdmin(userCtx.Roles) {
				return errors.New(errors.Controller, "only admins can delete other users", errors.Forbidden, nil)
			}
		}

		// Bind delete reason
		var deleteReason map[string]interface{}
		if err := c.Bind(&deleteReason); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		// Perform the deletion
		if err := uc.Service.Delete(ctx, targetUserID, nil); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// GetUsersByRole demonstrates filtering users by role (admin only)
func (uc *controller) GetUsersByRole() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context and check admin access
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// This endpoint requires admin access
		if !auth.IsAdmin(userCtx.Roles) {
			return errors.New(errors.Controller, "admin access required", errors.Forbidden, nil)
		}

		role := c.QueryParam("role")
		if role == "" {
			return errors.New(errors.Controller, "role parameter is required", errors.Validation, nil)
		}

		// Get all users and filter by role
		// In a real implementation, this would be done at the database level for efficiency
		users, err := uc.Service.FindAll(ctx)
		if err != nil {
			return err
		}

		var filteredUsers []interface{}
		for _, user := range users {
			for _, userRole := range user.Roles {
				if userRole == role {
					filteredUsers = append(filteredUsers, user.Sanitize())
					break
				}
			}
		}

		return c.JSON(http.StatusOK, filteredUsers)
	}
}

// GetMyStudents demonstrates teacher accessing their students
func (uc *controller) GetMyStudents() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user context and check teacher access
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Check if user is a teacher or has higher privileges
		if !auth.HasMinimumRole(userCtx.Roles, auth.RoleTeacher) {
			return errors.New(errors.Controller, "teacher access required", errors.Forbidden, nil)
		}

		// In a real implementation, this would query the database for students assigned to this teacher
		// For now, we'll return a placeholder response
		students := []interface{}{
			map[string]interface{}{
				"message": "This would return students assigned to teacher " + userCtx.UserID,
				"note":    "Implementation would require teacher-student relationship data",
			},
		}

		return c.JSON(http.StatusOK, students)
	}
}

// GetCompanyUsers demonstrates HR accessing company users
func (uc *controller) GetCompanyUsers() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user context and check HR permissions
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Check if user has HR permissions
		if !auth.IsHumanResources(userCtx.Roles) {
			return errors.New(errors.Controller, "HR access required", errors.Forbidden, nil)
		}

		// Get company IDs that this HR user can access
		companyIDs := auth.GetHRCompanyIDs(userCtx.Roles)

		// In a real implementation, this would query users by company IDs
		// For now, we'll return a placeholder response
		result := map[string]interface{}{
			"message":    "This would return users from companies",
			"companyIDs": companyIDs,
			"note":       "Implementation would require company-user relationship data",
		}

		return c.JSON(http.StatusOK, result)
	}
}

// Example of how to register these new endpoints with proper middleware
func (uc *controller) RegisterRBACRoutes(group *echo.Group) {
	// Apply middleware chain: AuthGuard -> UserContext -> RoleGuard
	authChain := middlewares.AuthGuard()
	userContextChain := middlewares.UserContextMiddleware()

	// Self-service endpoints (users can access their own data)
	selfOrAdmin := middlewares.SelfOrAdmin()
	group.GET("/me", uc.Find(), authChain, userContextChain)
	group.PATCH("/me", uc.Patch(), authChain, userContextChain)
	group.DELETE("/me", uc.Delete(), authChain, userContextChain)

	// Role-based endpoints
	group.GET("/users/:id", uc.FindWithRoleFilter(), authChain, userContextChain, selfOrAdmin)
	group.PUT("/users/:id", uc.UpdateWithRoleCheck(), authChain, userContextChain, selfOrAdmin)
	group.DELETE("/users/:id", uc.DeleteWithRoleCheck(), authChain, userContextChain, selfOrAdmin)

	// Admin-only endpoints
	adminOnly := middlewares.AdminOnly()
	group.GET("/users", uc.FindAllWithRoleFilter(), authChain, userContextChain, adminOnly)
	group.GET("/users/by-role", uc.GetUsersByRole(), authChain, userContextChain, adminOnly)

	// Teacher endpoints
	teacherOrAbove := middlewares.TeacherOrAbove()
	group.GET("/my-students", uc.GetMyStudents(), authChain, userContextChain, teacherOrAbove)

	// HR endpoints
	hrOnly := middlewares.HROnly()
	group.GET("/company-users", uc.GetCompanyUsers(), authChain, userContextChain, hrOnly)
}
