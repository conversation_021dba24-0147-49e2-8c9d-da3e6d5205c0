name: CI/CD for <PERSON><PERSON><PERSON> Backend to AWS EC2 (ARM64 with SSM and Polling)

on:
  push:
    branches:
      - production # Trigger deployment on pushes to the production branch

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest # Use the latest Ubuntu runner

    steps:
      # --------------------------------------------------
      # Build Steps
      # --------------------------------------------------
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go environment
        uses: actions/setup-go@v5
        with:
          go-version: '1.23.0' # Specify your Go version

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Run tests
        run: go test ./...

      - name: Build ARM64 Binary
        run: |
          echo "Building Go application for linux/arm64..."
          GOOS=linux GOARCH=arm64 go build -o dinbora cmd/dinbora/main.go
          ls -l dinbora

      # --------------------------------------------------
      # AWS Configuration & Deployment Steps
      # --------------------------------------------------
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Deploy to EC2 via SSM with Polling
        env:
          AWS_REGION: ${{ secrets.AWS_REGION }} # Make region available for polling
          S3_BUCKET_NAME: ${{ secrets.S3_BUCKET_NAME }}
        run: |
          echo "Starting deployment to region: ${AWS_REGION}..."
          # --- Timestamp and Paths ---
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          echo "Generated timestamp: ${TIMESTAMP}"
          BINARY_S3_PATH="dinbora-${TIMESTAMP}"
          MIGRATION_S3_PATH="migration-${TIMESTAMP}" # This creates migration-YYYYMMDDHHMMSS/ prefix implicitly via sync
          REFRESH_SCRIPT_S3_PATH="refresh-env-${TIMESTAMP}.sh"
          DEPLOY_SCRIPT_S3_PATH="deploy-app-${TIMESTAMP}.sh"

          # --- Upload Artifacts to S3 ---
          echo "Uploading artifacts to S3 bucket: ${S3_BUCKET_NAME}"
          aws s3 cp dinbora "s3://${S3_BUCKET_NAME}/${BINARY_S3_PATH}"
          # Note: sync creates the target directory if it doesn't exist
          aws s3 sync migration "s3://${S3_BUCKET_NAME}/${MIGRATION_S3_PATH}/" # Add trailing slash for clarity with sync
          aws s3 cp .github/workflows/refresh-env.sh "s3://${S3_BUCKET_NAME}/${REFRESH_SCRIPT_S3_PATH}"
          aws s3 cp .github/workflows/deploy-aws.sh "s3://${S3_BUCKET_NAME}/${DEPLOY_SCRIPT_S3_PATH}"
          echo "Uploading 'latest' versions..."
          aws s3 cp dinbora "s3://${S3_BUCKET_NAME}/dinbora-latest"
          aws s3 sync migration "s3://${S3_BUCKET_NAME}/migration-latest/" --delete # Add trailing slash
          # Create latest versions for scripts too if needed (add these lines if you use them)
          # aws s3 cp .github/workflows/refresh-env.sh "s3://${S3_BUCKET_NAME}/refresh-env-latest.sh"
          # aws s3 cp .github/workflows/deploy-aws.sh "s3://${S3_BUCKET_NAME}/deploy-app-latest.sh"

          # --- Get Target Instance IDs ---
          echo "Fetching instance IDs from ASG: dinbora-backend-asg"
          INSTANCE_IDS=$(aws autoscaling describe-auto-scaling-groups \
            --auto-scaling-group-names dinbora-backend-asg \
            --query "AutoScalingGroups[0].Instances[?LifecycleState=='InService'].InstanceId" \
            --output text)

          if [ -z "$INSTANCE_IDS" ]; then
            echo "[ERROR] No 'InService' instances found in the Auto Scaling Group 'dinbora-backend-asg'. Deployment cannot proceed."
            exit 1
          else
            echo "Target instances: ${INSTANCE_IDS}"
          fi

          # --- Helper Function for SSM Polling ---
          # (Define function within the run block's shell scope)
          poll_ssm_command() {
            local command_id=$1
            local instance_id=$2
            local step_name=$3
            echo "Polling SSM Command ID: ${command_id} on instance ${instance_id} for step: ${step_name}"
            TIMEOUT=300 # 5 minutes total timeout
            INTERVAL=10 # Check every 10 seconds
            ELAPSED=0
            while [ $ELAPSED -lt $TIMEOUT ]; do
              # Get invocation status for the specific instance
              STATUS=$(aws ssm list-command-invocations \
                --command-id "${command_id}" \
                --instance-id "${instance_id}" \
                --details \
                --query "CommandInvocations[0].Status" \
                --output text)

              echo "Instance ${instance_id} status for ${step_name}: ${STATUS} (${ELAPSED}s elapsed)"

              if [[ "$STATUS" == "Success" ]]; then
                echo "SSM command '${step_name}' succeeded on ${instance_id}."
                return 0 # Success
              elif [[ "$STATUS" == "Failed" || "$STATUS" == "Cancelled" || "$STATUS" == "TimedOut" || "$STATUS" == "Undeliverable" || "$STATUS" == "Terminated" ]]; then
                echo "[ERROR] SSM command '${step_name}' failed on ${instance_id} with status: ${STATUS}."
                # Get error output if available (only shows first ~2500 chars)
                echo "--- Start SSM Error Output ---"
                aws ssm list-command-invocations \
                  --command-id "${command_id}" \
                  --instance-id "${instance_id}" \
                  --details \
                  --query "CommandInvocations[0].CommandPlugins[?Name=='aws:runShellScript'].Output" \
                  --output text || echo "[WARN] Could not retrieve error output."
                 echo "--- End SSM Error Output ---"
                return 1 # Failure
              elif [[ "$STATUS" == "Pending" || "$STATUS" == "InProgress" || "$STATUS" == "Cancelling" ]]; then
                # Still running, wait...
                sleep $INTERVAL
                ELAPSED=$((ELAPSED + INTERVAL))
              else
                 echo "[WARN] Unexpected SSM status '${STATUS}' for command ${command_id} on instance ${instance_id}. Continuing poll."
                 sleep $INTERVAL
                 ELAPSED=$((ELAPSED + INTERVAL))
              fi


              if [ $ELAPSED -ge $TIMEOUT ]; then
                echo "[ERROR] Timeout waiting for SSM command '${step_name}' (${command_id}) on instance ${instance_id}."
                return 1 # Failure due to timeout
              fi
            done
          }


          # --- Deploy to Each Instance via SSM ---
          DEPLOY_FAILED=0 # Flag to track if any instance deployment failed
          for INSTANCE_ID in $INSTANCE_IDS; do
            echo "----------------------------------------------------"
            echo "--- Starting deployment cycle for instance: ${INSTANCE_ID} ---"
            # Define temporary locations on the target instance (unique per deployment)
            TMP_DIR="/home/<USER>/tmp_deploy_${TIMESTAMP}"
            TMP_MIGRATION_DIR="${TMP_DIR}/migration" # Specific migration sub-directory
            TMP_REFRESH_SCRIPT_LOCATION="$TMP_DIR/refresh-env.sh"
            TMP_DEPLOY_SCRIPT_LOCATION="$TMP_DIR/deploy-aws.sh"

            # --- SSM Command Step 1: Download All Assets ---
            echo "Sending SSM command (Download Assets) to ${INSTANCE_ID}"
            SSM_DOWNLOAD_COMMAND_ID=$(aws ssm send-command \
              --document-name "AWS-RunShellScript" \
              --targets "Key=instanceids,Values=${INSTANCE_ID}" \
              --parameters "{\"commands\":[
                \"echo '[INFO] Creating temporary deployment directory: ${TMP_DIR}'\",
                \"rm -rf ${TMP_DIR}\",
                \"mkdir -p ${TMP_MIGRATION_DIR}\",
                \"echo '[INFO] Downloading binary...'\",
                \"aws s3 cp s3://${S3_BUCKET_NAME}/${BINARY_S3_PATH} ${TMP_DIR}/dinbora\",
                \"echo '[INFO] Downloading migrations...'\",
                \"aws s3 sync s3://${S3_BUCKET_NAME}/${MIGRATION_S3_PATH}/ ${TMP_MIGRATION_DIR}/\",
                \"echo '[INFO] Downloading environment refresh script...'\",
                \"aws s3 cp s3://${S3_BUCKET_NAME}/${REFRESH_SCRIPT_S3_PATH} ${TMP_REFRESH_SCRIPT_LOCATION}\",
                \"chmod +x ${TMP_REFRESH_SCRIPT_LOCATION}\",
                \"echo '[INFO] Downloading main deployment script...'\",
                \"aws s3 cp s3://${S3_BUCKET_NAME}/${DEPLOY_SCRIPT_S3_PATH} ${TMP_DEPLOY_SCRIPT_LOCATION}\",
                \"chmod +x ${TMP_DEPLOY_SCRIPT_LOCATION}\"
              ]}" \
              --comment "Download assets ${TIMESTAMP} to ${INSTANCE_ID}" \
              --query "Command.CommandId" \
              --output text)

            # Poll for Download Assets command completion
            if ! poll_ssm_command "${SSM_DOWNLOAD_COMMAND_ID}" "${INSTANCE_ID}" "Download Assets"; then
              DEPLOY_FAILED=1 # Mark deployment as failed for this instance
              echo "[ERROR] Download Assets step failed for instance ${INSTANCE_ID}. Skipping further steps for this instance."
              continue # Skip to the next instance
            fi

            # --- SSM Command Step 2: Execute Deployment Script ---
            echo "Sending SSM command (Execute Deployment Script) to ${INSTANCE_ID}"
            SSM_EXECUTE_COMMAND_ID=$(aws ssm send-command \
              --document-name "AWS-RunShellScript" \
              --targets "Key=instanceids,Values=${INSTANCE_ID}" \
              --parameters "{\"commands\":[
                  \"echo '[INFO] Executing main deployment script: ${TMP_DEPLOY_SCRIPT_LOCATION} with arg ${TIMESTAMP}'\",
                  \"sudo ${TMP_DEPLOY_SCRIPT_LOCATION} ${TIMESTAMP}\"
              ]}" \
              --comment "Execute deployment script ${TIMESTAMP} on ${INSTANCE_ID}" \
              --query "Command.CommandId" \
              --output text)

            # Poll for Execute Deployment Script command completion
            if ! poll_ssm_command "${SSM_EXECUTE_COMMAND_ID}" "${INSTANCE_ID}" "Execute Deployment Script"; then
               DEPLOY_FAILED=1 # Mark deployment as failed for this instance
               echo "[ERROR] Execute Deployment Script step failed for instance ${INSTANCE_ID}."
               # Don't 'continue' here as we might still want to clean up S3 later
            fi

            echo "--- Completed deployment cycle for instance: ${INSTANCE_ID} ---"
            echo "----------------------------------------------------"
          done

          # --- Check Overall Deployment Status ---
          if [ $DEPLOY_FAILED -ne 0 ]; then
             echo "[ERROR] Deployment failed on one or more instances. Check logs above."
             # Decide if you want to exit 1 here or still proceed with cleanup
             # exit 1 # Fail the overall GitHub Actions job - uncomment if cleanup should NOT run on failure
          else
             echo "[SUCCESS] Deployment commands completed successfully on all targeted instances."
          fi

          # --- S3 Cleanup ---
          echo "--- Starting S3 Cleanup ---"

          # Clean up older binaries, keeping the latest three timestamped + 'latest'
          echo "Fetching list of binaries for cleanup..."
          # List files at root, filter for ' dinbora-', exclude latest, sort, keep all but last 3, extract filename
          OLD_BINARIES=$(aws s3 ls "s3://${S3_BUCKET_NAME}/" | grep ' dinbora-' | awk '{print $4}' | grep -v '^dinbora-latest$' | sort | head -n -3)
          if [ -n "$OLD_BINARIES" ]; then # Check if variable is not empty
            echo "Binaries marked for deletion:"
            printf '%s\n' "$OLD_BINARIES" # Use printf for safer printing
            echo "$OLD_BINARIES" | while IFS= read -r BINARY_TO_DELETE; do
              if [[ -n "$BINARY_TO_DELETE" && "$BINARY_TO_DELETE" != "dinbora-latest" ]]; then
                echo "Deleting binary: s3://${S3_BUCKET_NAME}/${BINARY_TO_DELETE}"
                # aws s3 rm "s3://${S3_BUCKET_NAME}/${BINARY_TO_DELETE}" --dryrun # Test first!
                aws s3 rm "s3://${S3_BUCKET_NAME}/${BINARY_TO_DELETE}"
              fi
            done
          else
            echo "No old binaries found to delete."
          fi

          # --- CORRECTED: Clean up older migration folders, keeping the latest three timestamped + 'latest' ---
          echo "Fetching list of migration folders for cleanup..."
          # 1. List all objects recursively
          # 2. Filter lines where the object key (4th field) starts with 'migration-'
          # 3. Extract the object key
          # 4. Extract only the part *before* the first slash (the top-level folder name) using sed
          # 5. Get unique folder names
          # 6. Exclude 'migration-latest'
          # 7. Sort the remaining timestamped folder names
          # 8. Select all but the last 3 (the oldest ones)
          OLD_MIGRATION_FOLDERS=$(aws s3 ls "s3://${S3_BUCKET_NAME}/" --recursive | \
              awk '$4 ~ /^migration-/ {print $4}' | \
              sed 's|/.*||' | \
              sort -u | \
              grep -v '^migration-latest$' | \
              sort | \
              head -n -3)

          if [ -n "$OLD_MIGRATION_FOLDERS" ]; then # Check if variable is not empty
            echo "Migration folders marked for deletion:"
            printf '%s\n' "$OLD_MIGRATION_FOLDERS" # Use printf for safer printing

            echo "$OLD_MIGRATION_FOLDERS" | while IFS= read -r FOLDER_NAME_TO_DELETE; do
               # Double check it's not empty and not migration-latest
              if [[ -n "$FOLDER_NAME_TO_DELETE" && "$FOLDER_NAME_TO_DELETE" != "migration-latest" ]]; then
                 # IMPORTANT: Append the trailing slash '/' for recursive deletion of the folder prefix
                 # *** REMOVED 'local' FROM THE NEXT LINE ***
                 S3_FOLDER_PATH="s3://${S3_BUCKET_NAME}/${FOLDER_NAME_TO_DELETE}/"
                 echo "Deleting migration folder: ${S3_FOLDER_PATH}"
                 # aws s3 rm --recursive "${S3_FOLDER_PATH}" --dryrun # Test first!
                 aws s3 rm --recursive "${S3_FOLDER_PATH}"
              else
                 echo "Skipping potentially invalid or protected folder name: [$FOLDER_NAME_TO_DELETE]"
              fi
            done
          else
            echo "No old migration folders found to delete."
          fi
          # --- END CORRECTION ---


          # Clean up older refresh scripts, keeping the latest three timestamped (+ assuming a refresh-env-latest.sh exists)
          echo "Fetching list of refresh scripts for cleanup..."
          # List files at root, filter for ' refresh-env-', exclude latest, sort, keep all but last 3, extract filename
          # NOTE: This assumes you *have* a refresh-env-latest.sh. If not, remove the grep -v part.
          OLD_REFRESH_SCRIPTS=$(aws s3 ls "s3://${S3_BUCKET_NAME}/" | grep ' refresh-env-' | awk '{print $4}' | grep -v '^refresh-env-latest\.sh$' | sort | head -n -3)
          if [ -n "$OLD_REFRESH_SCRIPTS" ]; then
             echo "Refresh scripts marked for deletion:"; printf '%s\n' "$OLD_REFRESH_SCRIPTS"
             echo "$OLD_REFRESH_SCRIPTS" | while IFS= read -r SCRIPT_TO_DELETE; do
                if [[ -n "$SCRIPT_TO_DELETE" && "$SCRIPT_TO_DELETE" != "refresh-env-latest.sh" ]]; then
                   echo "Deleting refresh script: s3://${S3_BUCKET_NAME}/$SCRIPT_TO_DELETE"
                   aws s3 rm "s3://${S3_BUCKET_NAME}/$SCRIPT_TO_DELETE"
                fi
             done
          else
             echo "No old refresh scripts found to delete."
          fi

          # Clean up older deploy scripts, keeping the latest three timestamped (+ assuming a deploy-app-latest.sh exists)
          echo "Fetching list of deploy scripts for cleanup..."
          # List files at root, filter for ' deploy-app-', exclude latest, sort, keep all but last 3, extract filename
          # NOTE: This assumes you *have* a deploy-app-latest.sh. If not, remove the grep -v part.
          OLD_DEPLOY_SCRIPTS=$(aws s3 ls "s3://${S3_BUCKET_NAME}/" | grep ' deploy-app-' | awk '{print $4}' | grep -v '^deploy-app-latest\.sh$' | sort | head -n -3)
          if [ -n "$OLD_DEPLOY_SCRIPTS" ]; then
             echo "Deploy scripts marked for deletion:"; printf '%s\n' "$OLD_DEPLOY_SCRIPTS"
             echo "$OLD_DEPLOY_SCRIPTS" | while IFS= read -r SCRIPT_TO_DELETE; do
                 if [[ -n "$SCRIPT_TO_DELETE" && "$SCRIPT_TO_DELETE" != "deploy-app-latest.sh" ]]; then
                    echo "Deleting deploy script: s3://${S3_BUCKET_NAME}/$SCRIPT_TO_DELETE"
                    aws s3 rm "s3://${S3_BUCKET_NAME}/$SCRIPT_TO_DELETE"
                 fi
             done
          else
             echo "No old deploy scripts found to delete."
          fi

          # --- Optional: Verify -latest versions exist ---
          echo "--- Verifying existence of -latest versions ---"
          # Check binary
          aws s3api head-object --bucket "${S3_BUCKET_NAME}" --key "dinbora-latest" > /dev/null 2>&1 || echo "[WARN] Verification failed: s3://${S3_BUCKET_NAME}/dinbora-latest not found or inaccessible."
          # Check migration prefix (check if ls command succeeds)
          aws s3 ls "s3://${S3_BUCKET_NAME}/migration-latest/" > /dev/null 2>&1 || echo "[WARN] Verification failed: s3://${S3_BUCKET_NAME}/migration-latest/ not found or inaccessible."
          # Check refresh script (assuming it exists - uncomment upload step if you want this)
          aws s3api head-object --bucket "${S3_BUCKET_NAME}" --key "refresh-env-latest.sh" > /dev/null 2>&1 || echo "[WARN] Verification failed: s3://${S3_BUCKET_NAME}/refresh-env-latest.sh not found or inaccessible. Did you upload it?"
          # Check deploy script (assuming it exists - uncomment upload step if you want this)
          aws s3api head-object --bucket "${S3_BUCKET_NAME}" --key "deploy-app-latest.sh" > /dev/null 2>&1 || echo "[WARN] Verification failed: s3://${S3_BUCKET_NAME}/deploy-app-latest.sh not found or inaccessible. Did you upload it?"

          echo "--- S3 Cleanup Finished ---"
          echo "Deployment script completed successfully."