package user

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/service/s3"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// V2
	// CRUD
	Find() echo.HandlerFunc
	FindByEmail() echo.HandlerFunc
	Patch() echo.HandlerFunc
	Delete() echo.HandlerFunc

	// Legacy operations
	LegacyFind() echo.HandlerFunc
	LegacyUpdate() echo.HandlerFunc
	LegacyDelete() echo.HandlerFunc

	// Utility
	FindCard() echo.HandlerFunc

	// Profile Picture
	UploadPhoto() echo.HandlerFunc

	// HR-specific methods
	FindEmployeesByHR() echo.HandlerFunc
	FindTopEngagers() echo.HandlerFunc
	FindJourneyProgression() echo.HandlerFunc
	FindDreamsProgression() echo.HandlerFunc
	FindFinancialProfileDistribution() echo.HandlerFunc
	FindAgeRanges() echo.HandlerFunc
	FindFinancialSituations() echo.HandlerFunc
	FindFinancialProfileEvolution() echo.HandlerFunc
	FindPersonalInterests() echo.HandlerFunc
	FindFinancialGoals() echo.HandlerFunc
}

type controller struct {
	Service   user.Service
	S3Service s3.Service
}

func New(service user.Service, s3Service s3.Service) Controller {
	return &controller{
		Service:   service,
		S3Service: s3Service,
	}
}

func (uc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	userGroup := legacyGroup.Group("user/", middlewares.AuthGuard())
	usersGroup := currentGroup.Group("/users", middlewares.AuthGuard())

	// V2
	// CRUD
	// Group for self-service endpoints under "/users/me"
	meGroup := usersGroup.Group("/me")
	meGroup.GET("", uc.Find())
	meGroup.PATCH("", uc.Patch())
	meGroup.DELETE("", uc.Delete())

	// Card operations under "/users/me/cards"
	cardsGroup := meGroup.Group("/cards")
	cardsGroup.GET("", uc.FindCard())

	// Photo operations under "/users/me/photo"
	photoGroup := meGroup.Group("/photos")
	photoGroup.POST("", uc.UploadPhoto())

	// HR-specific endpoints with proper middleware chain
	hrGroup := usersGroup.Group("/hr")
	hrGroup.GET("/employees", uc.FindEmployeesByHR(), middlewares.UserContextMiddleware(), middlewares.HROnly())
	hrGroup.GET("/top-engagers", uc.FindTopEngagers(), middlewares.UserContextMiddleware(), middlewares.HROnly())
	hrGroup.GET("/journey-progression", uc.FindJourneyProgression(), middlewares.UserContextMiddleware(), middlewares.HROnly())
	hrGroup.GET("/dreams-progression", uc.FindDreamsProgression(), middlewares.UserContextMiddleware(), middlewares.HROnly())
	hrGroup.GET("/financialprofile-distribution", uc.FindFinancialProfileDistribution(), middlewares.UserContextMiddleware(), middlewares.HROnly())
	hrGroup.GET("/age-ranges", uc.FindAgeRanges(), middlewares.UserContextMiddleware(), middlewares.HROnly())
	hrGroup.GET("/financial-situations", uc.FindFinancialSituations(), middlewares.UserContextMiddleware(), middlewares.HROnly())
	hrGroup.GET("/financialprofile-evolution", uc.FindFinancialProfileEvolution(), middlewares.UserContextMiddleware(), middlewares.HROnly())
	hrGroup.GET("/personal-interests", uc.FindPersonalInterests(), middlewares.UserContextMiddleware(), middlewares.HROnly())
	hrGroup.GET("/financial-goals", uc.FindFinancialGoals(), middlewares.UserContextMiddleware(), middlewares.HROnly())

	// Legacy operations
	userGroup.GET(":id/", uc.LegacyFind(), middlewares.AuthSameUserGuard(uc.Service))
	userGroup.POST("findByEmail/", uc.FindByEmail())
	userGroup.PUT(":id/", uc.LegacyUpdate(), middlewares.AuthSameUserGuard(uc.Service))
	userGroup.POST("delete/:id/", uc.LegacyDelete(), middlewares.AuthSameUserGuard(uc.Service))

	// Utility
	userGroup.POST("getCard/", uc.FindCard())
}
