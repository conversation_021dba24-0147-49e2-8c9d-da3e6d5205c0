package trail

import (
	"context"
	"fmt"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
)

// Extra Trail CRUD

func (s *service) CreateExtraTrail(ctx context.Context, trail *content.Trail) error {
	identifierKey := trailIdentifierKey(trail.Identifier)
	if _, found := s.Cache.Get(ctx, identifierKey); found {
		return errors.OldError("extra trail content already exists (cache hit)", errors.Conflict, nil)
	}

	foundContent, err := s.Repository.FindExtraTrailByIdentifier(ctx, trail.Identifier, "")
	if err == nil && foundContent != nil {
		foundContent.ID = foundContent.ObjectID.Hex()
		idKey := trailIDKey(foundContent.ID)
		_ = s.Cache.Set(ctx, identifierKey, foundContent, 0)
		_ = s.Cache.Set(ctx, idKey, foundContent, 0)
		return errors.OldError("extra trail content already exists (db hit)", errors.Conflict, err)
	}

	if err = s.Repository.CreateExtraTrail(ctx, trail); err != nil {
		return err
	}

	err = s.Cache.Delete(ctx, trailAllKey)
	if err != nil {
		log.Printf("Error invalidating FindAll cache after create: %v", err)
	} else {
		log.Println("Trail FindAll cache invalidated due to Create.")
	}

	// Invalidate extra trails cache patterns (user-specific caches will be invalidated on access)
	s.invalidateExtraTrailCachePatterns(ctx)

	return nil
}

func (s *service) FindExtraTrail(ctx context.Context, id string, userClassification string) (*content.Trail, error) {
	cacheKey := fmt.Sprintf("trail:extra:id:%s:class:%s", id, userClassification)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if trail, ok := cached.(*content.Trail); ok {
			log.Printf("Cache hit for FindExtraTrail for trail: %s, classification: %s", id, userClassification)
			return trail, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion failed", cacheKey)
	}

	log.Printf("Cache miss for FindExtraTrail for trail: %s, classification: %s. Fetching from database...", id, userClassification)
	trail, err := s.Repository.FindExtraTrail(ctx, id, userClassification)
	if err != nil {
		return nil, err
	}

	trail.ID = trail.ObjectID.Hex()

	errSet := s.Cache.Set(ctx, cacheKey, trail, 0)
	if errSet != nil {
		log.Printf("Error populating FindExtraTrail cache: %v", errSet)
	} else {
		log.Printf("FindExtraTrail Cache populated for trail: %s, classification: %s", id, userClassification)
	}

	return trail, nil
}

func (s *service) FindAllExtraTrails(ctx context.Context, userClassification string) ([]*content.Trail, error) {
	cacheKey := fmt.Sprintf("trail:extra:all:class:%s", userClassification)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if trails, ok := cached.([]*content.Trail); ok {
			log.Printf("Cache hit for FindAllExtraTrails for classification: %s", userClassification)
			return trails, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion failed", cacheKey)
	}

	log.Printf("Cache miss for FindAllExtraTrails for classification: %s. Fetching from database...", userClassification)
	trails, err := s.Repository.FindAllExtraTrails(ctx, userClassification)
	if err != nil {
		return nil, err
	}

	// Process trails to set IDs
	processedTrails := make([]*content.Trail, len(trails))
	for i, trail := range trails {
		trail.ID = trail.ObjectID.Hex()
		trailCopy := *trail
		processedTrails[i] = &trailCopy
	}

	errSet := s.Cache.Set(ctx, cacheKey, processedTrails, 0)
	if errSet != nil {
		log.Printf("Error populating FindAllExtraTrails cache: %v", errSet)
	} else {
		log.Printf("FindAllExtraTrails Cache populated with %d trails for classification: %s", len(processedTrails), userClassification)
	}

	return processedTrails, nil
}

func (s *service) FindExtraTrailByIdentifier(ctx context.Context, identifier string, userClassification string) (*content.Trail, error) {
	cacheKey := fmt.Sprintf("trail:extra:identifier:%s:class:%s", identifier, userClassification)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if trail, ok := cached.(*content.Trail); ok {
			log.Printf("Cache hit for FindExtraTrailByIdentifier for identifier: %s, classification: %s", identifier, userClassification)
			return trail, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion failed", cacheKey)
	}

	log.Printf("Cache miss for FindExtraTrailByIdentifier for identifier: %s, classification: %s. Fetching from database...", identifier, userClassification)
	trail, err := s.Repository.FindExtraTrailByIdentifier(ctx, identifier, userClassification)
	if err != nil {
		return nil, err
	}

	trail.ID = trail.ObjectID.Hex()

	errSet := s.Cache.Set(ctx, cacheKey, trail, 0)
	if errSet != nil {
		log.Printf("Error populating FindExtraTrailByIdentifier cache: %v", errSet)
	} else {
		log.Printf("FindExtraTrailByIdentifier Cache populated for identifier: %s, classification: %s", identifier, userClassification)
	}

	return trail, nil
}

func (s *service) UpdateExtraTrail(ctx context.Context, trail *content.Trail) error {
	err := s.Repository.UpdateExtraTrail(ctx, trail)
	if err != nil {
		return err
	}

	hexID := trail.ObjectID.Hex()
	s.invalidateAllTrailCache(ctx, trail.Identifier, hexID)

	// Also invalidate extra-specific caches
	s.invalidateExtraTrailCachePatterns(ctx)

	return nil
}

func (s *service) DeleteExtraTrail(ctx context.Context, id string) error {
	var identifierToInvalidate string

	// Try to get identifier from a potential cached trail (any user cache)
	// Since this is a delete operation, we don't have user context, so we'll fetch from DB
	log.Printf("Extra trail ID %s delete requested, fetching for invalidation info.", id)

	// We'll try with empty user context to see if we can get the trail info
	// This might fail due to access control, but we'll try anyway
	dbTrail, err := s.Repository.FindExtraTrail(ctx, id, "")
	if err == nil && dbTrail != nil {
		identifierToInvalidate = dbTrail.Identifier
	}

	err = s.Repository.DeleteExtraTrail(ctx, id)
	if err != nil {
		return err
	}

	if identifierToInvalidate == "" {
		log.Printf("Warning: Could not determine identifier for deleted extra trail ID %s for full cache invalidation.", id)
	}
	s.invalidateAllTrailCache(ctx, identifierToInvalidate, id)
	s.invalidateExtraTrailCachePatterns(ctx)

	return nil
}

func (s *service) FindExtraTrailCardData(ctx context.Context, id string, userClassification string) (*content.TrailCard, error) {
	cacheKey := fmt.Sprintf("trail:extra:card:id:%s:class:%s", id, userClassification)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if cardData, ok := cached.(*content.TrailCard); ok {
			log.Printf("Cache hit for FindExtraTrailCardData for trail: %s, classification: %s", id, userClassification)
			return cardData, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion failed", cacheKey)
	}

	log.Printf("Cache miss for FindExtraTrailCardData for trail: %s, classification: %s. Fetching from database...", id, userClassification)
	cardData, err := s.Repository.FindExtraTrailCardData(ctx, id, userClassification)
	if err != nil {
		return nil, err
	}

	cardData.ID = cardData.ObjectID.Hex()

	errSet := s.Cache.Set(ctx, cacheKey, cardData, 0)
	if errSet != nil {
		log.Printf("Error populating FindExtraTrailCardData cache: %v", errSet)
	} else {
		log.Printf("FindExtraTrailCardData Cache populated for trail: %s, classification: %s", id, userClassification)
	}

	return cardData, nil
}

func (s *service) FindAllExtraTrailsCardData(ctx context.Context, userClassification string) ([]*content.TrailCard, error) {
	cacheKey := fmt.Sprintf("trail:extra:cards:all:class:%s", userClassification)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if cardDataList, ok := cached.([]*content.TrailCard); ok {
			log.Printf("Cache hit for FindAllExtraTrailsCardData for classification: %s", userClassification)
			return cardDataList, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion failed", cacheKey)
	}

	log.Printf("Cache miss for FindAllExtraTrailsCardData for classification: %s. Fetching from database...", userClassification)
	cardDataList, err := s.Repository.FindAllExtraTrailsCardData(ctx, userClassification)
	if err != nil {
		return nil, err
	}

	// Process card data to set IDs
	for _, cardData := range cardDataList {
		cardData.ID = cardData.ObjectID.Hex()
	}

	errSet := s.Cache.Set(ctx, cacheKey, cardDataList, 0)
	if errSet != nil {
		log.Printf("Error populating FindAllExtraTrailsCardData cache: %v", errSet)
	} else {
		log.Printf("FindAllExtraTrailsCardData Cache populated with %d trail cards for classification: %s", len(cardDataList), userClassification)
	}

	return cardDataList, nil
}

// Extra Trail Lesson CRUD

func (s *service) CreateExtraLesson(ctx context.Context, trailID string, userClassification string, lesson *content.Lesson) error {
	trail, err := s.FindExtraTrail(ctx, trailID, userClassification)
	if err != nil {
		return err
	}

	trail.Lessons = append(trail.Lessons, lesson)

	return s.UpdateExtraTrail(ctx, trail)
}

func (s *service) FindExtraLesson(ctx context.Context, trailID string, userClassification string, lessonID string) (*content.Lesson, error) {
	trail, err := s.FindExtraTrail(ctx, trailID, userClassification)
	if err != nil {
		return nil, err
	}

	for _, lesson := range trail.Lessons {
		if lesson != nil && lesson.Identifier == lessonID {
			return lesson, nil
		}
	}

	return nil, errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
}

func (s *service) UpdateExtraLesson(ctx context.Context, trailID string, userClassification string, lessonID string, lesson *content.Lesson) error {
	trail, err := s.FindExtraTrail(ctx, trailID, userClassification)
	if err != nil {
		return err
	}

	found := false
	for i, l := range trail.Lessons {
		if l != nil && l.Identifier == lessonID {
			trail.Lessons[i] = lesson
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
	}

	return s.UpdateExtraTrail(ctx, trail)
}

func (s *service) DeleteExtraLesson(ctx context.Context, trailID string, userClassification string, lessonID string) error {
	trail, err := s.FindExtraTrail(ctx, trailID, userClassification)
	if err != nil {
		return err
	}

	found := false
	lessonIndex := -1
	for i, l := range trail.Lessons {
		if l != nil && l.Identifier == lessonID {
			lessonIndex = i
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "lesson not found", errors.NotFound, nil)
	}

	trail.Lessons = append(trail.Lessons[:lessonIndex], trail.Lessons[lessonIndex+1:]...)

	return s.UpdateExtraTrail(ctx, trail)
}

// Helper function to invalidate extra trail cache patterns
func (s *service) invalidateExtraTrailCachePatterns(ctx context.Context) {
	// Invalidate all extra trails cache
	errAll := s.Cache.Delete(ctx, "trail:extra:all")
	if errAll != nil {
		log.Printf("Error invalidating all extra trails cache: %v", errAll)
	} else {
		log.Println("All extra trails cache invalidated.")
	}

	// Invalidate all extra trail cards cache
	errCards := s.Cache.Delete(ctx, "trail:extra:cards:all")
	if errCards != nil {
		log.Printf("Error invalidating all extra trail cards cache: %v", errCards)
	} else {
		log.Println("All extra trail cards cache invalidated.")
	}
}
