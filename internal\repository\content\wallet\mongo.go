package wallet

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	return &mongoDB{collection: db.Collection(repository.WALLETS_COLLECTION)}
}

func (m mongoDB) Create(ctx context.Context, wallet *content.Wallet) error {
	_, err := m.collection.InsertOne(ctx, wallet)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "wallet already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create wallet", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) Find(ctx context.Context, id string) (*content.Wallet, error) {
	objectId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid ID format", errors.Validation, err)
	}

	var wallet content.Wallet
	if err = m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectId}}).Decode(&wallet); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "wallet not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "find wallet failed", errors.Internal, err)
	}

	return &wallet, nil
}

func (m mongoDB) FindMany(ctx context.Context, ids []primitive.ObjectID) ([]*content.Wallet, error) {
	if len(ids) == 0 {
		return []*content.Wallet{}, nil // Return empty slice if no IDs provided
	}

	filter := bson.M{"_id": bson.M{"$in": ids}}
	cursor, err := m.collection.Find(ctx, filter)
	if err != nil {
		return nil, errors.New(errors.Repository, "find many wallets failed", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var wallets []*content.Wallet
	if err = cursor.All(ctx, &wallets); err != nil {
		return nil, errors.New(errors.Repository, "decode wallets failed during find many", errors.Internal, err)
	}

	return wallets, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*content.Wallet, error) {
	cursor, err := m.collection.Find(ctx, bson.D{}, nil)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query wallets", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var wallets []*content.Wallet
	for cursor.Next(ctx) {
		var wallet content.Wallet
		if err = cursor.Decode(&wallet); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode wallet", errors.Internal, err)
		}
		wallets = append(wallets, &wallet)
	}
	return wallets, nil
}

func (m mongoDB) FindByIdentifier(ctx context.Context, identifier string) (*content.Wallet, error) {
	var wallet content.Wallet
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "identifier", Value: identifier}}).Decode(&wallet); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "wallet not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "find wallet by identifier failed", errors.Internal, err)
	}

	return &wallet, nil
}

func (m mongoDB) Update(ctx context.Context, wallet *content.Wallet) error {
	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: wallet.ObjectID}},
		primitive.M{"$set": wallet})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "wallet update would cause conflict", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update wallet", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "wallet not found for update", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid ID format", errors.Validation, err)
	}

	result, err := m.collection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectID}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete wallet", errors.Internal, err)
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "wallet not found for deletion", errors.NotFound, nil)
	}

	return nil
}
