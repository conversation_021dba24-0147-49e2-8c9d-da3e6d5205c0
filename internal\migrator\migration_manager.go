package migrator

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Migration defines the interface that all migrations must implement
type Migration interface {
	Name() string
	Up(ctx context.Context) error
}

// ChangeLog represents a record of executed migrations
type ChangeLog struct {
	Name     string    `json:"name" bson:"name"`
	Date     time.Time `json:"date" bson:"date"`
	Status   string    `json:"status" bson:"status"`
	ErrorMsg string    `json:"error_msg,omitempty" bson:"error_msg,omitempty"`
}

// MigrationManager handles the execution and tracking of migrations
type MigrationManager interface {
	Run(ctx context.Context) error
	AddMigration(m Migration)
	HasBeenApplied(ctx context.Context, name string) (bool, error)
}

type migrationManager struct {
	db         *mongo.Database
	migrations []Migration
}

// New creates a new MigrationManager instance
func NewMigrationManager(db *mongo.Database) MigrationManager {
	return &migrationManager{
		db:         db,
		migrations: make([]Migration, 0),
	}
}

// AddMigration adds a new migration to be executed
func (m *migrationManager) AddMigration(migration Migration) {
	m.migrations = append(m.migrations, migration)
}

// HasBeenApplied checks if a migration has already been executed successfully
func (m *migrationManager) HasBeenApplied(ctx context.Context, name string) (bool, error) {
	var changelog ChangeLog
	err := m.db.Collection(repository.CHANGELOGS_COLLECTION).FindOne(ctx,
		bson.D{primitive.E{Key: "name", Value: name}, primitive.E{Key: "status", Value: "completed"}},
	).Decode(&changelog)

	if err == mongo.ErrNoDocuments {
		return false, nil
	}
	if err != nil {
		return false, fmt.Errorf("error checking migration status: %w", err)
	}

	return true, nil
}

// logMigration records the execution status of a migration
func (m *migrationManager) logMigration(ctx context.Context, name string, err error) error {
	changelog := ChangeLog{
		Name:   name,
		Date:   time.Now().UTC(),
		Status: "completed",
	}

	if err != nil {
		changelog.Status = "failed"
		changelog.ErrorMsg = err.Error()
	}

	_, insertErr := m.db.Collection(repository.CHANGELOGS_COLLECTION).InsertOne(ctx, changelog)
	return insertErr
}

// Run executes all pending migrations
func (m *migrationManager) Run(ctx context.Context) error {
	log.Println("Starting migrations...")

	for _, migration := range m.migrations {
		name := migration.Name()

		// Check if migration has already been applied
		applied, err := m.HasBeenApplied(ctx, name)
		if err != nil {
			return fmt.Errorf("error checking migration %s: %w", name, err)
		}

		if applied {
			log.Printf("Migration %s has already been applied, skipping\n", name)
			continue
		}

		// Execute migration
		log.Printf("Running migration: %s\n", name)
		err = migration.Up(ctx)

		// Log migration execution
		logErr := m.logMigration(ctx, name, err)
		if logErr != nil {
			return fmt.Errorf("error logging migration %s: %w", name, logErr)
		}

		if err != nil {
			return fmt.Errorf("error executing migration %s: %w", name, err)
		}

		log.Printf("Successfully completed migration: %s\n", name)
	}

	log.Println("Finished running migrations")
	return nil
}
