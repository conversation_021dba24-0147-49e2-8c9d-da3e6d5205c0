// sudo dnf install -y https://dl.k6.io/rpm/repo.rpm
// sudo dnf install k6

// sudo yum update -y
// sudo yum install -y https://dl.k6.io/rpm/repo.rpm
// sudo yum install k6

import http from 'k6/http';
import { sleep, check, fail } from 'k6';
import { Trend } from 'k6/metrics';

// Create a custom metric to track response times
const responseTimeTrend = new Trend('waiting_time');

// --- Configuration ---
const BASE_URL = 'https://api.dinbora.com.br';

// !!! ADAPT THESE TO YOUR ACTUAL API !!!
const LOGIN_ENDPOINT = '/v1/auth/login/'; // Your login endpoint path
const TEST_ENDPOINT = '/v1/content/trail/67f6ddf3181babca8896e73c/'; // A REAL protected endpoint to test
const TOKEN_FIELD = 'Access'; // The FIELD NAME in the login response JSON containing the token

// --- VU Execution Options ---
export const options = {
  stages: [
    // Ramp up relatively quickly to exceed the threshold
    { duration: '1m', target: 100 },  // Ramp to 100 VUs
    { duration: '2m', target: 200 },  // Ramp to 200 VUs
    { duration: '3m', target: 400 },  // Ramp to 400 VUs - SHOULD trigger scaling
    { duration: '5m', target: 400 },  // MAINTAIN load after scaling occurs
    { duration: '1m', target: 0 },    // Ramp down
  ],
  thresholds: { // Keep reasonable thresholds
    'http_req_failed': ['rate<0.05'], // Allow slightly more failures under heavy load
    'http_req_duration': ['p(95)<2000'], // Allow up to 2s p95
    'waiting_time': ['p(95)<2000'],
    'checks': ['rate>0.95'],
  },
};

// --- Setup Function (Runs ONCE before VUs start) ---
export function setup() {
  console.log('Running setup() - Logging in...');

  // Get credentials from environment variables
  const username = '<EMAIL>' // __ENV.K6_LOGIN_USERNAME;
  const password = 'Mudar@123' //__ENV.K6_LOGIN_PASSWORD;

  if (!username || !password) {
    fail('Missing K6_LOGIN_USERNAME or K6_LOGIN_PASSWORD environment variables');
  }

  const loginPayload = JSON.stringify({
    // !!! ADAPT PAYLOAD STRUCTURE TO YOUR LOGIN API !!!
    email: username,
    password: password,
    // email: username, // Use email if your API expects that
  });

  const loginHeaders = {
    'Content-Type': 'application/json',
  };

  // Make the login request (Adapt POST if your method differs)
  const loginRes = http.post(`${BASE_URL}${LOGIN_ENDPOINT}`, loginPayload, { headers: loginHeaders });

  // Check if login was successful
  if (loginRes.status !== 200 && loginRes.status !== 201) { // Adapt status code if needed
      console.error(`Login failed! Status: ${loginRes.status}, Body: ${loginRes.body}`);
      fail(`Login failed with status code ${loginRes.status}`);
  }

  // Extract the token from the response body
  // !!! ADAPT TOKEN_FIELD if your token is named differently !!!
  const authToken = loginRes.json(TOKEN_FIELD);

  if (!authToken) {
      console.error(`Could not extract token from field "${TOKEN_FIELD}". Response: ${loginRes.body}`);
      fail(`Could not extract token from field "${TOKEN_FIELD}" in login response`);
  }

  console.log('Login successful. Token obtained.');

  // Return the token so it's available in the default function (VU code)
  return { authToken: authToken };
}


// --- Default Function (VU Code - Runs in loops by each VU) ---
export default function (data) {
  // 'data' contains the object returned by setup() - { authToken: '...' }

  if (!data.authToken) {
      // This shouldn't happen if setup succeeded, but safety check
      console.error("Auth token not received from setup function!");
      sleep(1);
      return;
  }

  // Prepare headers for authenticated requests
  const authHeaders = {
    'Authorization': `Bearer ${data.authToken}`,
    'Content-Type': 'application/json', // Add other headers if needed
  };

  // Make the request to the PROTECTED endpoint
  // !!! Adapt http.get if your endpoint uses POST, PUT, etc. !!!
  const res = http.get(`${BASE_URL}${TEST_ENDPOINT}`, { headers: authHeaders });

  // Add response time to our custom metric
  responseTimeTrend.add(res.timings.waiting);

  // Check response status (Adapt if your endpoint returns other success codes)
  check(res, {
    'status is 200': (r) => r.status === 200,
    // Add more specific checks if needed
    // 'response body contains expected data': (r) => r.body.includes('some_expected_string'),
  });

  // Handle potential authentication errors explicitly if needed
  if (res.status === 401 || res.status === 403) {
      console.error(`Authentication/Authorization error on ${TEST_ENDPOINT}: Status ${res.status}`);
      // Optionally 'fail' the iteration or just log it
  }

  // Simulate user think time
  sleep(1);
}

// k6 run loadtest.js

// --- Teardown Function (Optional - Runs ONCE after all VUs finish) ---
// export function teardown(data) {
//   console.log('Load test finished.');
//   // You could potentially add a logout call here if needed
// }

// Test original
// --- VU Execution Options ---
// export const options = {
//   stages: [
//     // Start gradually to ensure stability at lower loads
//     { duration: '30s', target: 25 },   // Ramp to 25 VUs over 30s
//     { duration: '1m', target: 25 },   // Stay at 25 VUs
//     { duration: '1m', target: 75 },   // Ramp to 75 VUs over 1m
//     { duration: '2m', target: 75 },   // Stay at 75 VUs
//     { duration: '1m', target: 150 },  // Ramp to 150 VUs over 1m
//     { duration: '3m', target: 150 },  // Stay at 150 VUs (OBSERVE PEAK PERFORMANCE HERE)
//     { duration: '1m', target: 250 },  // Ramp to 250 VUs over 1m
//     { duration: '3m', target: 250 },  // Stay at 250 VUs (PUSHING THE LIMITS)
//     // Add even higher stages if 250 VUs isn't enough to see degradation
//     // { duration: '1m', target: 400 },
//     // { duration: '3m', target: 400 },
//     { duration: '1m', target: 0 },    // Ramp down
//   ],
//   thresholds: {
//     'http_req_failed': ['rate<0.02'],
//     'http_req_duration': ['p(95)<1500'], // Increase allowed latency slightly as load increases
//     'waiting_time': ['p(95)<1500'],    // Increase allowed latency slightly
//     'checks': ['rate>0.95'],           // Allow slightly more check failures under high load
//   },
// };

// Test scalation
// export const options = {
//   stages: [
//     // Ramp up relatively quickly to exceed the threshold
//     { duration: '1m', target: 100 },  // Ramp to 100 VUs
//     { duration: '2m', target: 200 },  // Ramp to 200 VUs
//     { duration: '3m', target: 400 },  // Ramp to 400 VUs - SHOULD trigger scaling
//     { duration: '5m', target: 400 },  // MAINTAIN load after scaling occurs
//     { duration: '1m', target: 0 },    // Ramp down
//   ],
//   thresholds: { // Keep reasonable thresholds
//     'http_req_failed': ['rate<0.05'], // Allow slightly more failures under heavy load
//     'http_req_duration': ['p(95)<2000'], // Allow up to 2s p95
//     'waiting_time': ['p(95)<2000'],
//     'checks': ['rate>0.95'],
//   },
// };